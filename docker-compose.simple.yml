services:
  # Traefik reverse proxy
  traefik:
    image: traefik:v3.0
    container_name: supplement-traefik
    command:
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
      - "--log.level=INFO"
    ports:
      - "9080:80"
      - "9081:8080"  # Traefik dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - supplement-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.traefik.rule=Host(`traefik.pills.localhost`)"
      - "traefik.http.routers.traefik.entrypoints=web"
      - "traefik.http.services.traefik.loadbalancer.server.port=8080"

  # Backend API (Simple FastAPI)
  backend:
    image: python:3.11-slim
    container_name: supplement-backend
    working_dir: /app
    environment:
      - PYTHONUNBUFFERED=1
    volumes:
      - ./backend:/app
    networks:
      - supplement-network
    command: >
      sh -c "
        pip install fastapi uvicorn[standard] &&
        python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload
      "
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.backend.rule=Host(`api.pills.localhost`)"
      - "traefik.http.routers.backend.entrypoints=web"
      - "traefik.http.services.backend.loadbalancer.server.port=8000"
      - "traefik.http.routers.backend.middlewares=cors-headers"
      - "traefik.http.middlewares.cors-headers.headers.accesscontrolallowmethods=GET,OPTIONS,PUT,POST,DELETE,PATCH"
      - "traefik.http.middlewares.cors-headers.headers.accesscontrolallowheaders=*"
      - "traefik.http.middlewares.cors-headers.headers.accesscontrolalloworiginlist=*"
      - "traefik.http.middlewares.cors-headers.headers.accesscontrolmaxage=100"
      - "traefik.http.middlewares.cors-headers.headers.addvaryheader=true"

  # Frontend Application (Simple static server)
  frontend:
    image: nginx:alpine
    container_name: supplement-frontend
    volumes:
      - ./frontend/public:/usr/share/nginx/html
    networks:
      - supplement-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.frontend.rule=Host(`app.pills.localhost`)"
      - "traefik.http.routers.frontend.entrypoints=web"
      - "traefik.http.services.frontend.loadbalancer.server.port=80"

networks:
  supplement-network:
    driver: bridge
