/**
 * Health Metrics Integration E2E Tests
 * 
 * End-to-end tests for health metrics tracking, wearable device integration, and correlation analysis.
 */

import { test, expect, Page } from '@playwright/test';

// Helper functions
async function loginAsUser(page: Page) {
  await page.goto('/login');
  await page.fill('[data-testid="email-input"]', '<EMAIL>');
  await page.fill('[data-testid="password-input"]', 'password123');
  await page.click('[data-testid="login-button"]');
  await expect(page).toHaveURL('/dashboard');
}

async function navigateToHealth(page: Page) {
  await page.click('[data-testid="nav-health"]');
  await expect(page).toHaveURL('/health');
  await expect(page.locator('h1')).toContainText('Health Dashboard');
}

async function waitForHealthDataToLoad(page: Page) {
  // Wait for health metrics to load
  await page.waitForSelector('[data-testid="health-metric-card"]', { timeout: 10000 });
  await page.waitForTimeout(1000); // Allow time for data to populate
}

test.describe('Health Metrics Integration', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test.describe('Health Dashboard', () => {
    test('should display health dashboard with metrics overview', async ({ page }) => {
      await loginAsUser(page);
      await navigateToHealth(page);

      // Should show dashboard header
      await expect(page.locator('h1')).toContainText('Health Dashboard');
      await expect(page.locator('text=Track your health metrics and discover correlations')).toBeVisible();

      // Should show add metric button
      await expect(page.locator('button:has-text("Add Metric")')).toBeVisible();

      // Should show filter controls
      await expect(page.locator('select')).toBeVisible();

      // Should show tab navigation
      await expect(page.locator('text=Overview')).toBeVisible();
      await expect(page.locator('text=Health Metrics')).toBeVisible();
      await expect(page.locator('text=Correlations')).toBeVisible();
      await expect(page.locator('text=Connected Devices')).toBeVisible();
    });

    test('should display health metrics cards', async ({ page }) => {
      await loginAsUser(page);
      await navigateToHealth(page);
      await waitForHealthDataToLoad(page);

      // Should show health metric cards
      const metricCards = page.locator('[data-testid="health-metric-card"]');
      await expect(metricCards.first()).toBeVisible();

      // Should show metric information
      await expect(page.locator('text=Heart Rate')).toBeVisible();
      await expect(page.locator('text=Sleep Hours')).toBeVisible();
      await expect(page.locator('text=Weight')).toBeVisible();

      // Should show metric values and units
      await expect(page.locator('text=bpm')).toBeVisible();
      await expect(page.locator('text=hours')).toBeVisible();
      await expect(page.locator('text=kg')).toBeVisible();
    });

    test('should display health trends charts', async ({ page }) => {
      await loginAsUser(page);
      await navigateToHealth(page);
      await waitForHealthDataToLoad(page);

      // Should show trend charts
      await expect(page.locator('text=Health Metrics Trends')).toBeVisible();
      await expect(page.locator('text=Metrics by Category')).toBeVisible();

      // Should have SVG charts
      const charts = page.locator('svg');
      await expect(charts.first()).toBeVisible();
    });
  });

  test.describe('Tab Navigation', () => {
    test('should navigate between dashboard tabs', async ({ page }) => {
      await loginAsUser(page);
      await navigateToHealth(page);

      // Start on Overview tab
      await expect(page.locator('text=Health Metrics Trends')).toBeVisible();

      // Switch to Health Metrics tab
      await page.click('text=Health Metrics');
      await waitForHealthDataToLoad(page);

      // Should show all metrics
      const metricCards = page.locator('[data-testid="health-metric-card"]');
      await expect(metricCards).toHaveCount(3); // Assuming 3 test metrics

      // Switch to Correlations tab
      await page.click('text=Correlations');
      await expect(page.locator('text=Health-Supplement Correlations')).toBeVisible();

      // Switch to Connected Devices tab
      await page.click('text=Connected Devices');
      await expect(page.locator('text=Wearable Device Integration')).toBeVisible();
    });

    test('should display correlations data', async ({ page }) => {
      await loginAsUser(page);
      await navigateToHealth(page);

      // Navigate to correlations tab
      await page.click('text=Correlations');

      // Should show correlation cards
      await expect(page.locator('text=Health-Supplement Correlations')).toBeVisible();
      
      // Should show correlation information
      const correlationCards = page.locator('[data-testid="correlation-card"]');
      if (await correlationCards.count() > 0) {
        await expect(correlationCards.first()).toBeVisible();
        await expect(page.locator('text=↔')).toBeVisible(); // Correlation arrow
      }
    });
  });

  test.describe('Wearable Device Integration', () => {
    test('should display supported devices', async ({ page }) => {
      await loginAsUser(page);
      await navigateToHealth(page);

      // Navigate to devices tab
      await page.click('text=Connected Devices');

      // Should show device integration section
      await expect(page.locator('text=Wearable Device Integration')).toBeVisible();
      await expect(page.locator('text=Connect your wearable devices')).toBeVisible();

      // Should show supported devices
      await expect(page.locator('text=Fitbit')).toBeVisible();
      await expect(page.locator('text=Apple Health')).toBeVisible();
      await expect(page.locator('text=Google Fit')).toBeVisible();
      await expect(page.locator('text=Garmin Connect')).toBeVisible();
      await expect(page.locator('text=Samsung Health')).toBeVisible();
    });

    test('should handle device connection', async ({ page }) => {
      await loginAsUser(page);
      await navigateToHealth(page);

      // Navigate to devices tab
      await page.click('text=Connected Devices');

      // Find a device to connect
      const fitbitCard = page.locator('text=Fitbit').locator('..');
      const connectButton = fitbitCard.locator('button:has-text("Connect")');
      
      if (await connectButton.isVisible()) {
        await connectButton.click();

        // Should show connection process (OAuth popup or success message)
        // This would depend on the actual implementation
      }
    });

    test('should display connected device information', async ({ page }) => {
      await loginAsUser(page);
      await navigateToHealth(page);

      // Navigate to devices tab
      await page.click('text=Connected Devices');

      // Should show connected device status
      const connectedDevices = page.locator('text=Connected');
      if (await connectedDevices.count() > 0) {
        await expect(connectedDevices.first()).toBeVisible();
        
        // Should show sync information
        await expect(page.locator('text=Metrics Synced')).toBeVisible();
        await expect(page.locator('text=Data Points')).toBeVisible();
        await expect(page.locator('text=Last sync')).toBeVisible();
      }
    });

    test('should handle device synchronization', async ({ page }) => {
      await loginAsUser(page);
      await navigateToHealth(page);

      // Navigate to devices tab
      await page.click('text=Connected Devices');

      // Find sync button for connected device
      const syncButton = page.locator('button:has-text("Sync Now")');
      if (await syncButton.isVisible()) {
        await syncButton.click();

        // Should show syncing state
        await expect(page.locator('text=Syncing...')).toBeVisible();
        
        // Should show success notification
        await expect(page.locator('.notification')).toContainText('synced successfully');
      }
    });
  });

  test.describe('Health Metric Management', () => {
    test('should add new health metric', async ({ page }) => {
      await loginAsUser(page);
      await navigateToHealth(page);

      // Click add metric button
      await page.click('button:has-text("Add Metric")');

      // Should open add metric modal or navigate to form
      // This would depend on the actual implementation
    });

    test('should edit existing health metric', async ({ page }) => {
      await loginAsUser(page);
      await navigateToHealth(page);
      await waitForHealthDataToLoad(page);

      // Find edit button on a metric card
      const editButton = page.locator('[data-testid="health-metric-card"]').first().locator('button[aria-label="Edit metric"]');
      await editButton.click();

      // Should open edit modal
      await expect(page.locator('text=Edit')).toBeVisible();
      
      // Should have input field for value
      const valueInput = page.locator('input[type="number"]');
      await expect(valueInput).toBeVisible();

      // Update value
      await valueInput.fill('75');
      await page.click('button:has-text("Save")');

      // Should show success notification
      await expect(page.locator('.notification')).toContainText('updated successfully');
    });

    test('should delete health metric', async ({ page }) => {
      await loginAsUser(page);
      await navigateToHealth(page);
      await waitForHealthDataToLoad(page);

      // Find delete button on a metric card
      const deleteButton = page.locator('[data-testid="health-metric-card"]').first().locator('button[aria-label="Delete metric"]');
      await deleteButton.click();

      // Should show confirmation dialog
      page.on('dialog', dialog => dialog.accept());

      // Should show success notification
      await expect(page.locator('.notification')).toContainText('deleted successfully');
    });
  });

  test.describe('Filter and Search', () => {
    test('should filter metrics by category', async ({ page }) => {
      await loginAsUser(page);
      await navigateToHealth(page);
      await waitForHealthDataToLoad(page);

      // Filter by vital signs
      await page.selectOption('select', 'vitals');

      // Should show only vital sign metrics
      await page.waitForTimeout(1000);
      
      // Verify filtering worked (would need to check specific metrics)
    });

    test('should filter by time range', async ({ page }) => {
      await loginAsUser(page);
      await navigateToHealth(page);

      // Change time range
      const timeRangeSelects = page.locator('select');
      await timeRangeSelects.nth(1).selectOption('7d');

      // Should update data for 7-day range
      await page.waitForTimeout(1000);
    });
  });

  test.describe('Responsive Design', () => {
    test('should work on mobile devices', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      await loginAsUser(page);
      await navigateToHealth(page);

      // Should display mobile-friendly layout
      await expect(page.locator('h1')).toBeVisible();
      await expect(page.locator('button:has-text("Add Metric")')).toBeVisible();

      // Tabs should be scrollable on mobile
      const tabsContainer = page.locator('[data-testid="tabs-container"]');
      await expect(tabsContainer).toBeVisible();
    });

    test('should handle tablet layout', async ({ page }) => {
      await page.setViewportSize({ width: 768, height: 1024 });
      await loginAsUser(page);
      await navigateToHealth(page);

      // Should display tablet-friendly layout
      await expect(page.locator('h1')).toBeVisible();
      
      // Metric cards should be properly arranged
      await waitForHealthDataToLoad(page);
      const metricCards = page.locator('[data-testid="health-metric-card"]');
      await expect(metricCards.first()).toBeVisible();
    });
  });

  test.describe('Loading States', () => {
    test('should show loading state while fetching data', async ({ page }) => {
      await loginAsUser(page);
      
      // Intercept health API call to delay response
      await page.route('**/health/metrics*', async route => {
        await new Promise(resolve => setTimeout(resolve, 2000));
        route.continue();
      });

      await navigateToHealth(page);

      // Should show loading spinner
      await expect(page.locator('[role="status"]')).toBeVisible();
    });

    test('should handle empty data state', async ({ page }) => {
      await loginAsUser(page);
      
      // Mock empty health response
      await page.route('**/health/metrics*', route => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify([])
        });
      });

      await navigateToHealth(page);

      // Should show empty state
      await expect(page.locator('text=No health metrics yet')).toBeVisible();
      await expect(page.locator('text=Start tracking your health metrics')).toBeVisible();
    });
  });

  test.describe('Error Handling', () => {
    test('should handle health API errors', async ({ page }) => {
      await loginAsUser(page);
      
      // Mock API error
      await page.route('**/health/metrics*', route => {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ detail: 'Internal server error' })
        });
      });

      await navigateToHealth(page);

      // Should show error state
      await expect(page.locator('text=No health metrics yet')).toBeVisible();
    });

    test('should handle device sync errors', async ({ page }) => {
      await loginAsUser(page);
      await navigateToHealth(page);

      // Navigate to devices tab
      await page.click('text=Connected Devices');

      // Mock sync error
      await page.route('**/health/devices/*/sync', route => {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ detail: 'Sync failed' })
        });
      });

      // Try to sync a device
      const syncButton = page.locator('button:has-text("Sync Now")');
      if (await syncButton.isVisible()) {
        await syncButton.click();

        // Should show error notification
        await expect(page.locator('.notification')).toContainText('Failed to sync');
      }
    });
  });

  test.describe('Accessibility', () => {
    test('should be keyboard navigable', async ({ page }) => {
      await loginAsUser(page);
      await navigateToHealth(page);

      // Tab through interactive elements
      await page.keyboard.press('Tab'); // Category filter
      await expect(page.locator('select').first()).toBeFocused();

      await page.keyboard.press('Tab'); // Time range filter
      await expect(page.locator('select').nth(1)).toBeFocused();

      await page.keyboard.press('Tab'); // Add metric button
      await expect(page.locator('button:has-text("Add Metric")')).toBeFocused();

      // Should be able to activate with Enter
      await page.keyboard.press('Enter');
    });

    test('should have proper ARIA labels and roles', async ({ page }) => {
      await loginAsUser(page);
      await navigateToHealth(page);

      // Check for proper heading structure
      await expect(page.locator('h1')).toBeVisible();
      
      // Check for proper button roles
      const buttons = page.locator('button');
      const buttonCount = await buttons.count();
      
      for (let i = 0; i < Math.min(buttonCount, 3); i++) {
        const button = buttons.nth(i);
        await expect(button).toHaveAttribute('type', 'button');
      }
    });

    test('should support screen readers', async ({ page }) => {
      await loginAsUser(page);
      await navigateToHealth(page);

      // Check for descriptive text and proper structure
      await expect(page.locator('h1')).toContainText('Health Dashboard');
      await expect(page.locator('text=Track your health metrics')).toBeVisible();

      // Metric cards should have meaningful content
      await waitForHealthDataToLoad(page);
      await expect(page.locator('text=Heart Rate')).toBeVisible();
      await expect(page.locator('text=Sleep Hours')).toBeVisible();
    });
  });

  test.describe('Performance', () => {
    test('should load health page quickly', async ({ page }) => {
      const startTime = Date.now();
      
      await loginAsUser(page);
      await navigateToHealth(page);
      
      const loadTime = Date.now() - startTime;
      
      // Should load within reasonable time
      expect(loadTime).toBeLessThan(5000);
      
      // Should show content
      await expect(page.locator('h1')).toBeVisible();
    });

    test('should handle large datasets efficiently', async ({ page }) => {
      await loginAsUser(page);
      await navigateToHealth(page);

      // Should render multiple metrics efficiently
      await waitForHealthDataToLoad(page);
      
      const renderStart = Date.now();
      await page.click('text=Health Metrics');
      const renderTime = Date.now() - renderStart;

      // Should render quickly
      expect(renderTime).toBeLessThan(2000);
    });
  });

  test.describe('Data Accuracy', () => {
    test('should display consistent metric values', async ({ page }) => {
      await loginAsUser(page);
      await navigateToHealth(page);
      await waitForHealthDataToLoad(page);

      // Verify that metric values are displayed consistently
      // This would require checking that the same data appears in different views
      
      // For example, if heart rate is 72 in overview, 
      // it should be 72 in the metrics tab as well
      const heartRateInOverview = page.locator('text=Heart Rate').locator('..');
      await expect(heartRateInOverview).toBeVisible();

      await page.click('text=Health Metrics');
      const heartRateInMetrics = page.locator('text=Heart Rate').locator('..');
      await expect(heartRateInMetrics).toBeVisible();
    });

    test('should update data when filters change', async ({ page }) => {
      await loginAsUser(page);
      await navigateToHealth(page);
      await waitForHealthDataToLoad(page);

      // Get initial metric count
      const initialMetrics = await page.locator('[data-testid="health-metric-card"]').count();

      // Change category filter
      await page.selectOption('select', 'vitals');
      await page.waitForTimeout(1000);

      // Metric count should potentially change (depending on data)
      // This test would need real data to verify changes
    });
  });

  test.describe('User Authentication', () => {
    test('should require authentication to view health dashboard', async ({ page }) => {
      await page.goto('/health');

      // Should redirect to login or show auth prompt
      await expect(page).toHaveURL('/login');
    });

    test('should show appropriate message for unauthenticated users', async ({ page }) => {
      // Mock unauthenticated state
      await page.goto('/health');
      
      // If the page loads but shows auth message
      const authMessage = page.locator('text=Please log in to view health dashboard');
      if (await authMessage.isVisible()) {
        await expect(authMessage).toBeVisible();
      }
    });
  });
});
