/**
 * Analytics Data Visualization E2E Tests
 * 
 * End-to-end tests for analytics dashboard, data visualization, and insights.
 */

import { test, expect, Page } from '@playwright/test';

// Helper functions
async function loginAsUser(page: Page) {
  await page.goto('/login');
  await page.fill('[data-testid="email-input"]', '<EMAIL>');
  await page.fill('[data-testid="password-input"]', 'password123');
  await page.click('[data-testid="login-button"]');
  await expect(page).toHaveURL('/dashboard');
}

async function navigateToAnalytics(page: Page) {
  await page.click('[data-testid="nav-analytics"]');
  await expect(page).toHaveURL('/analytics');
  await expect(page.locator('h1')).toContainText('Analytics Dashboard');
}

async function waitForChartsToLoad(page: Page) {
  // Wait for charts to render
  await page.waitForSelector('svg', { timeout: 10000 });
  await page.waitForTimeout(1000); // Allow time for animations
}

test.describe('Analytics Data Visualization', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test.describe('Analytics Dashboard', () => {
    test('should display analytics dashboard with key metrics', async ({ page }) => {
      await loginAsUser(page);
      await navigateToAnalytics(page);

      // Should show dashboard header
      await expect(page.locator('h1')).toContainText('Analytics Dashboard');
      await expect(page.locator('text=Track your supplement journey')).toBeVisible();

      // Should show export button
      await expect(page.locator('button:has-text("Export Data")')).toBeVisible();

      // Should show filter controls
      await expect(page.locator('select')).toBeVisible();

      // Should show metric cards
      await expect(page.locator('text=Total Supplements')).toBeVisible();
      await expect(page.locator('text=Adherence Rate')).toBeVisible();
      await expect(page.locator('text=Total Intakes')).toBeVisible();
      await expect(page.locator('text=Current Streak')).toBeVisible();
    });

    test('should display metric values and trends', async ({ page }) => {
      await loginAsUser(page);
      await navigateToAnalytics(page);

      // Should show metric values (numbers)
      const metricValues = page.locator('[class*="MetricValue"]');
      await expect(metricValues.first()).toBeVisible();

      // Should show trend indicators
      await expect(page.locator('text=vs last period')).toBeVisible();

      // Should show trend icons (up/down arrows)
      const trendIcons = page.locator('[class*="MetricChange"]');
      await expect(trendIcons.first()).toBeVisible();
    });

    test('should render interactive charts', async ({ page }) => {
      await loginAsUser(page);
      await navigateToAnalytics(page);
      await waitForChartsToLoad(page);

      // Should show line chart for adherence trend
      await expect(page.locator('text=Adherence Trend')).toBeVisible();
      
      // Should show bar chart for categories
      await expect(page.locator('text=Supplements by Category')).toBeVisible();
      
      // Should show pie chart for distribution
      await expect(page.locator('text=Supplement Distribution')).toBeVisible();

      // Should have SVG charts
      const charts = page.locator('svg');
      await expect(charts.first()).toBeVisible();
    });

    test('should display AI-powered insights', async ({ page }) => {
      await loginAsUser(page);
      await navigateToAnalytics(page);

      // Should show insights section
      await expect(page.locator('text=AI-Powered Insights')).toBeVisible();

      // Should show insight cards
      const insightCards = page.locator('[class*="InsightCard"]');
      if (await insightCards.count() > 0) {
        await expect(insightCards.first()).toBeVisible();
      }
    });
  });

  test.describe('Filter Controls', () => {
    test('should change time range filter', async ({ page }) => {
      await loginAsUser(page);
      await navigateToAnalytics(page);

      // Change time range
      await page.selectOption('select', 'Last 7 days');
      
      // Should update the selection
      await expect(page.locator('select')).toHaveValue('7d');

      // Charts should update (would need to verify data changes)
      await waitForChartsToLoad(page);
    });

    test('should show custom date range inputs', async ({ page }) => {
      await loginAsUser(page);
      await navigateToAnalytics(page);

      // Select custom range
      await page.selectOption('select', 'Custom range');

      // Should show date inputs
      const dateInputs = page.locator('input[type="date"]');
      await expect(dateInputs.first()).toBeVisible();
      await expect(dateInputs.nth(1)).toBeVisible();
    });

    test('should handle custom date range selection', async ({ page }) => {
      await loginAsUser(page);
      await navigateToAnalytics(page);

      // Select custom range
      await page.selectOption('select', 'Custom range');

      // Set custom dates
      await page.fill('input[type="date"]', '2024-01-01');
      await page.fill('input[type="date"]:nth-of-type(2)', '2024-01-31');

      // Should update the date values
      await expect(page.locator('input[type="date"]').first()).toHaveValue('2024-01-01');
      await expect(page.locator('input[type="date"]').nth(1)).toHaveValue('2024-01-31');
    });
  });

  test.describe('Chart Interactions', () => {
    test('should show tooltips on chart hover', async ({ page }) => {
      await loginAsUser(page);
      await navigateToAnalytics(page);
      await waitForChartsToLoad(page);

      // Hover over chart elements
      const chartSvg = page.locator('svg').first();
      await chartSvg.hover();

      // Should show tooltip (implementation dependent)
      // This would need to be adjusted based on actual chart implementation
    });

    test('should handle chart click interactions', async ({ page }) => {
      await loginAsUser(page);
      await navigateToAnalytics(page);
      await waitForChartsToLoad(page);

      // Click on chart elements
      const chartSvg = page.locator('svg').first();
      await chartSvg.click();

      // Should handle click interactions (implementation dependent)
    });

    test('should display chart legends correctly', async ({ page }) => {
      await loginAsUser(page);
      await navigateToAnalytics(page);
      await waitForChartsToLoad(page);

      // Should show pie chart legend
      const pieChartContainer = page.locator('text=Supplement Distribution').locator('..');
      const legend = pieChartContainer.locator('[class*="Legend"]');
      
      if (await legend.count() > 0) {
        await expect(legend).toBeVisible();
      }
    });
  });

  test.describe('Data Export', () => {
    test('should handle data export', async ({ page }) => {
      await loginAsUser(page);
      await navigateToAnalytics(page);

      // Click export button
      const exportButton = page.locator('button:has-text("Export Data")');
      await exportButton.click();

      // Should trigger export (would need to verify download or modal)
      // This depends on the actual export implementation
    });

    test('should show export options', async ({ page }) => {
      await loginAsUser(page);
      await navigateToAnalytics(page);

      // Click export button
      await page.click('button:has-text("Export Data")');

      // Should show export options (if implemented as modal)
      // This would depend on the actual UI implementation
    });
  });

  test.describe('Responsive Design', () => {
    test('should work on mobile devices', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      await loginAsUser(page);
      await navigateToAnalytics(page);

      // Should display mobile-friendly layout
      await expect(page.locator('h1')).toBeVisible();
      await expect(page.locator('text=Total Supplements')).toBeVisible();

      // Charts should be responsive
      await waitForChartsToLoad(page);
      const charts = page.locator('svg');
      await expect(charts.first()).toBeVisible();
    });

    test('should handle tablet layout', async ({ page }) => {
      await page.setViewportSize({ width: 768, height: 1024 });
      await loginAsUser(page);
      await navigateToAnalytics(page);

      // Should display tablet-friendly layout
      await expect(page.locator('h1')).toBeVisible();
      
      // Metric cards should be properly arranged
      const metricCards = page.locator('[class*="MetricCard"]');
      await expect(metricCards.first()).toBeVisible();
    });
  });

  test.describe('Loading States', () => {
    test('should show loading state while fetching data', async ({ page }) => {
      await loginAsUser(page);
      
      // Intercept analytics API call to delay response
      await page.route('**/analytics/dashboard*', async route => {
        await new Promise(resolve => setTimeout(resolve, 2000));
        route.continue();
      });

      await navigateToAnalytics(page);

      // Should show loading spinner
      await expect(page.locator('[role="status"]')).toBeVisible();
    });

    test('should handle empty data state', async ({ page }) => {
      await loginAsUser(page);
      
      // Mock empty analytics response
      await page.route('**/analytics/dashboard*', route => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ metrics: null, trends: null, distribution: null, insights: [] })
        });
      });

      await navigateToAnalytics(page);

      // Should show empty state
      await expect(page.locator('text=No data available')).toBeVisible();
      await expect(page.locator('text=Start tracking your supplements')).toBeVisible();
    });
  });

  test.describe('Error Handling', () => {
    test('should handle analytics API errors', async ({ page }) => {
      await loginAsUser(page);
      
      // Mock API error
      await page.route('**/analytics/dashboard*', route => {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ detail: 'Internal server error' })
        });
      });

      await navigateToAnalytics(page);

      // Should show error state
      await expect(page.locator('text=No data available')).toBeVisible();
    });

    test('should handle network connectivity issues', async ({ page }) => {
      await loginAsUser(page);
      
      // Simulate network failure
      await page.route('**/analytics/dashboard*', route => {
        route.abort('failed');
      });

      await navigateToAnalytics(page);

      // Should show appropriate error message
      await expect(page.locator('text=No data available')).toBeVisible();
    });
  });

  test.describe('Accessibility', () => {
    test('should be keyboard navigable', async ({ page }) => {
      await loginAsUser(page);
      await navigateToAnalytics(page);

      // Tab through interactive elements
      await page.keyboard.press('Tab'); // Time range select
      await expect(page.locator('select')).toBeFocused();

      await page.keyboard.press('Tab'); // Export button
      await expect(page.locator('button:has-text("Export Data")')).toBeFocused();

      // Should be able to activate with Enter
      await page.keyboard.press('Enter');
    });

    test('should have proper ARIA labels and roles', async ({ page }) => {
      await loginAsUser(page);
      await navigateToAnalytics(page);

      // Check for proper heading structure
      await expect(page.locator('h1')).toBeVisible();
      
      // Check for proper button roles
      const buttons = page.locator('button');
      const buttonCount = await buttons.count();
      
      for (let i = 0; i < Math.min(buttonCount, 3); i++) {
        const button = buttons.nth(i);
        await expect(button).toHaveAttribute('type', 'button');
      }
    });

    test('should support screen readers', async ({ page }) => {
      await loginAsUser(page);
      await navigateToAnalytics(page);

      // Check for descriptive text and proper structure
      await expect(page.locator('h1')).toContainText('Analytics Dashboard');
      await expect(page.locator('text=Track your supplement journey')).toBeVisible();

      // Metric cards should have meaningful labels
      await expect(page.locator('text=Total Supplements')).toBeVisible();
      await expect(page.locator('text=Adherence Rate')).toBeVisible();
    });
  });

  test.describe('Performance', () => {
    test('should load analytics page quickly', async ({ page }) => {
      const startTime = Date.now();
      
      await loginAsUser(page);
      await navigateToAnalytics(page);
      
      const loadTime = Date.now() - startTime;
      
      // Should load within reasonable time
      expect(loadTime).toBeLessThan(5000);
      
      // Should show content
      await expect(page.locator('h1')).toBeVisible();
    });

    test('should render charts efficiently', async ({ page }) => {
      await loginAsUser(page);
      await navigateToAnalytics(page);

      const chartRenderStart = Date.now();
      await waitForChartsToLoad(page);
      const chartRenderTime = Date.now() - chartRenderStart;

      // Charts should render within reasonable time
      expect(chartRenderTime).toBeLessThan(3000);

      // Should have rendered charts
      const charts = page.locator('svg');
      await expect(charts.first()).toBeVisible();
    });
  });

  test.describe('Data Accuracy', () => {
    test('should display consistent data across components', async ({ page }) => {
      await loginAsUser(page);
      await navigateToAnalytics(page);
      await waitForChartsToLoad(page);

      // Verify that metric values are consistent
      // This would require checking that the same data appears in different visualizations
      
      // For example, if total supplements is 12 in metrics, 
      // it should be reflected consistently in charts
      const totalSupplements = page.locator('text=Total Supplements').locator('..');
      await expect(totalSupplements).toBeVisible();
    });

    test('should update data when filters change', async ({ page }) => {
      await loginAsUser(page);
      await navigateToAnalytics(page);
      await waitForChartsToLoad(page);

      // Get initial metric value
      const initialMetric = await page.locator('[class*="MetricValue"]').first().textContent();

      // Change time range
      await page.selectOption('select', 'Last 7 days');
      await waitForChartsToLoad(page);

      // Metric should potentially change (depending on data)
      // This test would need real data to verify changes
    });
  });

  test.describe('User Authentication', () => {
    test('should require authentication to view analytics', async ({ page }) => {
      await page.goto('/analytics');

      // Should redirect to login or show auth prompt
      await expect(page).toHaveURL('/login');
    });

    test('should show appropriate message for unauthenticated users', async ({ page }) => {
      // Mock unauthenticated state
      await page.goto('/analytics');
      
      // If the page loads but shows auth message
      const authMessage = page.locator('text=Please log in to view analytics');
      if (await authMessage.isVisible()) {
        await expect(authMessage).toBeVisible();
      }
    });
  });
});
