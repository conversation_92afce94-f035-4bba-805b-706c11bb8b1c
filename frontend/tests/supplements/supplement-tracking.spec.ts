/**
 * Supplement Tracking E2E Tests
 * 
 * End-to-end tests for supplement tracking workflows using Playwright.
 */

import { test, expect } from '@playwright/test';

// Test data
const testUser = {
  email: '<EMAIL>',
  password: 'password123',
};

const mockSupplements = [
  {
    id: '1',
    name: 'Vitamin D3',
    brand: 'Nature Made',
    category: 'vitamins',
    description: 'Supports bone health and immune function',
    serving_size: '2000',
    serving_unit: 'IU',
  },
  {
    id: '2',
    name: 'Omega-3 Fish Oil',
    brand: 'Nordic Naturals',
    category: 'omega-3',
    description: 'High-quality fish oil supplement',
    serving_size: '1000',
    serving_unit: 'mg',
  },
];

test.describe('Supplement Tracking Workflow', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication
    await page.route('**/api/v1/auth/login', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          access_token: 'mock-token',
          token_type: 'bearer',
          expires_in: 3600,
          user: {
            id: '1',
            email: testUser.email,
            full_name: 'Test User',
            is_active: true,
            is_superuser: false,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          },
        }),
      });
    });

    // Mock supplements API
    await page.route('**/api/v1/supplements**', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          items: mockSupplements,
          total: mockSupplements.length,
          page: 1,
          size: 20,
          pages: 1,
        }),
      });
    });

    // Login first
    await page.goto('/login');
    await page.getByLabel(/email address/i).fill(testUser.email);
    await page.getByLabel(/password/i).fill(testUser.password);
    await page.getByRole('button', { name: /sign in/i }).click();
    await expect(page).toHaveURL('/dashboard');
  });

  test.describe('Supplement Discovery', () => {
    test('should navigate to supplements page and display supplement library', async ({ page }) => {
      await page.goto('/supplements');

      // Check page elements
      await expect(page.getByRole('heading', { name: /supplement library/i })).toBeVisible();
      await expect(page.getByPlaceholder(/search supplements/i)).toBeVisible();
      await expect(page.getByDisplayValue(/all categories/i)).toBeVisible();

      // Check supplement cards are displayed
      await expect(page.getByText('Vitamin D3')).toBeVisible();
      await expect(page.getByText('Omega-3 Fish Oil')).toBeVisible();
    });

    test('should search for supplements', async ({ page }) => {
      await page.goto('/supplements');

      // Search for vitamin
      const searchInput = page.getByPlaceholder(/search supplements/i);
      await searchInput.fill('vitamin');

      // Should show search results
      await expect(page.getByText(/search results/i)).toBeVisible();
      await expect(page.getByText('Vitamin D3')).toBeVisible();
    });

    test('should filter supplements by category', async ({ page }) => {
      await page.goto('/supplements');

      // Select vitamins category
      const categorySelect = page.getByDisplayValue(/all categories/i);
      await categorySelect.selectOption('vitamins');

      // Should show filtered results
      await expect(page.getByText('Vitamin D3')).toBeVisible();
    });

    test('should clear filters', async ({ page }) => {
      await page.goto('/supplements');

      // Apply filters
      await page.getByPlaceholder(/search supplements/i).fill('vitamin');
      await page.getByDisplayValue(/all categories/i).selectOption('vitamins');

      // Clear filters
      await page.getByRole('button', { name: /clear filters/i }).click();

      // Filters should be cleared
      await expect(page.getByPlaceholder(/search supplements/i)).toHaveValue('');
      await expect(page.getByDisplayValue(/all categories/i)).toHaveValue('');
    });
  });

  test.describe('Quick Add Functionality', () => {
    test('should open quick track modal when quick add is clicked', async ({ page }) => {
      await page.goto('/supplements');

      // Click quick add on first supplement
      await page.getByRole('button', { name: /quick add/i }).first().click();

      // Modal should open
      await expect(page.getByRole('heading', { name: /log supplement intake/i })).toBeVisible();
      await expect(page.getByText('Vitamin D3')).toBeVisible();
    });

    test('should log supplement intake through quick add modal', async ({ page }) => {
      // Mock the track intake API
      await page.route('**/api/v1/supplements/*/track', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            id: 'intake-1',
            user_id: '1',
            supplement_id: '1',
            supplement: mockSupplements[0],
            dosage: 2000,
            dosage_unit: 'IU',
            taken_at: new Date().toISOString(),
            notes: '',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          }),
        });
      });

      await page.goto('/supplements');

      // Open quick add modal
      await page.getByRole('button', { name: /quick add/i }).first().click();

      // Fill in dosage (should be pre-filled)
      const dosageInput = page.getByLabel(/dosage/i);
      await expect(dosageInput).toHaveValue('2000');

      // Add notes
      await page.getByLabel(/notes/i).fill('Taken with breakfast');

      // Submit
      await page.getByRole('button', { name: /log intake/i }).click();

      // Should show success notification
      await expect(page.getByText(/successfully logged/i)).toBeVisible();
    });

    test('should use quick dosage buttons', async ({ page }) => {
      await page.goto('/supplements');

      // Open quick add modal
      await page.getByRole('button', { name: /quick add/i }).first().click();

      // Click a quick dosage button
      await page.getByRole('button', { name: /1000 IU/i }).click();

      // Dosage should be updated
      await expect(page.getByLabel(/dosage/i)).toHaveValue('1000');
    });

    test('should validate required fields', async ({ page }) => {
      await page.goto('/supplements');

      // Open quick add modal
      await page.getByRole('button', { name: /quick add/i }).first().click();

      // Clear dosage
      await page.getByLabel(/dosage/i).clear();

      // Try to submit
      await page.getByRole('button', { name: /log intake/i }).click();

      // Should show validation error
      await expect(page.getByText(/dosage is required/i)).toBeVisible();
    });

    test('should close modal when cancel is clicked', async ({ page }) => {
      await page.goto('/supplements');

      // Open quick add modal
      await page.getByRole('button', { name: /quick add/i }).first().click();

      // Click cancel
      await page.getByRole('button', { name: /cancel/i }).click();

      // Modal should close
      await expect(page.getByRole('heading', { name: /log supplement intake/i })).not.toBeVisible();
    });
  });

  test.describe('Supplement Detail View', () => {
    test('should navigate to supplement detail page', async ({ page }) => {
      // Mock single supplement API
      await page.route('**/api/v1/supplements/1', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify(mockSupplements[0]),
        });
      });

      await page.goto('/supplements');

      // Click on supplement details
      await page.getByRole('button', { name: /details/i }).first().click();

      // Should navigate to detail page
      await expect(page).toHaveURL('/supplements/1');
      await expect(page.getByRole('heading', { name: 'Vitamin D3' })).toBeVisible();
    });

    test('should display supplement information on detail page', async ({ page }) => {
      await page.route('**/api/v1/supplements/1', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify(mockSupplements[0]),
        });
      });

      await page.goto('/supplements/1');

      // Check supplement information
      await expect(page.getByText('Vitamin D3')).toBeVisible();
      await expect(page.getByText('by Nature Made')).toBeVisible();
      await expect(page.getByText('Vitamins')).toBeVisible();
      await expect(page.getByText(/supports bone health/i)).toBeVisible();
    });

    test('should log intake from detail page', async ({ page }) => {
      await page.route('**/api/v1/supplements/1', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify(mockSupplements[0]),
        });
      });

      await page.route('**/api/v1/supplements/1/track', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            id: 'intake-1',
            user_id: '1',
            supplement_id: '1',
            supplement: mockSupplements[0],
            dosage: 2000,
            dosage_unit: 'IU',
            taken_at: new Date().toISOString(),
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          }),
        });
      });

      await page.goto('/supplements/1');

      // Click log intake button
      await page.getByRole('button', { name: /log intake/i }).click();

      // Fill and submit modal
      await page.getByRole('button', { name: /log intake/i }).click();

      // Should show success notification
      await expect(page.getByText(/successfully logged/i)).toBeVisible();
    });
  });

  test.describe('Tracking Page', () => {
    test('should display tracking page with search and history', async ({ page }) => {
      // Mock intake history
      await page.route('**/api/v1/supplements/intake/history**', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify([
            {
              id: 'intake-1',
              user_id: '1',
              supplement_id: '1',
              supplement: mockSupplements[0],
              dosage: 2000,
              dosage_unit: 'IU',
              taken_at: new Date().toISOString(),
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            },
          ]),
        });
      });

      await page.goto('/tracking');

      // Check page elements
      await expect(page.getByRole('heading', { name: /supplement tracking/i })).toBeVisible();
      await expect(page.getByPlaceholder(/search supplements/i)).toBeVisible();
      await expect(page.getByText(/quick actions/i)).toBeVisible();
    });

    test('should show intake history', async ({ page }) => {
      await page.route('**/api/v1/supplements/intake/history**', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify([
            {
              id: 'intake-1',
              user_id: '1',
              supplement_id: '1',
              supplement: mockSupplements[0],
              dosage: 2000,
              dosage_unit: 'IU',
              taken_at: new Date().toISOString(),
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            },
          ]),
        });
      });

      await page.goto('/tracking');

      // Should show intake history
      await expect(page.getByText(/intake history/i)).toBeVisible();
      await expect(page.getByText('Vitamin D3')).toBeVisible();
      await expect(page.getByText('2000 IU')).toBeVisible();
    });
  });

  test.describe('Mobile Responsiveness', () => {
    test('should display properly on mobile devices', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      await page.goto('/supplements');

      // Check mobile layout
      await expect(page.getByRole('heading', { name: /supplement library/i })).toBeVisible();
      await expect(page.getByPlaceholder(/search supplements/i)).toBeVisible();
      
      // Should show filters button in compact mode
      await expect(page.getByRole('button', { name: /filters/i })).toBeVisible();
    });

    test('should open filters on mobile', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      await page.goto('/supplements');

      // Click filters button
      await page.getByRole('button', { name: /filters/i }).click();

      // Filters should be visible
      await expect(page.getByDisplayValue(/all categories/i)).toBeVisible();
    });
  });

  test.describe('Error Handling', () => {
    test('should handle API errors gracefully', async ({ page }) => {
      // Mock API error
      await page.route('**/api/v1/supplements**', async route => {
        await route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({
            detail: 'Internal server error',
          }),
        });
      });

      await page.goto('/supplements');

      // Should show error state
      await expect(page.getByText(/failed to fetch supplements/i)).toBeVisible();
    });

    test('should handle supplement not found', async ({ page }) => {
      await page.route('**/api/v1/supplements/999', async route => {
        await route.fulfill({
          status: 404,
          contentType: 'application/json',
          body: JSON.stringify({
            detail: 'Supplement not found',
          }),
        });
      });

      await page.goto('/supplements/999');

      // Should show not found message
      await expect(page.getByText(/supplement not found/i)).toBeVisible();
      await expect(page.getByRole('button', { name: /back to supplements/i })).toBeVisible();
    });
  });
});
