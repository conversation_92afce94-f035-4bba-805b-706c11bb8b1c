/**
 * Playwright Global Setup
 * 
 * Global setup for Playwright tests including test data preparation.
 */

import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global setup for Playwright tests...');

  // Create a browser instance for setup
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Setup test data or perform any global initialization
    console.log('📊 Setting up test data...');

    // Example: Create test users, seed database, etc.
    // This would typically involve API calls to your backend
    
    // For now, we'll just verify the app is running
    await page.goto(config.projects[0].use?.baseURL || 'http://localhost:3000');
    
    console.log('✅ Global setup completed successfully');
  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await context.close();
    await browser.close();
  }
}

export default globalSetup;
