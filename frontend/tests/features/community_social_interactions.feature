Feature: Community Social Interactions
  As a supplement user
  I want to interact with the community through posts and social features
  So that I can share experiences and learn from others

  Background:
    Given I am logged in as a community member
    And I am on the community feed page

  Scenario: Browse community feed
    When I visit the community feed
    Then I should see the community header with welcome message
    And I should see feed statistics:
      | Statistic     |
      | Total Posts   |
      | Today's Posts |
      | Active Users  |
      | Total Likes   |
    And I should see a list of community posts
    And each post should display:
      | Information       |
      | Author name       |
      | Post type badge   |
      | Post content      |
      | Like count        |
      | Comment count     |
      | Share count       |
      | Timestamp         |

  Scenario: Create a supplement review post
    When I click the "Create Post" button
    Then I should see the post creation modal
    And I should see my profile information

    When I select "Supplement Review" as the post type
    And I fill in the title "Amazing Results with Magnesium Glycinate"
    And I fill in the content:
      """
      I've been taking magnesium glycinate for 3 months now and the improvement 
      in my sleep quality has been remarkable. I fall asleep faster and wake up 
      more refreshed. Started with 200mg before bed and gradually increased to 400mg. 
      No digestive issues like I had with magnesium oxide. Highly recommend for 
      anyone struggling with sleep issues!
      """
    And I add tags:
      | Tag       |
      | magnesium |
      | sleep     |
      | glycinate |
    And I click "Post"
    Then I should see a success notification "Post created successfully"
    And I should see my new post in the feed
    And the post should show "Supplement Review" badge
    And the post should display all my tags

  Scenario: Create a research finding post
    When I click the "Create Post" button
    And I select "Research Finding" as the post type
    And I fill in the title "New Study on Omega-3 and Cognitive Function"
    And I fill in the content:
      """
      Recent research published in the Journal of Nutrition shows that omega-3 
      supplementation may improve cognitive function in older adults. The study 
      followed 200 participants over 12 months with significant improvements 
      in memory and processing speed.
      """
    And I add tags:
      | Tag                |
      | omega-3           |
      | research          |
      | cognitive-function |
      | study             |
    And I click "Post"
    Then I should see a success notification "Post created successfully"
    And the post should show "Research Finding" badge

  Scenario: Create an experience share post
    When I click the "Create Post" button
    And I select "Experience Share" as the post type
    And I fill in the content:
      """
      Just wanted to share my supplement journey over the past year. Started with 
      a basic multivitamin and gradually added targeted supplements based on blood 
      work results. The biggest game-changers have been vitamin D3, magnesium, 
      and omega-3. Feeling more energetic and sleeping better than ever!
      """
    And I add tags:
      | Tag          |
      | journey      |
      | blood-work   |
      | multivitamin |
      | energy       |
    And I click "Post"
    Then I should see a success notification "Post created successfully"
    And the post should show "Experience" badge

  Scenario: Validate post creation requirements
    When I click the "Create Post" button
    And I try to submit without filling the content
    And I click "Post"
    Then I should see a validation error "Content is required"
    And the post should not be created

    When I fill in content that exceeds the character limit
    Then I should see a character count warning
    And I should see a validation error about character limit
    And the "Post" button should be disabled

  Scenario: Manage post tags
    When I click the "Create Post" button
    And I start adding tags in the tag input field
    And I type "vitamin-d" and press Enter
    Then I should see "#vitamin-d" tag added

    When I type "health" and press Enter
    Then I should see "#health" tag added
    And I should see both tags displayed

    When I click the remove button on the "vitamin-d" tag
    Then the "vitamin-d" tag should be removed
    And the "health" tag should remain

    When I try to add more than 5 tags
    Then I should see an error "Maximum 5 tags allowed"
    And the tag input should be disabled

  Scenario: Like and unlike posts
    Given there are posts in the community feed
    When I click the like button on a post
    Then the like count should increase by 1
    And the like button should show as active/filled
    And I should see a visual feedback for the like action

    When I click the like button again to unlike
    Then the like count should decrease by 1
    And the like button should show as inactive/unfilled

  Scenario: Comment on posts
    Given there is a post in the community feed
    When I click the comment button on the post
    Then I should be taken to the post details page
    Or I should see a comment interface

    When I write a comment:
      """
      Thanks for sharing! I've been considering trying magnesium for sleep. 
      What brand do you use?
      """
    And I submit the comment
    Then I should see my comment added to the post
    And the comment count should increase by 1

  Scenario: Share posts
    Given there is a post in the community feed
    When I click the share button on the post
    Then I should see a success notification "Post shared successfully"
    And the share count should increase by 1

  Scenario: Navigate to user profiles
    Given there are posts in the community feed
    When I click on a post author's name
    Then I should be taken to their user profile page
    And I should see their profile information:
      | Information      |
      | Profile picture  |
      | Full name        |
      | Join date        |
      | Post count       |
      | Follower count   |
      | Following count  |

  Scenario: Follow and unfollow users
    Given I am viewing another user's profile
    When I click the "Follow" button
    Then the button should change to "Following"
    And the user's follower count should increase by 1
    And I should see their posts in my personalized feed

    When I click the "Following" button to unfollow
    Then the button should change back to "Follow"
    And the user's follower count should decrease by 1

  Scenario: Search and filter community posts
    Given there are multiple posts in the community feed
    When I search for "vitamin D"
    Then I should see only posts containing "vitamin D"
    And the search results should be highlighted

    When I clear the search
    Then I should see all posts again

    When I filter by "Supplement Reviews"
    Then I should see only posts with "Supplement Review" badge
    And other post types should be hidden

    When I change the sort to "Most Popular"
    Then the posts should be reordered by popularity
    And the most liked posts should appear first

  Scenario: Handle long post content
    Given there is a post with very long content
    When I view the post in the feed
    Then the content should be truncated
    And I should see a "Read more" button

    When I click "Read more"
    Then the full content should be displayed
    And the "Read more" button should disappear

  Scenario: Mobile community interaction
    Given I am using a mobile device
    When I browse the community feed
    Then the interface should be mobile-optimized
    And all posts should be easily readable
    And interaction buttons should be touch-friendly

    When I create a post on mobile
    Then the creation modal should be mobile-friendly
    And all form fields should be easily accessible
    And the virtual keyboard should not obstruct the interface

  Scenario: Accessibility in community features
    Given I am using assistive technology
    When I navigate the community feed
    Then all posts should be accessible via keyboard
    And screen readers should announce post information clearly
    And all interactive elements should have proper labels

    When I create a post
    Then all form fields should have proper labels
    And validation errors should be announced
    And the creation process should be fully keyboard accessible

  Scenario: Handle community errors gracefully
    Given there is a network connectivity issue
    When I try to create a post
    Then I should see a clear error message
    And I should be able to retry the action
    And my post content should not be lost

    When I try to like a post during network issues
    Then I should see an appropriate error message
    And the like state should not change incorrectly

  Scenario: Real-time community updates
    Given I am viewing the community feed
    When another user creates a new post
    Then I should see a notification about the new post
    Or the feed should update automatically

    When someone likes my post
    Then I should receive a notification
    And the like count should update in real-time

  Scenario: Community moderation
    Given I see inappropriate content in a post
    When I click the "Report" option
    Then I should see a reporting interface
    And I should be able to select a reason for reporting
    And the report should be submitted to moderators

  Scenario: Post engagement analytics
    Given I have created posts in the community
    When I view my profile or post analytics
    Then I should see engagement metrics:
      | Metric           |
      | Total likes      |
      | Total comments   |
      | Total shares     |
      | Reach/views      |
      | Engagement rate  |

  Scenario: Community notifications
    When I receive community notifications
    Then I should see notifications for:
      | Notification Type    |
      | New followers        |
      | Likes on my posts    |
      | Comments on my posts |
      | Mentions in posts    |
      | Replies to comments  |
    And I should be able to manage notification preferences

  Scenario: Content discovery and recommendations
    Given I have interacted with certain types of content
    When I browse the community feed
    Then I should see personalized content recommendations
    And posts should be ranked based on my interests
    And I should see suggestions for users to follow
