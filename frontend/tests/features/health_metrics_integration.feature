Feature: Health Metrics Integration
  As a supplement user
  I want to track my health metrics and correlate them with my supplement intake
  So that I can optimize my health outcomes and supplement routine

  Background:
    Given I am logged in as a user with health tracking data
    And I am on the health dashboard page

  Scenario: View health dashboard overview
    When I visit the health dashboard
    Then I should see the dashboard header with title "Health Dashboard"
    And I should see the description "Track your health metrics and discover correlations with your supplements"
    And I should see an "Add Metric" button
    And I should see filter controls for category and time range
    And I should see tab navigation with:
      | Tab               |
      | Overview          |
      | Health Metrics    |
      | Correlations      |
      | Connected Devices |

  Scenario: View health metrics cards
    When I view the health dashboard overview
    Then I should see health metric cards displaying:
      | Metric      | Value | Unit  | Status   | Trend |
      | Heart Rate  | 72    | bpm   | Optimal  | +2%   |
      | Sleep Hours | 7.5   | hours | Warning  | -10%  |
      | Weight      | 70.5  | kg    | Optimal  | +5%   |
    And each metric card should show:
      | Information         |
      | Metric name         |
      | Current value       |
      | Unit of measurement |
      | Target range        |
      | Status indicator    |
      | Trend arrow         |
      | Last updated time   |
      | Data source         |
      | Sync status         |

  Scenario: View health trends visualization
    When I view the health dashboard overview
    Then I should see interactive charts:
      | Chart Type | Title                   | Data Displayed           |
      | Line Chart | Health Metrics Trends   | Metrics over time        |
      | Bar Chart  | Metrics by Category     | Category distribution    |
    And charts should be responsive and interactive
    And I should be able to hover over data points for details

  Scenario: Navigate between dashboard tabs
    Given I am viewing the health dashboard
    When I click on the "Health Metrics" tab
    Then I should see all my health metrics in a grid layout
    And each metric should be displayed as a detailed card

    When I click on the "Correlations" tab
    Then I should see "Health-Supplement Correlations" section
    And I should see correlation cards showing:
      | Information              |
      | Health metric name       |
      | Supplement name          |
      | Correlation strength     |
      | Confidence level         |
      | Description              |

    When I click on the "Connected Devices" tab
    Then I should see "Wearable Device Integration" section
    And I should see supported devices:
      | Device          | Status      |
      | Fitbit          | Available   |
      | Apple Health    | Available   |
      | Google Fit      | Available   |
      | Garmin Connect  | Available   |
      | Samsung Health  | Available   |

  Scenario: Connect wearable devices
    Given I am on the "Connected Devices" tab
    When I click "Connect Fitbit" button
    Then I should see a device connection process
    And I should be redirected to Fitbit authorization
    Or I should see a connection modal

    When the device connection is successful
    Then I should see "Successfully connected to Fitbit" notification
    And the Fitbit card should show "Connected" status
    And I should see device metrics:
      | Metric          |
      | Metrics Synced  |
      | Data Points     |
      | Sync Frequency  |
      | Last Sync Time  |

  Scenario: Sync data from connected devices
    Given I have a connected Fitbit device
    When I click the "Sync Now" button on the Fitbit card
    Then I should see "Syncing..." status
    And the sync button should be disabled during sync

    When the sync completes successfully
    Then I should see "Device data synced successfully" notification
    And the "Last sync" time should be updated
    And new health metrics should appear in my dashboard

  Scenario: Manage health metrics manually
    When I click the "Add Metric" button
    Then I should see a metric creation form or modal
    And I should be able to select:
      | Field           | Options                                    |
      | Metric Type     | Heart Rate, Blood Pressure, Weight, etc.  |
      | Category        | Vitals, Fitness, Sleep, Nutrition, etc.   |
      | Unit            | bpm, mmHg, kg, hours, etc.                |
      | Target Range    | Min and max values                         |

    When I fill in the metric details:
      | Field        | Value           |
      | Metric Type  | Blood Pressure  |
      | Category     | Vitals          |
      | Value        | 120/80          |
      | Unit         | mmHg            |
      | Target Min   | 90/60           |
      | Target Max   | 140/90          |
    And I click "Save Metric"
    Then I should see "Health metric created successfully" notification
    And the new metric should appear in my dashboard

  Scenario: Edit existing health metrics
    Given I have health metrics in my dashboard
    When I click the edit button on a metric card
    Then I should see an edit modal with current values
    And I should be able to update the metric value

    When I change the value from "72" to "75"
    And I click "Save"
    Then I should see "Health metric updated successfully" notification
    And the metric card should show the updated value
    And the "Last updated" time should be current

  Scenario: Delete health metrics
    Given I have health metrics in my dashboard
    When I click the delete button on a metric card
    Then I should see a confirmation dialog "Are you sure you want to delete this health metric?"

    When I confirm the deletion
    Then I should see "Health metric deleted successfully" notification
    And the metric should be removed from my dashboard

  Scenario: Filter health metrics
    Given I have multiple health metrics across different categories
    When I select "Vital Signs" from the category filter
    Then I should see only metrics in the vitals category
    And other category metrics should be hidden

    When I select "Last 7 days" from the time range filter
    Then the metrics should update to show 7-day data
    And charts should reflect the new time range

    When I select "All Categories" to clear the filter
    Then all metrics should be visible again

  Scenario: View health-supplement correlations
    Given I have been tracking both health metrics and supplements
    When I view the correlations tab
    Then I should see correlation analysis results:
      | Health Metric | Supplement         | Strength | Description                                    |
      | Sleep Hours   | Magnesium Glycinate| 75%      | Strong positive correlation with sleep duration |
      | Heart Rate    | Omega-3            | -40%     | Moderate negative correlation with resting HR   |
    And each correlation should show:
      | Information        |
      | Correlation arrow  |
      | Strength percentage|
      | Confidence level   |
      | Time period        |
      | Data points used   |

  Scenario: Receive health insights and recommendations
    Given I have sufficient health and supplement data
    When I view my health dashboard
    Then I should see AI-powered insights such as:
      | Insight Type     | Example                                           |
      | Positive         | Your sleep quality has improved since starting magnesium |
      | Recommendation   | Consider taking omega-3 to help reduce heart rate        |
      | Pattern          | Your metrics show better consistency on weekdays         |
      | Alert            | Your blood pressure readings are trending upward         |

  Scenario: Handle device sync errors
    Given I have a connected device
    When I attempt to sync but the device is unavailable
    Then I should see "Failed to sync device data" error notification
    And I should have an option to retry the sync
    And the device status should show "Sync Failed"

    When I click "Retry Sync"
    Then the sync should be attempted again
    And I should see appropriate success or error feedback

  Scenario: Mobile health tracking experience
    Given I am using a mobile device
    When I view the health dashboard
    Then the layout should be mobile-optimized
    And metric cards should stack vertically
    And charts should be touch-friendly and responsive
    And all tabs should be easily accessible
    And device connection should work on mobile

  Scenario: Accessibility in health tracking
    Given I am using assistive technology
    When I navigate the health dashboard
    Then all health metrics should be accessible via keyboard
    And screen readers should announce metric values clearly
    And all interactive elements should have proper focus indicators
    And charts should have alternative text descriptions
    And device connection flows should be fully accessible

  Scenario: Data privacy and security
    When I connect wearable devices
    Then my health data should be encrypted in transit
    And I should only see my own health metrics
    And device connections should use secure OAuth flows
    And I should be able to disconnect devices at any time
    And disconnecting should revoke access to my data

  Scenario: Health data export
    When I want to export my health data
    Then I should have options to export:
      | Format | Content                           |
      | CSV    | Raw metric values and timestamps  |
      | PDF    | Health summary report             |
      | JSON   | Complete data with metadata       |
    And exported data should include:
      | Information          |
      | Metric values        |
      | Timestamps           |
      | Data sources         |
      | Correlation insights |
      | Trend analysis       |

  Scenario: Integration with supplement tracking
    Given I have both health metrics and supplement data
    When I view correlation analysis
    Then I should see how supplements affect my health metrics
    And I should receive recommendations for:
      | Recommendation Type    |
      | Optimal timing         |
      | Dosage adjustments     |
      | Supplement combinations|
      | Lifestyle changes      |

    When I make changes to my supplement routine
    Then I should see how it impacts my health metrics over time
    And I should receive updated correlation insights

  Scenario: Real-time health monitoring
    Given I have connected devices with real-time sync
    When new health data is available from my devices
    Then my dashboard should update automatically
    Or I should see a notification to refresh data
    And the "last updated" timestamp should reflect real-time sync

  Scenario: Health metric trends and predictions
    Given I have sufficient historical health data
    When I view trend analysis
    Then I should see:
      | Analysis Type      |
      | Trend direction    |
      | Seasonal patterns  |
      | Correlation trends |
      | Predictive insights|
    And I should receive alerts for:
      | Alert Type           |
      | Concerning trends    |
      | Goal achievements    |
      | Anomaly detection    |
      | Optimization opportunities |

  Scenario: Biomarker tracking integration
    When I add biomarker data from lab tests
    Then I should be able to track:
      | Biomarker Type    |
      | Vitamin D levels  |
      | B12 levels        |
      | Iron levels       |
      | Cholesterol       |
      | Blood glucose     |
    And I should see correlations with my supplement intake
    And I should receive recommendations based on biomarker results
