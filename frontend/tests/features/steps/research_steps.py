"""
Research Feature Step Definitions

Step definitions for research protocol creation and study participation features.
"""

from behave import given, when, then, step
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import Select
from selenium.common.exceptions import TimeoutException
import time


# Background steps
@given('I am logged in as a researcher')
def step_login_as_researcher(context):
    """Log in as a researcher user."""
    context.browser.get(f"{context.base_url}/login")
    
    # Fill login form
    email_input = context.browser.find_element(By.CSS_SELECTOR, '[data-testid="email-input"]')
    password_input = context.browser.find_element(By.CSS_SELECTOR, '[data-testid="password-input"]')
    login_button = context.browser.find_element(By.CSS_SELECTOR, '[data-testid="login-button"]')
    
    email_input.send_keys("<EMAIL>")
    password_input.send_keys("password123")
    login_button.click()
    
    # Wait for redirect to dashboard
    WebDriverWait(context.browser, 10).until(
        EC.url_contains("/dashboard")
    )


@given('I am logged in as a participant')
def step_login_as_participant(context):
    """Log in as a participant user."""
    context.browser.get(f"{context.base_url}/login")
    
    # Fill login form
    email_input = context.browser.find_element(By.CSS_SELECTOR, '[data-testid="email-input"]')
    password_input = context.browser.find_element(By.CSS_SELECTOR, '[data-testid="password-input"]')
    login_button = context.browser.find_element(By.CSS_SELECTOR, '[data-testid="login-button"]')
    
    email_input.send_keys("<EMAIL>")
    password_input.send_keys("password123")
    login_button.click()
    
    # Wait for redirect to dashboard
    WebDriverWait(context.browser, 10).until(
        EC.url_contains("/dashboard")
    )


@given('I am on the research hub page')
def step_navigate_to_research_hub(context):
    """Navigate to the research hub page."""
    research_nav = context.browser.find_element(By.CSS_SELECTOR, '[data-testid="nav-research"]')
    research_nav.click()
    
    # Wait for research page to load
    WebDriverWait(context.browser, 10).until(
        EC.presence_of_element_located((By.TAG_NAME, "h1"))
    )
    
    page_title = context.browser.find_element(By.TAG_NAME, "h1")
    assert "Research Hub" in page_title.text


@given('there are available research protocols')
def step_ensure_available_protocols(context):
    """Ensure there are research protocols available for testing."""
    # This would typically seed the database with test protocols
    # For now, we'll assume protocols exist
    pass


# Protocol Creation Steps
@when('I click the "Create Protocol" button')
def step_click_create_protocol(context):
    """Click the create protocol button."""
    create_button = WebDriverWait(context.browser, 10).until(
        EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Create Protocol')]"))
    )
    create_button.click()


@then('I should see the protocol creation wizard')
def step_verify_protocol_wizard(context):
    """Verify the protocol creation wizard is displayed."""
    WebDriverWait(context.browser, 10).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, ".step-indicator"))
    )


@then('I should be on the "{step_name}" step')
def step_verify_current_step(context, step_name):
    """Verify the current step in the wizard."""
    step_title = WebDriverWait(context.browser, 10).until(
        EC.presence_of_element_located((By.TAG_NAME, "h2"))
    )
    assert step_name in step_title.text


@when('I fill in the basic information')
def step_fill_basic_information(context):
    """Fill in basic protocol information from table."""
    for row in context.table:
        field = row['Field']
        value = row['Value']
        
        if field == "Title":
            title_input = context.browser.find_element(By.ID, "title")
            title_input.clear()
            title_input.send_keys(value)
        elif field == "Description":
            desc_input = context.browser.find_element(By.ID, "description")
            desc_input.clear()
            desc_input.send_keys(value)
        elif field == "Duration (weeks)":
            duration_input = context.browser.find_element(By.ID, "duration_weeks")
            duration_input.clear()
            duration_input.send_keys(value)
        elif field == "Max Participants":
            max_input = context.browser.find_element(By.ID, "max_participants")
            max_input.clear()
            max_input.send_keys(value)


@when('I add research objectives')
def step_add_research_objectives(context):
    """Add research objectives from table."""
    for i, row in enumerate(context.table):
        if i > 0:  # Add new objective field if not the first one
            add_button = context.browser.find_element(By.XPATH, "//button[contains(text(), 'Add Objective')]")
            add_button.click()
            time.sleep(0.5)  # Wait for field to be added
        
        objective_input = context.browser.find_element(By.NAME, f"objectives.{i}.value")
        objective_input.send_keys(row['Objective'])


@when('I add primary endpoints')
def step_add_primary_endpoints(context):
    """Add primary endpoints from table."""
    for i, row in enumerate(context.table):
        if i > 0:
            add_button = context.browser.find_element(By.XPATH, "//button[contains(text(), 'Add Primary Endpoint')]")
            add_button.click()
            time.sleep(0.5)
        
        endpoint_input = context.browser.find_element(By.NAME, f"primary_endpoints.{i}.value")
        endpoint_input.send_keys(row['Endpoint'])


@when('I add secondary endpoints')
def step_add_secondary_endpoints(context):
    """Add secondary endpoints from table."""
    for i, row in enumerate(context.table):
        add_button = context.browser.find_element(By.XPATH, "//button[contains(text(), 'Add Secondary Endpoint')]")
        add_button.click()
        time.sleep(0.5)
        
        endpoint_input = context.browser.find_element(By.NAME, f"secondary_endpoints.{i}.value")
        endpoint_input.send_keys(row['Endpoint'])


@when('I add inclusion criteria')
def step_add_inclusion_criteria(context):
    """Add inclusion criteria from table."""
    for i, row in enumerate(context.table):
        if i > 0:
            add_button = context.browser.find_element(By.XPATH, "//button[contains(text(), 'Add Inclusion Criterion')]")
            add_button.click()
            time.sleep(0.5)
        
        criteria_input = context.browser.find_element(By.NAME, f"inclusion_criteria.{i}.value")
        criteria_input.send_keys(row['Criterion'])


@when('I add exclusion criteria')
def step_add_exclusion_criteria(context):
    """Add exclusion criteria from table."""
    for i, row in enumerate(context.table):
        if i > 0:
            add_button = context.browser.find_element(By.XPATH, "//button[contains(text(), 'Add Exclusion Criterion')]")
            add_button.click()
            time.sleep(0.5)
        
        criteria_input = context.browser.find_element(By.NAME, f"exclusion_criteria.{i}.value")
        criteria_input.send_keys(row['Criterion'])


@when('I add supplements under study')
def step_add_supplements(context):
    """Add supplements from table."""
    for i, row in enumerate(context.table):
        if i > 0:
            add_button = context.browser.find_element(By.XPATH, "//button[contains(text(), 'Add Supplement')]")
            add_button.click()
            time.sleep(0.5)
        
        supplement_input = context.browser.find_element(By.NAME, f"supplements.{i}.value")
        supplement_input.send_keys(row['Supplement'])


@when('I fill in data collection schedule')
def step_fill_data_collection(context):
    """Fill in data collection schedule."""
    schedule_input = context.browser.find_element(By.ID, "data_collection_schedule")
    schedule_input.clear()
    schedule_input.send_keys(context.text)


@when('I fill in ethical considerations')
def step_fill_ethical_considerations(context):
    """Fill in ethical considerations."""
    ethics_input = context.browser.find_element(By.ID, "ethical_considerations")
    ethics_input.clear()
    ethics_input.send_keys(context.text)


@when('I fill in consent form URL: "{url}"')
def step_fill_consent_url(context, url):
    """Fill in consent form URL."""
    consent_input = context.browser.find_element(By.ID, "consent_form_url")
    consent_input.clear()
    consent_input.send_keys(url)


@when('I click "{button_text}"')
def step_click_button(context, button_text):
    """Click a button with specific text."""
    button = WebDriverWait(context.browser, 10).until(
        EC.element_to_be_clickable((By.XPATH, f"//button[contains(text(), '{button_text}')]"))
    )
    button.click()


@when('I review the protocol details')
def step_review_protocol_details(context):
    """Review the protocol details on the review step."""
    # Just verify we're on the review step
    review_heading = WebDriverWait(context.browser, 10).until(
        EC.presence_of_element_located((By.XPATH, "//h3[contains(text(), 'Protocol Review')]"))
    )
    assert review_heading.is_displayed()


@then('I should see all the information I entered')
def step_verify_entered_information(context):
    """Verify all entered information is displayed in review."""
    # This would check specific elements in the review section
    review_section = context.browser.find_element(By.CSS_SELECTOR, ".protocol-review")
    assert review_section.is_displayed()


@then('I should see "{text}" as the {field}')
def step_verify_field_value(context, text, field):
    """Verify a specific field value in the review."""
    if field == "title":
        title_element = context.browser.find_element(By.TAG_NAME, "h4")
        assert text in title_element.text
    elif field == "duration":
        duration_element = context.browser.find_element(By.XPATH, f"//text()[contains(., '{text}')]")
        assert duration_element
    elif field == "max participants":
        participants_element = context.browser.find_element(By.XPATH, f"//text()[contains(., '{text}')]")
        assert participants_element


@then('I should see a success notification "{message}"')
def step_verify_success_notification(context, message):
    """Verify success notification is displayed."""
    notification = WebDriverWait(context.browser, 10).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, ".notification"))
    )
    assert message in notification.text


@then('I should be redirected to the protocol details page')
def step_verify_protocol_details_redirect(context):
    """Verify redirect to protocol details page."""
    WebDriverWait(context.browser, 10).until(
        EC.url_matches(r".*/research/protocols/\w+")
    )


@then('the protocol status should be "{status}"')
def step_verify_protocol_status(context, status):
    """Verify the protocol status."""
    status_element = WebDriverWait(context.browser, 10).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, f"[data-status='{status}']"))
    )
    assert status_element.is_displayed()


# Validation Steps
@when('I try to proceed without filling required fields')
def step_skip_required_fields(context):
    """Skip filling required fields."""
    # Just proceed without filling anything
    pass


@then('I should see validation errors')
def step_verify_validation_errors(context):
    """Verify validation errors are displayed."""
    for row in context.table:
        field = row['Field']
        error_message = row['Error Message']
        
        error_element = WebDriverWait(context.browser, 5).until(
            EC.presence_of_element_located((By.XPATH, f"//text()[contains(., '{error_message}')]"))
        )
        assert error_element.is_displayed()


@then('I should remain on the "{step_name}" step')
def step_verify_remain_on_step(context, step_name):
    """Verify we remain on the specified step."""
    step_title = context.browser.find_element(By.TAG_NAME, "h2")
    assert step_name in step_title.text


# Study Participation Steps
@when('I visit the research hub')
def step_visit_research_hub(context):
    """Visit the research hub page."""
    context.browser.get(f"{context.base_url}/research")
    WebDriverWait(context.browser, 10).until(
        EC.presence_of_element_located((By.TAG_NAME, "h1"))
    )


@then('I should see a list of available research protocols')
def step_verify_protocol_list(context):
    """Verify research protocols are displayed."""
    protocol_cards = WebDriverWait(context.browser, 10).until(
        EC.presence_of_all_elements_located((By.CSS_SELECTOR, '[data-testid="protocol-card"]'))
    )
    assert len(protocol_cards) > 0


@then('each protocol should display')
def step_verify_protocol_information(context):
    """Verify each protocol displays required information."""
    protocol_card = context.browser.find_element(By.CSS_SELECTOR, '[data-testid="protocol-card"]')
    
    for row in context.table:
        info_type = row['Information']
        
        if info_type == "Protocol title":
            title = protocol_card.find_element(By.CSS_SELECTOR, "h3")
            assert title.is_displayed()
        elif info_type == "Study status":
            status = protocol_card.find_element(By.CSS_SELECTOR, "[data-status]")
            assert status.is_displayed()
        elif info_type == "Join button":
            join_button = protocol_card.find_element(By.XPATH, ".//button[contains(text(), 'Join Study')]")
            assert join_button.is_displayed()


@when('I search for "{search_term}"')
def step_search_protocols(context, search_term):
    """Search for protocols using the search term."""
    search_input = context.browser.find_element(By.CSS_SELECTOR, '[placeholder*="Search research protocols"]')
    search_input.clear()
    search_input.send_keys(search_term)
    time.sleep(1)  # Wait for debounce


@then('I should see only protocols related to {search_term}')
def step_verify_search_results(context, search_term):
    """Verify search results contain only relevant protocols."""
    protocol_cards = context.browser.find_elements(By.CSS_SELECTOR, '[data-testid="protocol-card"]')
    
    for card in protocol_cards:
        card_text = card.text.lower()
        assert search_term.lower() in card_text


@when('I filter by "{status}" status')
def step_filter_by_status(context, status):
    """Filter protocols by status."""
    status_select = Select(context.browser.find_element(By.TAG_NAME, "select"))
    status_select.select_by_visible_text(status.title())
    time.sleep(1)  # Wait for filter to apply


@given('there is a recruiting study "{study_name}"')
def step_ensure_recruiting_study(context, study_name):
    """Ensure a specific recruiting study exists."""
    # This would typically create or verify the study exists
    context.target_study = study_name


@when('I click "Join Study" for that protocol')
def step_click_join_study(context):
    """Click join study for the target protocol."""
    # Find the protocol card with the target study name
    protocol_cards = context.browser.find_elements(By.CSS_SELECTOR, '[data-testid="protocol-card"]')
    
    for card in protocol_cards:
        if context.target_study in card.text:
            join_button = card.find_element(By.XPATH, ".//button[contains(text(), 'Join Study')]")
            join_button.click()
            break
    else:
        raise AssertionError(f"Could not find study: {context.target_study}")


@when('I confirm joining the study')
def step_confirm_join_study(context):
    """Confirm joining the study in any confirmation dialog."""
    # If there's a confirmation dialog, confirm it
    try:
        confirm_button = WebDriverWait(context.browser, 5).until(
            EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Confirm') or contains(text(), 'Join')]"))
        )
        confirm_button.click()
    except TimeoutException:
        # No confirmation dialog, action already completed
        pass


@then('I should see a success message "{message}"')
def step_verify_success_message(context, message):
    """Verify success message is displayed."""
    success_notification = WebDriverWait(context.browser, 10).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, ".notification"))
    )
    assert message in success_notification.text


# Mobile and Accessibility Steps
@given('I am using a mobile device')
def step_set_mobile_viewport(context):
    """Set mobile viewport size."""
    context.browser.set_window_size(375, 667)


@given('I am using assistive technology')
def step_setup_assistive_technology(context):
    """Setup for assistive technology testing."""
    # This would configure screen reader simulation or other assistive tech
    pass


@then('the interface should be mobile-optimized')
def step_verify_mobile_optimization(context):
    """Verify the interface is optimized for mobile."""
    # Check that elements are properly sized and positioned for mobile
    main_content = context.browser.find_element(By.TAG_NAME, "main")
    assert main_content.is_displayed()
    
    # Verify responsive design elements
    viewport_width = context.browser.execute_script("return window.innerWidth")
    assert viewport_width <= 768  # Mobile breakpoint


@then('all study information should be accessible')
def step_verify_accessibility(context):
    """Verify accessibility compliance."""
    # Check for proper ARIA labels, roles, etc.
    buttons = context.browser.find_elements(By.TAG_NAME, "button")
    for button in buttons:
        # Verify buttons have accessible names
        accessible_name = button.get_attribute("aria-label") or button.text
        assert accessible_name.strip() != ""
