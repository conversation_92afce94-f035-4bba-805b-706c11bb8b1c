Feature: Research Protocol Creation
  As a researcher
  I want to create comprehensive research protocols
  So that I can conduct evidence-based supplement studies

  Background:
    Given I am logged in as a researcher
    And I am on the research hub page

  Scenario: Create a complete research protocol
    When I click the "Create Protocol" button
    Then I should see the protocol creation wizard
    And I should be on the "Basic Information" step

    When I fill in the basic information:
      | Field             | Value                                    |
      | Title             | Magnesium Sleep Quality Study           |
      | Description       | Investigating magnesium's effect on sleep |
      | Duration (weeks)  | 8                                       |
      | Max Participants  | 60                                      |
    And I click "Next"
    Then I should be on the "Objectives & Endpoints" step

    When I add research objectives:
      | Objective                           |
      | Measure sleep quality improvements  |
      | Assess sleep onset time changes     |
      | Monitor magnesium blood levels      |
    And I add primary endpoints:
      | Endpoint                    |
      | Pittsburgh Sleep Quality Index |
      | Sleep onset latency         |
    And I add secondary endpoints:
      | Endpoint                    |
      | Serum magnesium levels      |
      | Sleep efficiency percentage |
    And I click "Next"
    Then I should be on the "Participant Criteria" step

    When I add inclusion criteria:
      | Criterion                           |
      | Age 25-65 years                     |
      | Self-reported sleep difficulties    |
      | No current magnesium supplementation |
    And I add exclusion criteria:
      | Criterion                           |
      | Kidney disease                      |
      | Current sleep medication use        |
      | Pregnancy or nursing               |
    And I click "Next"
    Then I should be on the "Methodology" step

    When I add supplements under study:
      | Supplement              |
      | Magnesium Glycinate 400mg |
    And I fill in data collection schedule:
      """
      Daily sleep diary entries via mobile app.
      Weekly questionnaires on sleep quality.
      Blood draws at baseline, week 4, and week 8.
      """
    And I click "Next"
    Then I should be on the "Ethics & Consent" step

    When I fill in ethical considerations:
      """
      This study has been approved by the Institutional Review Board (IRB).
      All participants will provide informed consent before enrollment.
      Minimal risk intervention with well-established safety profile.
      Participants can withdraw at any time without penalty.
      """
    And I fill in consent form URL: "https://example.com/magnesium-consent.pdf"
    And I click "Next"
    Then I should be on the "Review & Submit" step

    When I review the protocol details
    Then I should see all the information I entered
    And I should see "Magnesium Sleep Quality Study" as the title
    And I should see "8 weeks" as the duration
    And I should see "60" as max participants

    When I click "Create Protocol"
    Then I should see a success notification "Research protocol created successfully"
    And I should be redirected to the protocol details page
    And the protocol status should be "draft"

  Scenario: Validate required fields in protocol creation
    When I click the "Create Protocol" button
    And I try to proceed without filling required fields
    And I click "Next"
    Then I should see validation errors:
      | Field       | Error Message           |
      | Title       | Title is required       |
      | Description | Description is required |
      | Duration    | Duration is required    |
    And I should remain on the "Basic Information" step

  Scenario: Navigate between protocol creation steps
    When I click the "Create Protocol" button
    And I fill in the title "Test Protocol Navigation"
    And I fill in the description "Testing step navigation"
    And I fill in the duration "4"
    And I click "Next"
    Then I should be on the "Objectives & Endpoints" step

    When I click "Previous"
    Then I should be on the "Basic Information" step
    And the title field should contain "Test Protocol Navigation"
    And the description field should contain "Testing step navigation"
    And the duration field should contain "4"

  Scenario: Cancel protocol creation
    When I click the "Create Protocol" button
    And I fill in some protocol information
    And I click "Cancel"
    Then I should be back on the research hub page
    And I should not see the protocol creation wizard

  Scenario: Add and remove dynamic fields
    When I click the "Create Protocol" button
    And I navigate to the "Objectives & Endpoints" step
    Then I should see 1 objective field

    When I click "Add Objective"
    Then I should see 2 objective fields

    When I fill in objective 1: "Primary research objective"
    And I fill in objective 2: "Secondary research objective"
    And I remove objective 2
    Then I should see 1 objective field
    And objective 1 should contain "Primary research objective"

  Scenario: Handle protocol creation with minimum required fields
    When I click the "Create Protocol" button
    And I fill in only the required fields:
      | Field             | Value                    |
      | Title             | Minimal Protocol         |
      | Description       | Basic protocol description |
      | Duration (weeks)  | 2                        |
    And I add one objective: "Test minimal protocol"
    And I add one primary endpoint: "Basic measurement"
    And I add one inclusion criterion: "Healthy adults"
    And I add one exclusion criterion: "Under 18 years"
    And I fill in basic data collection: "Weekly surveys"
    And I fill in basic ethical considerations: "IRB approved"
    And I complete the protocol creation
    Then the protocol should be created successfully
    And it should have the minimal required information

  Scenario: Create protocol with maximum fields
    When I click the "Create Protocol" button
    And I fill in all possible fields with comprehensive data
    And I add multiple objectives, endpoints, and criteria
    And I complete the protocol creation
    Then the protocol should be created successfully
    And it should contain all the comprehensive information

  Scenario: Handle protocol creation errors
    Given the API is temporarily unavailable
    When I try to create a complete protocol
    And I click "Create Protocol"
    Then I should see an error notification
    And I should remain on the review step
    And I should be able to retry the creation

  Scenario: Save draft and resume later
    When I click the "Create Protocol" button
    And I fill in partial protocol information
    And I navigate away from the page
    Then I should see a warning about unsaved changes
    
    When I confirm leaving the page
    And I return to create a new protocol
    Then the form should be empty
    And I should start fresh

  Scenario: Protocol creation accessibility
    When I click the "Create Protocol" button
    Then all form fields should have proper labels
    And I should be able to navigate using only the keyboard
    And screen reader announcements should be appropriate
    And error messages should be clearly associated with fields

  Scenario: Mobile protocol creation
    Given I am using a mobile device
    When I click the "Create Protocol" button
    Then the form should be mobile-friendly
    And all fields should be easily accessible
    And navigation buttons should be appropriately sized
    And the step indicator should be visible and clear
