{"name": "supplement-tracker-frontend", "version": "0.1.0", "private": true, "dependencies": {"@reduxjs/toolkit": "^2.0.1", "@tanstack/react-query": "^5.17.0", "@types/node": "^20.10.6", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-toast": "^1.1.5", "axios": "^1.6.5", "chart.js": "^4.4.1", "chartjs-adapter-date-fns": "^3.0.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "date-fns": "^3.2.0", "framer-motion": "^10.18.0", "i18next": "^23.7.16", "lucide-react": "^0.312.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-i18next": "^13.5.0", "react-query": "^3.39.3", "react-redux": "^9.0.4", "react-router-dom": "^6.21.1", "react-scripts": "5.0.1", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "styled-components": "^6.1.6", "tailwindcss": "^3.4.0", "typescript": "^5.3.3", "web-vitals": "^3.5.0", "zod": "^3.22.4", "zustand": "^4.4.7", "workbox-background-sync": "^7.0.0", "workbox-broadcast-update": "^7.0.0", "workbox-cacheable-response": "^7.0.0", "workbox-core": "^7.0.0", "workbox-expiration": "^7.0.0", "workbox-google-analytics": "^7.0.0", "workbox-navigation-preload": "^7.0.0", "workbox-precaching": "^7.0.0", "workbox-range-requests": "^7.0.0", "workbox-routing": "^7.0.0", "workbox-strategies": "^7.0.0", "workbox-streams": "^7.0.0"}, "devDependencies": {"@playwright/test": "^1.40.1", "@testing-library/jest-dom": "^6.1.6", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.11", "@types/styled-components": "^5.1.34", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.7.0", "prettier": "^3.1.1", "typescript": "^5.3.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "test:coverage": "react-scripts test --coverage --watchAll=false", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,css,md}", "type-check": "tsc --noEmit"}, "eslintConfig": {"extends": ["react-app", "react-app/jest", "prettier"], "rules": {"react-hooks/exhaustive-deps": "warn", "@typescript-eslint/no-unused-vars": "error"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"collectCoverageFrom": ["src/**/*.{ts,tsx}", "!src/**/*.d.ts", "!src/index.tsx", "!src/serviceWorker.ts"], "coverageThreshold": {"global": {"branches": 80, "functions": 80, "lines": 80, "statements": 80}}}}