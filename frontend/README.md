# Supplement Tracker Frontend

A modern React TypeScript application for the Supplement Tracker platform - an evidence-based supplement research and tracking system.

## 🚀 Features

### ✅ Implemented
- **Authentication System**: Complete login/register flow with JWT token management
- **Protected Routes**: Route-based authentication with automatic redirects
- **Redux State Management**: Centralized state with Redux Toolkit
- **Responsive Design**: Mobile-first responsive UI components
- **Theme System**: Light/dark theme support with styled-components
- **Type Safety**: Full TypeScript implementation
- **Testing**: Unit tests with Jest/RTL and E2E tests with <PERSON><PERSON>

### 🚧 In Development
- **Supplement Tracking**: Search, discovery, and intake logging
- **Research Tools**: Protocol management and participation
- **Community Features**: Posts, discussions, and peer reviews
- **Analytics Dashboard**: Data visualization and insights
- **PWA Features**: Offline support and push notifications

## 🛠 Tech Stack

- **Framework**: React 18 with TypeScript
- **State Management**: Redux Toolkit + React Query
- **Styling**: Styled Components with theme system
- **Routing**: React Router v6
- **Forms**: React Hook Form with validation
- **Testing**: Jest, React Testing Library, Playwright
- **Build Tool**: Create React App (CRA)

## 📁 Project Structure

```
frontend/
├── public/                 # Static assets
├── src/
│   ├── components/        # Reusable UI components
│   │   ├── auth/         # Authentication components
│   │   ├── common/       # Common UI components
│   │   └── layout/       # Layout components
│   ├── pages/            # Page components
│   │   ├── auth/         # Login/Register pages
│   │   ├── dashboard/    # Dashboard page
│   │   ├── supplements/  # Supplement pages
│   │   ├── research/     # Research pages
│   │   ├── community/    # Community pages
│   │   └── analytics/    # Analytics pages
│   ├── store/            # Redux store and slices
│   │   └── slices/       # Redux slices
│   ├── services/         # API services
│   ├── hooks/            # Custom React hooks
│   ├── types/            # TypeScript type definitions
│   ├── styles/           # Global styles and themes
│   └── utils/            # Utility functions
├── tests/                # E2E tests (Playwright)
└── package.json
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ and npm
- Backend API running on `http://localhost:8000`

### Installation

1. **Install dependencies**:
   ```bash
   cd frontend
   npm install
   ```

2. **Set up environment variables**:
   ```bash
   cp .env.example .env.local
   ```
   
   Configure your `.env.local`:
   ```
   REACT_APP_API_BASE_URL=http://localhost:8000/api/v1
   REACT_APP_ENVIRONMENT=development
   ```

3. **Start development server**:
   ```bash
   npm start
   ```
   
   The app will be available at `http://localhost:3000`

## 🧪 Testing

### Unit Tests
```bash
# Run all unit tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm test -- --watch
```

### E2E Tests
```bash
# Install Playwright browsers (first time only)
npx playwright install

# Run E2E tests
npm run test:e2e

# Run E2E tests with UI
npm run test:e2e:ui

# Run specific test file
npx playwright test tests/auth/login.spec.ts
```

## 🎨 UI Components

### Common Components
- **Button**: Multi-variant button with loading states
- **Input**: Form input with validation styling
- **Card**: Content container with shadow variants
- **LoadingSpinner**: Animated loading indicator
- **NotificationContainer**: Global notification system
- **ModalContainer**: Global modal system

### Authentication Components
- **LoginPage**: User login with validation
- **RegisterPage**: User registration with password strength
- **ProtectedRoute**: Route protection wrapper

### Layout Components
- **Layout**: Main application layout
- **Sidebar**: Navigation sidebar (coming soon)
- **Header**: Application header (coming soon)

## 🔄 State Management

### Redux Slices
- **authSlice**: Authentication state and actions
- **supplementSlice**: Supplement data and tracking
- **researchSlice**: Research protocols and participation
- **communitySlice**: Community posts and interactions
- **uiSlice**: UI state (modals, notifications, theme)

### API Integration
- **apiClient**: Axios-based API client with interceptors
- **authService**: Authentication API calls
- **supplementService**: Supplement-related API calls
- **researchService**: Research-related API calls
- **communityService**: Community-related API calls

## 🎯 User Workflows

### Authentication Flow
1. User visits protected route
2. Redirected to login page
3. Enters credentials and submits
4. JWT token stored and user redirected
5. Subsequent requests include auth header

### Supplement Tracking Flow
1. Search/browse supplements
2. Select supplement and dosage
3. Log intake with timestamp
4. View intake history and analytics
5. Export data for research

### Research Participation Flow
1. Browse available studies
2. Review study details and criteria
3. Join study and provide consent
4. Complete study tasks and surveys
5. View study progress and results

## 🔧 Development Guidelines

### Code Style
- Use TypeScript for all new code
- Follow React functional component patterns
- Use styled-components for styling
- Implement proper error boundaries
- Write tests for all new features

### Testing Strategy
- **Unit Tests**: Component logic and user interactions
- **Integration Tests**: API integration and state management
- **E2E Tests**: Complete user workflows and critical paths
- **Visual Tests**: Component appearance and responsive design

### Performance
- Lazy load route components
- Optimize bundle size with code splitting
- Use React.memo for expensive components
- Implement proper loading states
- Cache API responses with React Query

## 🚀 Deployment

### Build for Production
```bash
npm run build
```

### Environment Configuration
- **Development**: Local API, debug logging
- **Staging**: Staging API, limited logging
- **Production**: Production API, error tracking

### CI/CD Pipeline
1. **Lint and Type Check**: ESLint and TypeScript
2. **Unit Tests**: Jest and React Testing Library
3. **E2E Tests**: Playwright across browsers
4. **Build**: Production bundle creation
5. **Deploy**: Static hosting deployment

## 📊 Monitoring and Analytics

### Error Tracking
- Runtime error boundaries
- API error logging
- User action tracking

### Performance Monitoring
- Core Web Vitals tracking
- Bundle size monitoring
- API response time tracking

## 🤝 Contributing

1. **Fork the repository**
2. **Create feature branch**: `git checkout -b feature/amazing-feature`
3. **Write tests** for new functionality
4. **Ensure all tests pass**: `npm test && npm run test:e2e`
5. **Commit changes**: `git commit -m 'Add amazing feature'`
6. **Push to branch**: `git push origin feature/amazing-feature`
7. **Open Pull Request**

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the `/docs` folder
- **Issues**: Create GitHub issues for bugs
- **Discussions**: Use GitHub discussions for questions
- **Email**: Contact the development team

---

**Built with ❤️ for evidence-based supplement research**
