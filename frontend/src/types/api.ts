/**
 * API Types and Interfaces
 * 
 * This file contains all TypeScript interfaces and types for API communication
 * with the Supplement Tracker backend.
 */

// Base API Response Types
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

// Authentication Types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  full_name: string;
  username?: string;
}

export interface AuthResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  user: User;
}

// User Types
export interface User {
  id: string;
  email: string;
  full_name: string;
  username?: string;
  is_active: boolean;
  is_superuser: boolean;
  created_at: string;
  updated_at: string;
  profile?: UserProfile;
}

export interface UserProfile {
  bio?: string;
  date_of_birth?: string;
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say';
  height_cm?: number;
  weight_kg?: number;
  activity_level?: 'sedentary' | 'lightly_active' | 'moderately_active' | 'very_active' | 'extremely_active';
  health_goals?: string[];
  medical_conditions?: string[];
  allergies?: string[];
}

// Supplement Types
export interface Supplement {
  id: string;
  name: string;
  brand?: string;
  description?: string;
  category: string;
  serving_size?: string;
  serving_unit?: string;
  ingredients?: string[];
  barcode?: string;
  image_url?: string;
  created_at: string;
  updated_at: string;
  created_by_user_id?: string;
}

export interface SupplementIntake {
  id: string;
  user_id: string;
  supplement_id: string;
  supplement: Supplement;
  dosage: number;
  dosage_unit: string;
  taken_at: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface SupplementIntakeCreate {
  dosage: number;
  dosage_unit: string;
  taken_at?: string;
  notes?: string;
}

// Research Types
export interface ResearchProtocol {
  id: string;
  title: string;
  description: string;
  objectives: string[];
  methodology: string;
  inclusion_criteria: string[];
  exclusion_criteria: string[];
  duration_weeks: number;
  status: 'draft' | 'recruiting' | 'active' | 'completed' | 'cancelled';
  created_by_user_id: string;
  created_by_user: User;
  participant_count: number;
  max_participants?: number;
  created_at: string;
  updated_at: string;
}

export interface StudyParticipant {
  id: string;
  protocol_id: string;
  user_id: string;
  user: User;
  status: 'pending' | 'active' | 'completed' | 'withdrawn';
  enrolled_at: string;
  completed_at?: string;
  notes?: string;
}

export interface LiteratureReference {
  id: string;
  title: string;
  authors: string[];
  journal?: string;
  publication_date?: string;
  doi?: string;
  pubmed_id?: string;
  url?: string;
  abstract?: string;
  tags: string[];
  added_by_user_id: string;
  created_at: string;
  updated_at: string;
}

// Community Types
export interface CommunityPost {
  id: string;
  title: string;
  content: string;
  post_type: 'discussion' | 'question' | 'experience' | 'research';
  author_id: string;
  author: User;
  group_id?: string;
  tags: string[];
  upvotes: number;
  downvotes: number;
  comment_count: number;
  view_count: number;
  created_at: string;
  updated_at: string;
}

export interface PostComment {
  id: string;
  post_id: string;
  author_id: string;
  author: User;
  content: string;
  parent_comment_id?: string;
  upvotes: number;
  downvotes: number;
  created_at: string;
  updated_at: string;
}

export interface CommunityGroup {
  id: string;
  name: string;
  description: string;
  group_type: 'public' | 'private' | 'research';
  member_count: number;
  created_by_user_id: string;
  created_at: string;
  updated_at: string;
}

export interface UserFollow {
  id: string;
  follower_id: string;
  following_id: string;
  created_at: string;
}

// Analytics Types
export interface AnalyticsData {
  supplement_trends: SupplementTrend[];
  correlation_insights: CorrelationInsight[];
  health_metrics: HealthMetric[];
  usage_statistics: UsageStatistic[];
}

export interface SupplementTrend {
  supplement_id: string;
  supplement_name: string;
  intake_frequency: number;
  average_dosage: number;
  trend_direction: 'increasing' | 'decreasing' | 'stable';
  period_days: number;
}

export interface CorrelationInsight {
  supplement_a: string;
  supplement_b: string;
  correlation_coefficient: number;
  significance_level: number;
  sample_size: number;
  insight_type: 'positive' | 'negative' | 'neutral';
}

export interface HealthMetric {
  metric_name: string;
  value: number;
  unit: string;
  recorded_at: string;
  trend: 'improving' | 'declining' | 'stable';
}

export interface UsageStatistic {
  date: string;
  supplements_taken: number;
  unique_supplements: number;
  total_dosage: number;
}

// API Error Types
export interface ApiError {
  detail: string;
  status_code: number;
  error_type?: string;
}

// Form Types
export interface SearchFilters {
  search?: string;
  category?: string;
  brand?: string;
  sort_by?: 'name' | 'created_at' | 'popularity';
  sort_order?: 'asc' | 'desc';
}

export interface DateRange {
  start_date: string;
  end_date: string;
}

// Notification Types
export interface Notification {
  id: string;
  user_id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  read: boolean;
  created_at: string;
  action_url?: string;
}
