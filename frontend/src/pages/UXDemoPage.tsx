/**
 * UX Demo Page
 * 
 * Showcases the new UX components for the Supplement Tracker
 */

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import Dashboard from '@/components/Dashboard';
import SupplementTracker from '@/components/SupplementTracker';
import ResearchHub from '@/components/ResearchHub';
import { 
  Activity, 
  Pill, 
  FlaskConical, 
  ArrowRight,
  CheckCircle,
  Sparkles
} from 'lucide-react';

const UXDemoPage: React.FC = () => {
  const [activeDemo, setActiveDemo] = useState<'overview' | 'dashboard' | 'tracker' | 'research'>('overview');

  const mockUser = {
    name: '<PERSON>',
    email: '<EMAIL>',
    joinDate: '2024-01-15'
  };

  const demos = [
    {
      id: 'dashboard',
      title: 'Dashboard',
      description: 'Central hub with quick stats, today\'s supplements, and active research',
      icon: Activity,
      color: 'bg-blue-500',
      features: ['Quick stats overview', 'Today\'s supplement schedule', 'Active research progress', 'Quick actions']
    },
    {
      id: 'tracker',
      title: 'Supplement Tracker',
      description: 'Comprehensive supplement logging and schedule management',
      icon: Pill,
      color: 'bg-green-500',
      features: ['Daily intake logging', 'Supplement schedule', 'Intake history', 'Streak tracking']
    },
    {
      id: 'research',
      title: 'Research Hub',
      description: 'Discover and participate in supplement research studies',
      icon: FlaskConical,
      color: 'bg-purple-500',
      features: ['Study discovery', 'Research participation', 'Progress tracking', 'Study creation']
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {activeDemo === 'overview' && (
        <div className="p-6">
          <div className="max-w-6xl mx-auto">
            {/* Header */}
            <div className="text-center mb-12">
              <div className="flex items-center justify-center mb-4">
                <Sparkles className="h-12 w-12 text-blue-600 mr-3" />
                <h1 className="text-4xl font-bold text-gray-900 dark:text-white">
                  UX Demo
                </h1>
              </div>
              <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Experience the new Supplement Tracker user interface with modern MVP components
              </p>
              <div className="flex items-center justify-center mt-4 space-x-2">
                <Badge variant="secondary">
                  <CheckCircle className="w-3 h-3 mr-1" />
                  UX Implementation Complete
                </Badge>
                <Badge variant="outline">React + TypeScript</Badge>
                <Badge variant="default">MVP Components</Badge>
              </div>
            </div>

            {/* Demo Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
              {demos.map((demo) => {
                const IconComponent = demo.icon;
                return (
                  <Card key={demo.id} className="p-6 hover:shadow-lg transition-shadow">
                    <div className="flex items-center mb-4">
                      <div className={`p-3 ${demo.color} rounded-lg mr-4`}>
                        <IconComponent className="h-6 w-6 text-white" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                        {demo.title}
                      </h3>
                    </div>
                    
                    <p className="text-gray-600 dark:text-gray-300 mb-4">
                      {demo.description}
                    </p>
                    
                    <div className="space-y-2 mb-6">
                      {demo.features.map((feature, index) => (
                        <div key={index} className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                          <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                          {feature}
                        </div>
                      ))}
                    </div>
                    
                    <Button 
                      onClick={() => setActiveDemo(demo.id as any)}
                      className="w-full"
                    >
                      View {demo.title}
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </Card>
                );
              })}
            </div>

            {/* Features Overview */}
            <Card className="p-8 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20">
              <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6 text-center">
                UX Implementation Highlights
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600 mb-2">3</div>
                  <div className="text-sm text-gray-600 dark:text-gray-300">Core UX Components</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600 mb-2">50+</div>
                  <div className="text-sm text-gray-600 dark:text-gray-300">MVP UI Components</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-600 mb-2">100%</div>
                  <div className="text-sm text-gray-600 dark:text-gray-300">Responsive Design</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-orange-600 mb-2">WCAG</div>
                  <div className="text-sm text-gray-600 dark:text-gray-300">AA Accessible</div>
                </div>
              </div>
            </Card>

            {/* Navigation */}
            <div className="mt-8 text-center">
              <p className="text-gray-600 dark:text-gray-300 mb-4">
                Select a component above to see it in action, or
              </p>
              <Button variant="outline" asChild>
                <a href="/">
                  Return to Main App
                </a>
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Component Demos */}
      {activeDemo === 'dashboard' && (
        <div>
          <div className="bg-white dark:bg-gray-800 border-b p-4">
            <div className="max-w-6xl mx-auto flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Dashboard Demo
              </h2>
              <Button variant="outline" onClick={() => setActiveDemo('overview')}>
                Back to Overview
              </Button>
            </div>
          </div>
          <Dashboard user={mockUser} />
        </div>
      )}

      {activeDemo === 'tracker' && (
        <div>
          <div className="bg-white dark:bg-gray-800 border-b p-4">
            <div className="max-w-6xl mx-auto flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Supplement Tracker Demo
              </h2>
              <Button variant="outline" onClick={() => setActiveDemo('overview')}>
                Back to Overview
              </Button>
            </div>
          </div>
          <SupplementTracker />
        </div>
      )}

      {activeDemo === 'research' && (
        <div>
          <div className="bg-white dark:bg-gray-800 border-b p-4">
            <div className="max-w-6xl mx-auto flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Research Hub Demo
              </h2>
              <Button variant="outline" onClick={() => setActiveDemo('overview')}>
                Back to Overview
              </Button>
            </div>
          </div>
          <ResearchHub />
        </div>
      )}
    </div>
  );
};

export default UXDemoPage;
