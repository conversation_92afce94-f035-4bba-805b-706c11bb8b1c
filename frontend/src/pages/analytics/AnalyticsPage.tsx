/**
 * Analytics Page Component
 *
 * Main analytics page with comprehensive supplement tracking insights and data visualization.
 */

import React, { useEffect } from 'react';
import { useAppDispatch } from '@/store';
import { setPageTitle, setBreadcrumbs } from '@/store/slices/uiSlice';
import styled from 'styled-components';

// Components
import AnalyticsDashboard from '@/components/analytics/AnalyticsDashboard';

// Styled components
const PageContainer = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
`;

const AnalyticsPage: React.FC = () => {
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(setPageTitle('Analytics'));
    dispatch(setBreadcrumbs([
      { label: 'Dashboard', href: '/dashboard' },
      { label: 'Analytics' },
    ]));
  }, [dispatch]);

  return (
    <PageContainer>
      <AnalyticsDashboard />
    </PageContainer>
  );
};

export default AnalyticsPage;
