/**
 * Register Page Component
 * 
 * User registration page with form validation and account creation.
 */

import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import styled from 'styled-components';
import { useAppDispatch, useAppSelector } from '@/store';
import { registerUser, clearError, selectAuthLoading, selectAuthError } from '@/store/slices/authSlice';
import { showErrorNotification, showSuccessNotification } from '@/store/slices/uiSlice';
import { RegisterRequest } from '@/types/api';

// Components
import Button from '@/components/common/Button';
import Input from '@/components/common/Input';
import Card from '@/components/common/Card';
import LoadingSpinner from '@/components/common/LoadingSpinner';

// Styled components (reusing from LoginPage with modifications)
const RegisterContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, ${props => props.theme.colors.primary}20, ${props => props.theme.colors.secondary}20);
  padding: 1rem;
`;

const RegisterCard = styled(Card)`
  width: 100%;
  max-width: 450px;
  padding: 2rem;
`;

const Logo = styled.div`
  text-align: center;
  margin-bottom: 2rem;
  
  h1 {
    color: ${props => props.theme.colors.primary};
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }
  
  p {
    color: ${props => props.theme.colors.textSecondary};
    font-size: 0.9rem;
  }
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const Label = styled.label`
  font-weight: 500;
  color: ${props => props.theme.colors.text};
  font-size: 0.9rem;
`;

const ErrorMessage = styled.div`
  color: ${props => props.theme.colors.error};
  font-size: 0.8rem;
  margin-top: 0.25rem;
`;

const PasswordStrength = styled.div<{ strength: number }>`
  height: 4px;
  background: ${props => props.theme.colors.border};
  border-radius: 2px;
  margin-top: 0.5rem;
  overflow: hidden;
  
  &::after {
    content: '';
    display: block;
    height: 100%;
    width: ${props => props.strength}%;
    background: ${props => {
      if (props.strength < 30) return props.theme.colors.error;
      if (props.strength < 60) return props.theme.colors.warning;
      return props.theme.colors.success;
    }};
    transition: width 0.3s ease;
  }
`;

const PasswordHint = styled.div`
  font-size: 0.8rem;
  color: ${props => props.theme.colors.textSecondary};
  margin-top: 0.25rem;
`;

const TermsCheckbox = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  margin: 0.5rem 0;
  
  input[type="checkbox"] {
    margin-top: 0.2rem;
  }
  
  label {
    font-size: 0.9rem;
    color: ${props => props.theme.colors.textSecondary};
    line-height: 1.4;
    
    a {
      color: ${props => props.theme.colors.primary};
      text-decoration: none;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
`;

const Footer = styled.div`
  text-align: center;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid ${props => props.theme.colors.border};
  
  p {
    color: ${props => props.theme.colors.textSecondary};
    font-size: 0.9rem;
    
    a {
      color: ${props => props.theme.colors.primary};
      text-decoration: none;
      font-weight: 500;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
`;

// Form validation rules
const validationRules = {
  email: {
    required: 'Email is required',
    pattern: {
      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
      message: 'Invalid email address',
    },
  },
  full_name: {
    required: 'Full name is required',
    minLength: {
      value: 2,
      message: 'Full name must be at least 2 characters',
    },
  },
  username: {
    minLength: {
      value: 3,
      message: 'Username must be at least 3 characters',
    },
    pattern: {
      value: /^[a-zA-Z0-9_]+$/,
      message: 'Username can only contain letters, numbers, and underscores',
    },
  },
  password: {
    required: 'Password is required',
    minLength: {
      value: 8,
      message: 'Password must be at least 8 characters',
    },
  },
  confirmPassword: {
    required: 'Please confirm your password',
  },
  terms: {
    required: 'You must accept the terms and conditions',
  },
};

// Password strength calculation
const calculatePasswordStrength = (password: string): number => {
  let strength = 0;
  if (password.length >= 8) strength += 25;
  if (password.match(/[a-z]/)) strength += 25;
  if (password.match(/[A-Z]/)) strength += 25;
  if (password.match(/[0-9]/)) strength += 15;
  if (password.match(/[^a-zA-Z0-9]/)) strength += 10;
  return Math.min(strength, 100);
};

interface RegisterFormData extends RegisterRequest {
  confirmPassword: string;
  terms: boolean;
}

const RegisterPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const isLoading = useAppSelector(selectAuthLoading);
  const error = useAppSelector(selectAuthError);
  const [passwordStrength, setPasswordStrength] = useState(0);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setFocus,
  } = useForm<RegisterFormData>();

  const watchPassword = watch('password', '');
  const watchConfirmPassword = watch('confirmPassword', '');

  // Clear errors on component mount
  useEffect(() => {
    dispatch(clearError());
    setFocus('full_name');
  }, [dispatch, setFocus]);

  // Update password strength
  useEffect(() => {
    setPasswordStrength(calculatePasswordStrength(watchPassword));
  }, [watchPassword]);

  // Show error notification when error changes
  useEffect(() => {
    if (error) {
      dispatch(showErrorNotification(error, 'Registration Failed'));
    }
  }, [error, dispatch]);

  const onSubmit = async (data: RegisterFormData) => {
    // Check password confirmation
    if (data.password !== data.confirmPassword) {
      dispatch(showErrorNotification('Passwords do not match'));
      return;
    }

    try {
      const registerData: RegisterRequest = {
        email: data.email,
        password: data.password,
        full_name: data.full_name,
        username: data.username || undefined,
      };

      await dispatch(registerUser(registerData)).unwrap();
      
      dispatch(showSuccessNotification('Account created successfully! Welcome to Supplement Tracker.'));
      navigate('/dashboard', { replace: true });
    } catch (error) {
      // Error is handled by the slice and notification
      console.error('Registration failed:', error);
    }
  };

  return (
    <RegisterContainer>
      <RegisterCard>
        <Logo>
          <h1>Join Supplement Tracker</h1>
          <p>Start your evidence-based supplement journey</p>
        </Logo>

        <Form onSubmit={handleSubmit(onSubmit)}>
          <FormGroup>
            <Label htmlFor="full_name">Full Name</Label>
            <Input
              id="full_name"
              type="text"
              placeholder="Enter your full name"
              {...register('full_name', validationRules.full_name)}
              error={!!errors.full_name}
              disabled={isLoading}
            />
            {errors.full_name && (
              <ErrorMessage>{errors.full_name.message}</ErrorMessage>
            )}
          </FormGroup>

          <FormGroup>
            <Label htmlFor="email">Email Address</Label>
            <Input
              id="email"
              type="email"
              placeholder="Enter your email"
              {...register('email', validationRules.email)}
              error={!!errors.email}
              disabled={isLoading}
            />
            {errors.email && (
              <ErrorMessage>{errors.email.message}</ErrorMessage>
            )}
          </FormGroup>

          <FormGroup>
            <Label htmlFor="username">Username (Optional)</Label>
            <Input
              id="username"
              type="text"
              placeholder="Choose a username"
              {...register('username', validationRules.username)}
              error={!!errors.username}
              disabled={isLoading}
            />
            {errors.username && (
              <ErrorMessage>{errors.username.message}</ErrorMessage>
            )}
          </FormGroup>

          <FormGroup>
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              type="password"
              placeholder="Create a strong password"
              {...register('password', validationRules.password)}
              error={!!errors.password}
              disabled={isLoading}
            />
            {errors.password && (
              <ErrorMessage>{errors.password.message}</ErrorMessage>
            )}
            {watchPassword && (
              <>
                <PasswordStrength strength={passwordStrength} />
                <PasswordHint>
                  Password strength: {passwordStrength < 30 ? 'Weak' : passwordStrength < 60 ? 'Medium' : 'Strong'}
                </PasswordHint>
              </>
            )}
          </FormGroup>

          <FormGroup>
            <Label htmlFor="confirmPassword">Confirm Password</Label>
            <Input
              id="confirmPassword"
              type="password"
              placeholder="Confirm your password"
              {...register('confirmPassword', validationRules.confirmPassword)}
              error={!!errors.confirmPassword || (watchConfirmPassword && watchPassword !== watchConfirmPassword)}
              disabled={isLoading}
            />
            {errors.confirmPassword && (
              <ErrorMessage>{errors.confirmPassword.message}</ErrorMessage>
            )}
            {watchConfirmPassword && watchPassword !== watchConfirmPassword && (
              <ErrorMessage>Passwords do not match</ErrorMessage>
            )}
          </FormGroup>

          <TermsCheckbox>
            <input
              type="checkbox"
              id="terms"
              {...register('terms', validationRules.terms)}
              disabled={isLoading}
            />
            <label htmlFor="terms">
              I agree to the{' '}
              <Link to="/terms" target="_blank">Terms of Service</Link>
              {' '}and{' '}
              <Link to="/privacy" target="_blank">Privacy Policy</Link>
            </label>
          </TermsCheckbox>
          {errors.terms && (
            <ErrorMessage>{errors.terms.message}</ErrorMessage>
          )}

          <Button
            type="submit"
            variant="primary"
            size="large"
            disabled={isLoading || passwordStrength < 30}
            fullWidth
          >
            {isLoading ? <LoadingSpinner size="small" /> : 'Create Account'}
          </Button>
        </Form>

        <Footer>
          <p>
            Already have an account?{' '}
            <Link to="/login">Sign in here</Link>
          </p>
        </Footer>
      </RegisterCard>
    </RegisterContainer>
  );
};

export default RegisterPage;
