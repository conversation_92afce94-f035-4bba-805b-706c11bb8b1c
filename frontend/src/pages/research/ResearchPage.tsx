/**
 * Research Page Component
 *
 * Main research hub with protocol discovery and management.
 */

import React, { useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from '@/store';
import {
  fetchResearchProtocols,
  updateFilters,
  selectResearchProtocols,
  selectResearchLoading,
  selectResearchFilters
} from '@/store/slices/researchSlice';
import { setPageTitle, setBreadcrumbs, openModal } from '@/store/slices/uiSlice';
import { selectUser } from '@/store/slices/authSlice';
import styled from 'styled-components';

// Components
import Button from '@/components/common/Button';
import Input from '@/components/common/Input';
import Card from '@/components/common/Card';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import ResearchProtocolCard from '@/components/research/ResearchProtocolCard';

// Icons
const SearchIcon = () => <span>🔍</span>;
const PlusIcon = () => <span>+</span>;
const FilterIcon = () => <span>🔽</span>;
const ClearIcon = () => <span>✕</span>;

// Styled components
const PageContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
`;

const PageHeader = styled.div`
  margin-bottom: 2rem;

  h1 {
    color: ${props => props.theme.colors.text};
    margin: 0 0 0.5rem 0;
    font-size: 2rem;
  }

  p {
    color: ${props => props.theme.colors.textSecondary};
    font-size: 1.1rem;
    margin: 0;
  }
`;

const ActionBar = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;

  @media (max-width: ${props => props.theme.breakpoints.tablet}) {
    flex-direction: column;
    align-items: stretch;
  }
`;

const SearchSection = styled.div`
  display: flex;
  gap: 1rem;
  flex: 1;

  @media (max-width: ${props => props.theme.breakpoints.mobile}) {
    flex-direction: column;
  }
`;

const SearchInput = styled(Input)`
  flex: 1;
  max-width: 400px;
`;

const FilterSelect = styled.select`
  padding: 0.75rem 1rem;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.medium};
  background: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
  font-size: 0.9375rem;
  min-width: 150px;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 2px ${props => props.theme.colors.primary}20;
  }
`;

const CreateButton = styled(Button)`
  white-space: nowrap;
`;

const StatsSection = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
`;

const StatCard = styled(Card)`
  padding: 1.5rem;
  text-align: center;

  .stat-value {
    font-size: 2rem;
    font-weight: ${props => props.theme.typography.fontWeight.bold};
    color: ${props => props.theme.colors.primary};
    margin-bottom: 0.5rem;
  }

  .stat-label {
    color: ${props => props.theme.colors.textSecondary};
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
`;

const ProtocolsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 2rem;

  @media (max-width: ${props => props.theme.breakpoints.mobile}) {
    grid-template-columns: 1fr;
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 4rem 2rem;

  h3 {
    color: ${props => props.theme.colors.text};
    margin-bottom: 1rem;
  }

  p {
    color: ${props => props.theme.colors.textSecondary};
    margin-bottom: 2rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
`;

// Protocol status options
const STATUS_OPTIONS = [
  { value: '', label: 'All Statuses' },
  { value: 'recruiting', label: 'Recruiting' },
  { value: 'active', label: 'Active' },
  { value: 'completed', label: 'Completed' },
  { value: 'draft', label: 'Draft' },
];

const ResearchPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const protocols = useAppSelector(selectResearchProtocols);
  const isLoading = useAppSelector(selectResearchLoading);
  const filters = useAppSelector(selectResearchFilters);
  const currentUser = useAppSelector(selectUser);

  const [localSearch, setLocalSearch] = useState(filters.search);

  useEffect(() => {
    dispatch(setPageTitle('Research Hub'));
    dispatch(setBreadcrumbs([
      { label: 'Dashboard', href: '/dashboard' },
      { label: 'Research' },
    ]));
  }, [dispatch]);

  useEffect(() => {
    dispatch(fetchResearchProtocols({
      status: filters.status,
      search: filters.search,
      skip: 0,
      limit: 20,
    }));
  }, [filters, dispatch]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setLocalSearch(value);

    // Debounce search
    const timeoutId = setTimeout(() => {
      dispatch(updateFilters({ search: value }));
    }, 300);

    return () => clearTimeout(timeoutId);
  };

  const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    dispatch(updateFilters({ status: e.target.value }));
  };

  const handleClearFilters = () => {
    setLocalSearch('');
    dispatch(updateFilters({ search: '', status: '' }));
  };

  const handleCreateProtocol = () => {
    if (!currentUser) {
      // Redirect to login or show message
      return;
    }

    dispatch(openModal({
      component: 'protocol-builder',
      props: {},
      size: 'large',
    }));
  };

  const handleJoinProtocol = async (protocolId: string) => {
    // This would be implemented with the join protocol API
    console.log('Joining protocol:', protocolId);
  };

  const hasActiveFilters = filters.search || filters.status;

  // Calculate stats
  const stats = {
    total: protocols.length,
    recruiting: protocols.filter(p => p.status === 'recruiting').length,
    active: protocols.filter(p => p.status === 'active').length,
    completed: protocols.filter(p => p.status === 'completed').length,
  };

  return (
    <PageContainer>
      <PageHeader>
        <h1>Research Hub</h1>
        <p>
          Discover research studies, create protocols, and contribute to evidence-based supplement research
        </p>
      </PageHeader>

      <ActionBar>
        <SearchSection>
          <SearchInput
            type="text"
            placeholder="Search research protocols..."
            value={localSearch}
            onChange={handleSearchChange}
            leftIcon={<SearchIcon />}
            rightIcon={
              localSearch ? (
                <button
                  onClick={() => {
                    setLocalSearch('');
                    dispatch(updateFilters({ search: '' }));
                  }}
                  style={{ background: 'none', border: 'none', cursor: 'pointer' }}
                >
                  <ClearIcon />
                </button>
              ) : undefined
            }
          />

          <FilterSelect
            value={filters.status}
            onChange={handleStatusChange}
          >
            {STATUS_OPTIONS.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </FilterSelect>

          {hasActiveFilters && (
            <Button
              variant="ghost"
              size="small"
              onClick={handleClearFilters}
              leftIcon={<ClearIcon />}
            >
              Clear
            </Button>
          )}
        </SearchSection>

        <CreateButton
          variant="primary"
          onClick={handleCreateProtocol}
          leftIcon={<PlusIcon />}
        >
          Create Protocol
        </CreateButton>
      </ActionBar>

      <StatsSection>
        <StatCard>
          <div className="stat-value">{stats.total}</div>
          <div className="stat-label">Total Protocols</div>
        </StatCard>
        <StatCard>
          <div className="stat-value">{stats.recruiting}</div>
          <div className="stat-label">Recruiting</div>
        </StatCard>
        <StatCard>
          <div className="stat-value">{stats.active}</div>
          <div className="stat-label">Active Studies</div>
        </StatCard>
        <StatCard>
          <div className="stat-value">{stats.completed}</div>
          <div className="stat-label">Completed</div>
        </StatCard>
      </StatsSection>

      {isLoading ? (
        <LoadingContainer>
          <LoadingSpinner size="large" />
        </LoadingContainer>
      ) : protocols.length === 0 ? (
        <EmptyState>
          <h3>
            {hasActiveFilters ? 'No protocols found' : 'No research protocols available'}
          </h3>
          <p>
            {hasActiveFilters
              ? 'Try adjusting your search terms or filters to find relevant protocols.'
              : 'Be the first to create a research protocol and contribute to evidence-based supplement research.'
            }
          </p>
          {!hasActiveFilters && (
            <CreateButton
              variant="primary"
              onClick={handleCreateProtocol}
              leftIcon={<PlusIcon />}
            >
              Create First Protocol
            </CreateButton>
          )}
          {hasActiveFilters && (
            <Button
              variant="outline"
              onClick={handleClearFilters}
            >
              Clear Filters
            </Button>
          )}
        </EmptyState>
      ) : (
        <ProtocolsGrid>
          {protocols.map(protocol => (
            <ResearchProtocolCard
              key={protocol.id}
              protocol={protocol}
              onJoin={handleJoinProtocol}
              showJoinButton={!!currentUser}
            />
          ))}
        </ProtocolsGrid>
      )}
    </PageContainer>
  );
};

export default ResearchPage;
