/**
 * Community Page Component
 *
 * Main community hub with feed, social interactions, and user connections.
 */

import React, { useEffect } from 'react';
import { useAppDispatch } from '@/store';
import { setPageTitle, setBreadcrumbs } from '@/store/slices/uiSlice';
import styled from 'styled-components';

// Components
import CommunityFeed from '@/components/community/CommunityFeed';

// Styled components
const PageContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
`;

const PageHeader = styled.div`
  margin-bottom: 2rem;
  text-align: center;

  h1 {
    color: ${props => props.theme.colors.text};
    margin: 0 0 0.5rem 0;
    font-size: 2.5rem;
    font-weight: ${props => props.theme.typography.fontWeight.bold};
  }

  p {
    color: ${props => props.theme.colors.textSecondary};
    font-size: 1.2rem;
    margin: 0;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
  }
`;

const CommunityPage: React.FC = () => {
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(setPageTitle('Community'));
    dispatch(setBreadcrumbs([
      { label: 'Dashboard', href: '/dashboard' },
      { label: 'Community' },
    ]));
  }, [dispatch]);

  return (
    <PageContainer>
      <PageHeader>
        <h1>Community Hub</h1>
        <p>
          Connect with fellow supplement enthusiasts, share your experiences,
          and discover insights from the community
        </p>
      </PageHeader>

      <CommunityFeed />
    </PageContainer>
  );
};

export default CommunityPage;
