/**
 * Supplements Page Component
 *
 * Main supplements page with search, discovery, and management features.
 */

import React, { useEffect } from 'react';
import { useAppDispatch } from '@/store';
import { setPageTitle, setBreadcrumbs } from '@/store/slices/uiSlice';
import styled from 'styled-components';

// Components
import SupplementSearch from '@/components/supplements/SupplementSearch';

// Styled components
const PageContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
`;

const PageHeader = styled.div`
  margin-bottom: 2rem;

  h1 {
    color: ${props => props.theme.colors.text};
    margin: 0 0 0.5rem 0;
    font-size: 2rem;
  }

  p {
    color: ${props => props.theme.colors.textSecondary};
    font-size: 1.1rem;
    margin: 0;
  }
`;

const SupplementsPage: React.FC = () => {
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(setPageTitle('Supplements'));
    dispatch(setBreadcrumbs([
      { label: 'Dashboard', href: '/dashboard' },
      { label: 'Supplements' },
    ]));
  }, [dispatch]);

  return (
    <PageContainer>
      <PageHeader>
        <h1>Supplement Library</h1>
        <p>
          Discover, search, and track supplements from our comprehensive database
        </p>
      </PageHeader>

      <SupplementSearch showQuickAdd />
    </PageContainer>
  );
};

export default SupplementsPage;
