/**
 * Dashboard Page Component
 * 
 * Main dashboard for authenticated users.
 */

import React from 'react';
import styled from 'styled-components';

const DashboardContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const WelcomeSection = styled.section`
  background: ${props => props.theme.colors.surface};
  border-radius: ${props => props.theme.borderRadius.large};
  padding: 2rem;
  margin-bottom: 2rem;
  border: 1px solid ${props => props.theme.colors.border};
`;

const Title = styled.h1`
  color: ${props => props.theme.colors.text};
  margin-bottom: 1rem;
`;

const Subtitle = styled.p`
  color: ${props => props.theme.colors.textSecondary};
  font-size: 1.1rem;
  margin-bottom: 0;
`;

const DashboardPage: React.FC = () => {
  return (
    <DashboardContainer>
      <WelcomeSection>
        <Title>Welcome to Supplement Tracker</Title>
        <Subtitle>
          Your evidence-based supplement research platform is ready to use.
        </Subtitle>
      </WelcomeSection>
      
      {/* Additional dashboard content will be added here */}
    </DashboardContainer>
  );
};

export default DashboardPage;
