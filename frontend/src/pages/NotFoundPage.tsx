/**
 * 404 Not Found Page Component
 */

import React from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';

const Container = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 2rem;
`;

const Content = styled.div`
  max-width: 500px;
`;

const Title = styled.h1`
  font-size: 4rem;
  color: ${props => props.theme.colors.primary};
  margin-bottom: 1rem;
`;

const Message = styled.p`
  font-size: 1.2rem;
  color: ${props => props.theme.colors.textSecondary};
  margin-bottom: 2rem;
`;

const HomeLink = styled(Link)`
  display: inline-block;
  background: ${props => props.theme.colors.primary};
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: ${props => props.theme.borderRadius.medium};
  text-decoration: none;
  font-weight: 500;
  
  &:hover {
    background: ${props => props.theme.colors.primaryDark};
  }
`;

const NotFoundPage: React.FC = () => {
  return (
    <Container>
      <Content>
        <Title>404</Title>
        <Message>
          The page you're looking for doesn't exist.
        </Message>
        <HomeLink to="/dashboard">
          Return to Dashboard
        </HomeLink>
      </Content>
    </Container>
  );
};

export default NotFoundPage;
