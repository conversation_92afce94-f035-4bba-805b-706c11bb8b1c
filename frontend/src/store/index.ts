/**
 * Redux Store Configuration
 * 
 * This file sets up the Redux store with Redux Toolkit, including
 * authentication, supplements, research, and community slices.
 */

import { configureStore } from '@reduxjs/toolkit';
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';

// Import slice reducers
import authReducer from './slices/authSlice';
import supplementReducer from './slices/supplementSlice';
import researchReducer from './slices/researchSlice';
import communityReducer from './slices/communitySlice';
import analyticsReducer from './slices/analyticsSlice';
import healthReducer from './slices/healthSlice';
import uiReducer from './slices/uiSlice';

// Configure the store
export const store = configureStore({
  reducer: {
    auth: authReducer,
    supplements: supplementReducer,
    research: researchReducer,
    community: communityReducer,
    analytics: analyticsReducer,
    health: healthReducer,
    ui: uiReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
});

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Use throughout your app instead of plain `useDispatch` and `useSelector`
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

export default store;
