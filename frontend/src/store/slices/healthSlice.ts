/**
 * Health Slice
 * 
 * Redux slice for health metrics, wearable device integration, and correlation analysis.
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '@/store';
import { healthService } from '@/services/api';

// Types
interface HealthMetric {
  id: string;
  name: string;
  category: string;
  unit: string;
  current_value: number | null;
  target_min?: number;
  target_max?: number;
  trend?: number;
  status?: 'optimal' | 'warning' | 'critical' | 'unknown';
  recorded_at?: string;
  data_source?: string;
  is_synced?: boolean;
  supplement_correlation?: number;
  created_at: string;
  updated_at: string;
}

interface ConnectedDevice {
  id: string;
  device_id: string;
  device_name: string;
  device_type: string;
  is_connected: boolean;
  last_sync?: string;
  sync_frequency?: string;
  metrics_synced?: number;
  data_points?: number;
  auth_token?: string;
  created_at: string;
  updated_at: string;
}

interface HealthCorrelation {
  id: string;
  health_metric: string;
  supplement: string;
  strength: number;
  confidence: number;
  description: string;
  time_period: string;
  data_points: number;
}

interface BiomarkerReading {
  id: string;
  biomarker_type: string;
  value: number;
  unit: string;
  reference_range_min?: number;
  reference_range_max?: number;
  test_date: string;
  lab_name?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

interface HealthFilters {
  category: string;
  timeRange: string;
  deviceId?: string;
  metricType?: string;
}

interface HealthState {
  metrics: HealthMetric[];
  connectedDevices: ConnectedDevice[];
  correlations: HealthCorrelation[];
  biomarkers: BiomarkerReading[];
  isLoading: boolean;
  isSyncing: boolean;
  deviceSyncStatus: Record<string, boolean>;
  error: string | null;
  filters: HealthFilters;
  lastUpdated: string | null;
}

// Async thunks
export const fetchHealthMetrics = createAsyncThunk(
  'health/fetchMetrics',
  async (params: {
    category?: string;
    timeRange?: string;
    deviceId?: string;
  }) => {
    const response = await healthService.getHealthMetrics(params);
    return response;
  }
);

export const createHealthMetric = createAsyncThunk(
  'health/createMetric',
  async (metricData: Partial<HealthMetric>) => {
    const response = await healthService.createHealthMetric(metricData);
    return response;
  }
);

export const updateHealthMetric = createAsyncThunk(
  'health/updateMetric',
  async (params: { id: string } & Partial<HealthMetric>) => {
    const { id, ...updateData } = params;
    const response = await healthService.updateHealthMetric(id, updateData);
    return response;
  }
);

export const deleteHealthMetric = createAsyncThunk(
  'health/deleteMetric',
  async (metricId: string) => {
    await healthService.deleteHealthMetric(metricId);
    return metricId;
  }
);

export const fetchConnectedDevices = createAsyncThunk(
  'health/fetchDevices',
  async () => {
    const response = await healthService.getConnectedDevices();
    return response;
  }
);

export const connectDevice = createAsyncThunk(
  'health/connectDevice',
  async (deviceData: {
    device_id: string;
    device_name: string;
    auth_token: string;
  }) => {
    const response = await healthService.connectDevice(deviceData);
    return response;
  }
);

export const disconnectDevice = createAsyncThunk(
  'health/disconnectDevice',
  async (deviceId: string) => {
    await healthService.disconnectDevice(deviceId);
    return deviceId;
  }
);

export const syncDeviceData = createAsyncThunk(
  'health/syncDevice',
  async (deviceId: string) => {
    const response = await healthService.syncDeviceData(deviceId);
    return { deviceId, data: response };
  }
);

export const fetchHealthCorrelations = createAsyncThunk(
  'health/fetchCorrelations',
  async (params: {
    timeRange?: string;
    metricIds?: string[];
    supplementIds?: string[];
  }) => {
    const response = await healthService.getHealthCorrelations(params);
    return response;
  }
);

export const fetchBiomarkers = createAsyncThunk(
  'health/fetchBiomarkers',
  async (params: {
    timeRange?: string;
    biomarkerType?: string;
  }) => {
    const response = await healthService.getBiomarkers(params);
    return response;
  }
);

export const createBiomarkerReading = createAsyncThunk(
  'health/createBiomarker',
  async (biomarkerData: Partial<BiomarkerReading>) => {
    const response = await healthService.createBiomarkerReading(biomarkerData);
    return response;
  }
);

export const generateHealthInsights = createAsyncThunk(
  'health/generateInsights',
  async (params: {
    metricIds?: string[];
    timeRange?: string;
  }) => {
    const response = await healthService.generateHealthInsights(params);
    return response;
  }
);

// Initial state
const initialState: HealthState = {
  metrics: [],
  connectedDevices: [],
  correlations: [],
  biomarkers: [],
  isLoading: false,
  isSyncing: false,
  deviceSyncStatus: {},
  error: null,
  filters: {
    category: '',
    timeRange: '30d',
  },
  lastUpdated: null,
};

// Health slice
const healthSlice = createSlice({
  name: 'health',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateHealthFilters: (state, action: PayloadAction<Partial<HealthFilters>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    resetHealthFilters: (state) => {
      state.filters = initialState.filters;
    },
    clearHealthData: (state) => {
      state.metrics = [];
      state.correlations = [];
      state.biomarkers = [];
      state.lastUpdated = null;
    },
    setDeviceSyncStatus: (state, action: PayloadAction<{ deviceId: string; syncing: boolean }>) => {
      state.deviceSyncStatus[action.payload.deviceId] = action.payload.syncing;
    },
    updateMetricValue: (state, action: PayloadAction<{ id: string; value: number; timestamp: string }>) => {
      const metric = state.metrics.find(m => m.id === action.payload.id);
      if (metric) {
        metric.current_value = action.payload.value;
        metric.recorded_at = action.payload.timestamp;
      }
    },
  },
  extraReducers: (builder) => {
    // Fetch health metrics
    builder
      .addCase(fetchHealthMetrics.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchHealthMetrics.fulfilled, (state, action) => {
        state.isLoading = false;
        state.metrics = action.payload;
        state.lastUpdated = new Date().toISOString();
      })
      .addCase(fetchHealthMetrics.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch health metrics';
      });

    // Create health metric
    builder
      .addCase(createHealthMetric.fulfilled, (state, action) => {
        state.metrics.push(action.payload);
      })
      .addCase(createHealthMetric.rejected, (state, action) => {
        state.error = action.error.message || 'Failed to create health metric';
      });

    // Update health metric
    builder
      .addCase(updateHealthMetric.fulfilled, (state, action) => {
        const index = state.metrics.findIndex(m => m.id === action.payload.id);
        if (index !== -1) {
          state.metrics[index] = action.payload;
        }
      })
      .addCase(updateHealthMetric.rejected, (state, action) => {
        state.error = action.error.message || 'Failed to update health metric';
      });

    // Delete health metric
    builder
      .addCase(deleteHealthMetric.fulfilled, (state, action) => {
        state.metrics = state.metrics.filter(m => m.id !== action.payload);
      })
      .addCase(deleteHealthMetric.rejected, (state, action) => {
        state.error = action.error.message || 'Failed to delete health metric';
      });

    // Fetch connected devices
    builder
      .addCase(fetchConnectedDevices.fulfilled, (state, action) => {
        state.connectedDevices = action.payload;
      })
      .addCase(fetchConnectedDevices.rejected, (state, action) => {
        state.error = action.error.message || 'Failed to fetch connected devices';
      });

    // Connect device
    builder
      .addCase(connectDevice.fulfilled, (state, action) => {
        state.connectedDevices.push(action.payload);
      })
      .addCase(connectDevice.rejected, (state, action) => {
        state.error = action.error.message || 'Failed to connect device';
      });

    // Disconnect device
    builder
      .addCase(disconnectDevice.fulfilled, (state, action) => {
        state.connectedDevices = state.connectedDevices.filter(d => d.device_id !== action.payload);
      })
      .addCase(disconnectDevice.rejected, (state, action) => {
        state.error = action.error.message || 'Failed to disconnect device';
      });

    // Sync device data
    builder
      .addCase(syncDeviceData.pending, (state, action) => {
        state.isSyncing = true;
        state.deviceSyncStatus[action.meta.arg] = true;
      })
      .addCase(syncDeviceData.fulfilled, (state, action) => {
        state.isSyncing = false;
        state.deviceSyncStatus[action.payload.deviceId] = false;
        
        // Update device last sync time
        const device = state.connectedDevices.find(d => d.device_id === action.payload.deviceId);
        if (device) {
          device.last_sync = new Date().toISOString();
          device.data_points = (device.data_points || 0) + (action.payload.data.length || 0);
        }
      })
      .addCase(syncDeviceData.rejected, (state, action) => {
        state.isSyncing = false;
        state.deviceSyncStatus[action.meta.arg] = false;
        state.error = action.error.message || 'Failed to sync device data';
      });

    // Fetch health correlations
    builder
      .addCase(fetchHealthCorrelations.fulfilled, (state, action) => {
        state.correlations = action.payload;
      })
      .addCase(fetchHealthCorrelations.rejected, (state, action) => {
        state.error = action.error.message || 'Failed to fetch health correlations';
      });

    // Fetch biomarkers
    builder
      .addCase(fetchBiomarkers.fulfilled, (state, action) => {
        state.biomarkers = action.payload;
      })
      .addCase(fetchBiomarkers.rejected, (state, action) => {
        state.error = action.error.message || 'Failed to fetch biomarkers';
      });

    // Create biomarker reading
    builder
      .addCase(createBiomarkerReading.fulfilled, (state, action) => {
        state.biomarkers.push(action.payload);
      })
      .addCase(createBiomarkerReading.rejected, (state, action) => {
        state.error = action.error.message || 'Failed to create biomarker reading';
      });
  },
});

// Export actions
export const { 
  clearError, 
  updateHealthFilters, 
  resetHealthFilters,
  clearHealthData,
  setDeviceSyncStatus,
  updateMetricValue
} = healthSlice.actions;

// Selectors
export const selectHealthMetrics = (state: RootState) => state.health.metrics;
export const selectConnectedDevices = (state: RootState) => state.health.connectedDevices;
export const selectHealthCorrelations = (state: RootState) => state.health.correlations;
export const selectBiomarkers = (state: RootState) => state.health.biomarkers;
export const selectHealthLoading = (state: RootState) => state.health.isLoading;
export const selectHealthSyncing = (state: RootState) => state.health.isSyncing;
export const selectDeviceSyncStatus = (state: RootState) => state.health.deviceSyncStatus;
export const selectHealthError = (state: RootState) => state.health.error;
export const selectHealthFilters = (state: RootState) => state.health.filters;
export const selectHealthLastUpdated = (state: RootState) => state.health.lastUpdated;

// Computed selectors
export const selectMetricsByCategory = (category: string) => (state: RootState) => {
  return state.health.metrics.filter(metric => 
    category === '' || metric.category === category
  );
};

export const selectConnectedDevicesByType = (deviceType: string) => (state: RootState) => {
  return state.health.connectedDevices.filter(device => 
    device.device_type === deviceType && device.is_connected
  );
};

export const selectStrongCorrelations = (state: RootState) => {
  return state.health.correlations.filter(correlation => 
    Math.abs(correlation.strength) >= 0.7
  );
};

export const selectMetricsNeedingAttention = (state: RootState) => {
  return state.health.metrics.filter(metric => 
    metric.status === 'warning' || metric.status === 'critical'
  );
};

export const selectRecentBiomarkers = (days: number = 30) => (state: RootState) => {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - days);
  
  return state.health.biomarkers.filter(biomarker => 
    new Date(biomarker.test_date) >= cutoffDate
  );
};

export const selectDeviceById = (deviceId: string) => (state: RootState) => {
  return state.health.connectedDevices.find(device => device.device_id === deviceId);
};

export const selectMetricTrends = (state: RootState) => {
  return state.health.metrics.map(metric => ({
    id: metric.id,
    name: metric.name,
    trend: metric.trend || 0,
    status: metric.status || 'unknown',
  }));
};

// Export reducer
export default healthSlice.reducer;
