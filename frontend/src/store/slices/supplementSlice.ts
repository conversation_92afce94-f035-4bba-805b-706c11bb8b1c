/**
 * Supplement Redux Slice
 * 
 * Manages supplement-related state including supplement data, intake tracking, and history.
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Supplement, SupplementIntake, SupplementIntakeCreate, PaginatedResponse } from '@/types/api';
import { supplementService } from '@/services/api';

// Define the initial state interface
interface SupplementState {
  supplements: Supplement[];
  currentSupplement: Supplement | null;
  intakeHistory: SupplementIntake[];
  recentIntakes: SupplementIntake[];
  isLoading: boolean;
  isTrackingIntake: boolean;
  error: string | null;
  pagination: {
    total: number;
    page: number;
    size: number;
    pages: number;
  };
  filters: {
    search: string;
    category: string;
  };
}

// Initial state
const initialState: SupplementState = {
  supplements: [],
  currentSupplement: null,
  intakeHistory: [],
  recentIntakes: [],
  isLoading: false,
  isTrackingIntake: false,
  error: null,
  pagination: {
    total: 0,
    page: 1,
    size: 20,
    pages: 0,
  },
  filters: {
    search: '',
    category: '',
  },
};

// Async thunks for supplement actions
export const fetchSupplements = createAsyncThunk(
  'supplements/fetchSupplements',
  async (params: { search?: string; category?: string; skip?: number; limit?: number } = {}, { rejectWithValue }) => {
    try {
      const response = await supplementService.getSupplements(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.detail || 'Failed to fetch supplements');
    }
  }
);

export const fetchSupplement = createAsyncThunk(
  'supplements/fetchSupplement',
  async (supplementId: string, { rejectWithValue }) => {
    try {
      const response = await supplementService.getSupplement(supplementId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.detail || 'Failed to fetch supplement');
    }
  }
);

export const createSupplement = createAsyncThunk(
  'supplements/createSupplement',
  async (supplementData: Partial<Supplement>, { rejectWithValue }) => {
    try {
      const response = await supplementService.createSupplement(supplementData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.detail || 'Failed to create supplement');
    }
  }
);

export const trackSupplementIntake = createAsyncThunk(
  'supplements/trackIntake',
  async ({ supplementId, intakeData }: { supplementId: string; intakeData: SupplementIntakeCreate }, { rejectWithValue }) => {
    try {
      const response = await supplementService.trackIntake(supplementId, intakeData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.detail || 'Failed to track supplement intake');
    }
  }
);

export const fetchIntakeHistory = createAsyncThunk(
  'supplements/fetchIntakeHistory',
  async (params: { supplement_id?: string; skip?: number; limit?: number } = {}, { rejectWithValue }) => {
    try {
      const response = await supplementService.getHistory(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.detail || 'Failed to fetch intake history');
    }
  }
);

// Create the supplement slice
const supplementSlice = createSlice({
  name: 'supplements',
  initialState,
  reducers: {
    // Clear error state
    clearError: (state) => {
      state.error = null;
    },
    // Update filters
    updateFilters: (state, action: PayloadAction<Partial<SupplementState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    // Clear current supplement
    clearCurrentSupplement: (state) => {
      state.currentSupplement = null;
    },
    // Add recent intake (for optimistic updates)
    addRecentIntake: (state, action: PayloadAction<SupplementIntake>) => {
      state.recentIntakes.unshift(action.payload);
      // Keep only the last 10 recent intakes
      state.recentIntakes = state.recentIntakes.slice(0, 10);
    },
  },
  extraReducers: (builder) => {
    // Fetch supplements
    builder
      .addCase(fetchSupplements.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchSupplements.fulfilled, (state, action: PayloadAction<PaginatedResponse<Supplement>>) => {
        state.isLoading = false;
        state.supplements = action.payload.items;
        state.pagination = {
          total: action.payload.total,
          page: action.payload.page,
          size: action.payload.size,
          pages: action.payload.pages,
        };
        state.error = null;
      })
      .addCase(fetchSupplements.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch single supplement
    builder
      .addCase(fetchSupplement.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchSupplement.fulfilled, (state, action: PayloadAction<Supplement>) => {
        state.isLoading = false;
        state.currentSupplement = action.payload;
        state.error = null;
      })
      .addCase(fetchSupplement.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Create supplement
    builder
      .addCase(createSupplement.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createSupplement.fulfilled, (state, action: PayloadAction<Supplement>) => {
        state.isLoading = false;
        state.supplements.unshift(action.payload);
        state.error = null;
      })
      .addCase(createSupplement.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Track supplement intake
    builder
      .addCase(trackSupplementIntake.pending, (state) => {
        state.isTrackingIntake = true;
        state.error = null;
      })
      .addCase(trackSupplementIntake.fulfilled, (state, action: PayloadAction<SupplementIntake>) => {
        state.isTrackingIntake = false;
        state.recentIntakes.unshift(action.payload);
        state.recentIntakes = state.recentIntakes.slice(0, 10);
        state.error = null;
      })
      .addCase(trackSupplementIntake.rejected, (state, action) => {
        state.isTrackingIntake = false;
        state.error = action.payload as string;
      });

    // Fetch intake history
    builder
      .addCase(fetchIntakeHistory.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchIntakeHistory.fulfilled, (state, action: PayloadAction<SupplementIntake[]>) => {
        state.isLoading = false;
        state.intakeHistory = action.payload;
        state.error = null;
      })
      .addCase(fetchIntakeHistory.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

// Export actions
export const { clearError, updateFilters, clearCurrentSupplement, addRecentIntake } = supplementSlice.actions;

// Export selectors
export const selectSupplements = (state: { supplements: SupplementState }) => state.supplements.supplements;
export const selectCurrentSupplement = (state: { supplements: SupplementState }) => state.supplements.currentSupplement;
export const selectIntakeHistory = (state: { supplements: SupplementState }) => state.supplements.intakeHistory;
export const selectRecentIntakes = (state: { supplements: SupplementState }) => state.supplements.recentIntakes;
export const selectSupplementsLoading = (state: { supplements: SupplementState }) => state.supplements.isLoading;
export const selectTrackingIntake = (state: { supplements: SupplementState }) => state.supplements.isTrackingIntake;
export const selectSupplementsError = (state: { supplements: SupplementState }) => state.supplements.error;
export const selectSupplementsPagination = (state: { supplements: SupplementState }) => state.supplements.pagination;
export const selectSupplementsFilters = (state: { supplements: SupplementState }) => state.supplements.filters;

// Export reducer
export default supplementSlice.reducer;
