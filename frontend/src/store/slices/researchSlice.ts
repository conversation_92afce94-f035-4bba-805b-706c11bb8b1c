/**
 * Research Redux Slice
 * 
 * Manages research-related state including protocols, participants, and analytics.
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { ResearchProtocol, StudyParticipant, LiteratureReference, StudyParticipation, DataCollection } from '@/types/api';
import { researchService } from '@/services/api';
import { RootState } from '@/store';

// Define the initial state interface
interface ResearchState {
  protocols: ResearchProtocol[];
  currentProtocol: ResearchProtocol | null;
  myProtocols: ResearchProtocol[];
  participants: StudyParticipant[];
  literature: LiteratureReference[];
  myParticipations: StudyParticipation[];
  dataCollections: DataCollection[];
  isLoading: boolean;
  isCreatingProtocol: boolean;
  isJoiningStudy: boolean;
  isSubmittingData: boolean;
  error: string | null;
  filters: {
    status: string;
    search: string;
  };
  pagination: {
    total: number;
    page: number;
    size: number;
    pages: number;
  };
}

// Initial state
const initialState: ResearchState = {
  protocols: [],
  currentProtocol: null,
  myProtocols: [],
  participants: [],
  literature: [],
  myParticipations: [],
  dataCollections: [],
  isLoading: false,
  isCreatingProtocol: false,
  isJoiningStudy: false,
  isSubmittingData: false,
  error: null,
  filters: {
    status: '',
    search: '',
  },
  pagination: {
    total: 0,
    page: 1,
    size: 20,
    pages: 0,
  },
};

// Async thunks for research actions
export const fetchResearchProtocols = createAsyncThunk(
  'research/fetchProtocols',
  async (params: { status?: string; creator_id?: string; search?: string; skip?: number; limit?: number } = {}, { rejectWithValue }) => {
    try {
      const response = await researchService.getProtocols(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.detail || 'Failed to fetch research protocols');
    }
  }
);

export const fetchResearchProtocol = createAsyncThunk(
  'research/fetchProtocol',
  async (protocolId: string, { rejectWithValue }) => {
    try {
      const response = await researchService.getProtocol(protocolId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.detail || 'Failed to fetch research protocol');
    }
  }
);

export const createResearchProtocol = createAsyncThunk(
  'research/createProtocol',
  async (protocolData: Partial<ResearchProtocol>, { rejectWithValue }) => {
    try {
      const response = await researchService.createProtocol(protocolData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.detail || 'Failed to create research protocol');
    }
  }
);

export const joinStudy = createAsyncThunk(
  'research/joinStudy',
  async (protocolId: string, { rejectWithValue }) => {
    try {
      const response = await researchService.joinStudy(protocolId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.detail || 'Failed to join study');
    }
  }
);

export const leaveStudy = createAsyncThunk(
  'research/leaveStudy',
  async (protocolId: string, { rejectWithValue }) => {
    try {
      const response = await researchService.leaveStudy(protocolId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.detail || 'Failed to leave study');
    }
  }
);

export const fetchMyParticipations = createAsyncThunk(
  'research/fetchMyParticipations',
  async (_, { rejectWithValue }) => {
    try {
      const response = await researchService.getMyParticipations();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.detail || 'Failed to fetch participations');
    }
  }
);

export const submitDataCollection = createAsyncThunk(
  'research/submitDataCollection',
  async ({ protocolId, data }: { protocolId: string; data: Partial<DataCollection> }, { rejectWithValue }) => {
    try {
      const response = await researchService.submitDataCollection(protocolId, data);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.detail || 'Failed to submit data');
    }
  }
);

// Create the research slice
const researchSlice = createSlice({
  name: 'research',
  initialState,
  reducers: {
    // Clear error state
    clearError: (state) => {
      state.error = null;
    },
    // Update filters
    updateFilters: (state, action: PayloadAction<Partial<ResearchState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    // Clear current protocol
    clearCurrentProtocol: (state) => {
      state.currentProtocol = null;
    },
    // Set participants for current protocol
    setParticipants: (state, action: PayloadAction<StudyParticipant[]>) => {
      state.participants = action.payload;
    },
    // Add participant
    addParticipant: (state, action: PayloadAction<StudyParticipant>) => {
      state.participants.push(action.payload);
    },
    // Update participant status
    updateParticipantStatus: (state, action: PayloadAction<{ participantId: string; status: string }>) => {
      const participant = state.participants.find(p => p.id === action.payload.participantId);
      if (participant) {
        participant.status = action.payload.status as any;
      }
    },
  },
  extraReducers: (builder) => {
    // Fetch research protocols
    builder
      .addCase(fetchResearchProtocols.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchResearchProtocols.fulfilled, (state, action: PayloadAction<ResearchProtocol[]>) => {
        state.isLoading = false;
        state.protocols = action.payload;
        state.error = null;
      })
      .addCase(fetchResearchProtocols.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch single research protocol
    builder
      .addCase(fetchResearchProtocol.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchResearchProtocol.fulfilled, (state, action: PayloadAction<ResearchProtocol>) => {
        state.isLoading = false;
        state.currentProtocol = action.payload;
        state.error = null;
      })
      .addCase(fetchResearchProtocol.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Create research protocol
    builder
      .addCase(createResearchProtocol.pending, (state) => {
        state.isCreatingProtocol = true;
        state.error = null;
      })
      .addCase(createResearchProtocol.fulfilled, (state, action: PayloadAction<ResearchProtocol>) => {
        state.isCreatingProtocol = false;
        state.protocols.unshift(action.payload);
        state.myProtocols.unshift(action.payload);
        state.error = null;
      })
      .addCase(createResearchProtocol.rejected, (state, action) => {
        state.isCreatingProtocol = false;
        state.error = action.payload as string;
      });

    // Join study
    builder
      .addCase(joinStudy.pending, (state) => {
        state.isJoiningStudy = true;
        state.error = null;
      })
      .addCase(joinStudy.fulfilled, (state, action: PayloadAction<StudyParticipation>) => {
        state.isJoiningStudy = false;
        state.myParticipations.push(action.payload);

        // Update protocol participant count
        const protocol = state.protocols.find(p => p.id === action.payload.protocol_id);
        if (protocol) {
          protocol.participant_count += 1;
        }
        if (state.currentProtocol?.id === action.payload.protocol_id) {
          state.currentProtocol.participant_count += 1;
        }
      })
      .addCase(joinStudy.rejected, (state, action) => {
        state.isJoiningStudy = false;
        state.error = action.payload as string;
      });

    // Leave study
    builder
      .addCase(leaveStudy.fulfilled, (state, action) => {
        state.myParticipations = state.myParticipations.filter(
          p => p.protocol_id !== action.meta.arg
        );

        // Update protocol participant count
        const protocol = state.protocols.find(p => p.id === action.meta.arg);
        if (protocol) {
          protocol.participant_count -= 1;
        }
        if (state.currentProtocol?.id === action.meta.arg) {
          state.currentProtocol.participant_count -= 1;
        }
      })
      .addCase(leaveStudy.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Fetch my participations
    builder
      .addCase(fetchMyParticipations.fulfilled, (state, action: PayloadAction<StudyParticipation[]>) => {
        state.myParticipations = action.payload;
      })
      .addCase(fetchMyParticipations.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Submit data collection
    builder
      .addCase(submitDataCollection.pending, (state) => {
        state.isSubmittingData = true;
        state.error = null;
      })
      .addCase(submitDataCollection.fulfilled, (state, action: PayloadAction<DataCollection>) => {
        state.isSubmittingData = false;
        state.dataCollections.push(action.payload);
      })
      .addCase(submitDataCollection.rejected, (state, action) => {
        state.isSubmittingData = false;
        state.error = action.payload as string;
      });
  },
});

// Export actions
export const { 
  clearError, 
  updateFilters, 
  clearCurrentProtocol, 
  setParticipants, 
  addParticipant, 
  updateParticipantStatus 
} = researchSlice.actions;

// Export selectors
export const selectResearchProtocols = (state: RootState) => state.research.protocols;
export const selectCurrentProtocol = (state: RootState) => state.research.currentProtocol;
export const selectMyProtocols = (state: RootState) => state.research.myProtocols;
export const selectParticipants = (state: RootState) => state.research.participants;
export const selectLiterature = (state: RootState) => state.research.literature;
export const selectMyParticipations = (state: RootState) => state.research.myParticipations;
export const selectDataCollections = (state: RootState) => state.research.dataCollections;
export const selectResearchLoading = (state: RootState) => state.research.isLoading;
export const selectIsCreatingProtocol = (state: RootState) => state.research.isCreatingProtocol;
export const selectIsJoiningStudy = (state: RootState) => state.research.isJoiningStudy;
export const selectIsSubmittingData = (state: RootState) => state.research.isSubmittingData;
export const selectResearchError = (state: RootState) => state.research.error;
export const selectResearchFilters = (state: RootState) => state.research.filters;
export const selectResearchPagination = (state: RootState) => state.research.pagination;

// Export reducer
export default researchSlice.reducer;
