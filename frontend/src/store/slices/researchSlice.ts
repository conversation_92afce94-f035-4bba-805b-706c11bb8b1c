/**
 * Research Redux Slice
 * 
 * Manages research-related state including protocols, participants, and analytics.
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { ResearchProtocol, StudyParticipant, LiteratureReference } from '@/types/api';
import { researchService } from '@/services/api';

// Define the initial state interface
interface ResearchState {
  protocols: ResearchProtocol[];
  currentProtocol: ResearchProtocol | null;
  myProtocols: ResearchProtocol[];
  participants: StudyParticipant[];
  literature: LiteratureReference[];
  isLoading: boolean;
  error: string | null;
  filters: {
    status: string;
    search: string;
  };
}

// Initial state
const initialState: ResearchState = {
  protocols: [],
  currentProtocol: null,
  myProtocols: [],
  participants: [],
  literature: [],
  isLoading: false,
  error: null,
  filters: {
    status: '',
    search: '',
  },
};

// Async thunks for research actions
export const fetchResearchProtocols = createAsyncThunk(
  'research/fetchProtocols',
  async (params: { status?: string; creator_id?: string; search?: string; skip?: number; limit?: number } = {}, { rejectWithValue }) => {
    try {
      const response = await researchService.getProtocols(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.detail || 'Failed to fetch research protocols');
    }
  }
);

export const fetchResearchProtocol = createAsyncThunk(
  'research/fetchProtocol',
  async (protocolId: string, { rejectWithValue }) => {
    try {
      const response = await researchService.getProtocol(protocolId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.detail || 'Failed to fetch research protocol');
    }
  }
);

export const createResearchProtocol = createAsyncThunk(
  'research/createProtocol',
  async (protocolData: Partial<ResearchProtocol>, { rejectWithValue }) => {
    try {
      const response = await researchService.createProtocol(protocolData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.detail || 'Failed to create research protocol');
    }
  }
);

// Create the research slice
const researchSlice = createSlice({
  name: 'research',
  initialState,
  reducers: {
    // Clear error state
    clearError: (state) => {
      state.error = null;
    },
    // Update filters
    updateFilters: (state, action: PayloadAction<Partial<ResearchState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    // Clear current protocol
    clearCurrentProtocol: (state) => {
      state.currentProtocol = null;
    },
    // Set participants for current protocol
    setParticipants: (state, action: PayloadAction<StudyParticipant[]>) => {
      state.participants = action.payload;
    },
    // Add participant
    addParticipant: (state, action: PayloadAction<StudyParticipant>) => {
      state.participants.push(action.payload);
    },
    // Update participant status
    updateParticipantStatus: (state, action: PayloadAction<{ participantId: string; status: string }>) => {
      const participant = state.participants.find(p => p.id === action.payload.participantId);
      if (participant) {
        participant.status = action.payload.status as any;
      }
    },
  },
  extraReducers: (builder) => {
    // Fetch research protocols
    builder
      .addCase(fetchResearchProtocols.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchResearchProtocols.fulfilled, (state, action: PayloadAction<ResearchProtocol[]>) => {
        state.isLoading = false;
        state.protocols = action.payload;
        state.error = null;
      })
      .addCase(fetchResearchProtocols.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch single research protocol
    builder
      .addCase(fetchResearchProtocol.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchResearchProtocol.fulfilled, (state, action: PayloadAction<ResearchProtocol>) => {
        state.isLoading = false;
        state.currentProtocol = action.payload;
        state.error = null;
      })
      .addCase(fetchResearchProtocol.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Create research protocol
    builder
      .addCase(createResearchProtocol.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createResearchProtocol.fulfilled, (state, action: PayloadAction<ResearchProtocol>) => {
        state.isLoading = false;
        state.protocols.unshift(action.payload);
        state.myProtocols.unshift(action.payload);
        state.error = null;
      })
      .addCase(createResearchProtocol.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

// Export actions
export const { 
  clearError, 
  updateFilters, 
  clearCurrentProtocol, 
  setParticipants, 
  addParticipant, 
  updateParticipantStatus 
} = researchSlice.actions;

// Export selectors
export const selectResearchProtocols = (state: { research: ResearchState }) => state.research.protocols;
export const selectCurrentProtocol = (state: { research: ResearchState }) => state.research.currentProtocol;
export const selectMyProtocols = (state: { research: ResearchState }) => state.research.myProtocols;
export const selectParticipants = (state: { research: ResearchState }) => state.research.participants;
export const selectLiterature = (state: { research: ResearchState }) => state.research.literature;
export const selectResearchLoading = (state: { research: ResearchState }) => state.research.isLoading;
export const selectResearchError = (state: { research: ResearchState }) => state.research.error;
export const selectResearchFilters = (state: { research: ResearchState }) => state.research.filters;

// Export reducer
export default researchSlice.reducer;
