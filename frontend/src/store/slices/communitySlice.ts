/**
 * Community Redux Slice
 * 
 * Manages community-related state including posts, comments, groups, and social interactions.
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { CommunityPost, PostComment, CommunityGroup, UserFollow, Comment, UserProfile } from '@/types/api';
import { communityService } from '@/services/api';
import { RootState } from '@/store';

// Define the initial state interface
interface CommunityState {
  posts: CommunityPost[];
  currentPost: CommunityPost | null;
  comments: { [postId: string]: Comment[] };
  groups: CommunityGroup[];
  following: UserFollow[];
  followers: UserFollow[];
  userProfiles: { [userId: string]: UserProfile };
  isLoading: boolean;
  isPostingComment: boolean;
  isCreatingPost: boolean;
  isLikingPost: { [postId: string]: boolean };
  isSharingPost: { [postId: string]: boolean };
  isFollowingUser: { [userId: string]: boolean };
  commentsLoading: { [postId: string]: boolean };
  profilesLoading: { [userId: string]: boolean };
  error: string | null;
  feedFilters: {
    post_type: string;
    search: string;
    sort: string;
  };
  feedPagination: {
    total: number;
    page: number;
    size: number;
    hasMore: boolean;
  };
  filters: {
    post_type: string;
    group_id: string;
    search: string;
  };
}

// Initial state
const initialState: CommunityState = {
  posts: [],
  currentPost: null,
  comments: {},
  groups: [],
  following: [],
  followers: [],
  userProfiles: {},
  isLoading: false,
  isPostingComment: false,
  isCreatingPost: false,
  isLikingPost: {},
  isSharingPost: {},
  isFollowingUser: {},
  commentsLoading: {},
  profilesLoading: {},
  error: null,
  feedFilters: {
    post_type: '',
    search: '',
    sort: 'recent',
  },
  feedPagination: {
    total: 0,
    page: 1,
    size: 10,
    hasMore: false,
  },
  filters: {
    post_type: '',
    group_id: '',
    search: '',
  },
};

// Async thunks for community actions
export const fetchCommunityPosts = createAsyncThunk(
  'community/fetchPosts',
  async (params: { skip?: number; limit?: number; group_id?: string; post_type?: string; author_id?: string } = {}, { rejectWithValue }) => {
    try {
      const response = await communityService.getPosts(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.detail || 'Failed to fetch community posts');
    }
  }
);

export const fetchCommunityPost = createAsyncThunk(
  'community/fetchPost',
  async (postId: string, { rejectWithValue }) => {
    try {
      const response = await communityService.getPost(postId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.detail || 'Failed to fetch community post');
    }
  }
);

export const createCommunityPost = createAsyncThunk(
  'community/createPost',
  async (postData: Partial<CommunityPost>, { rejectWithValue }) => {
    try {
      const response = await communityService.createPost(postData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.detail || 'Failed to create community post');
    }
  }
);

export const likePost = createAsyncThunk(
  'community/likePost',
  async (postId: string, { rejectWithValue }) => {
    try {
      const response = await communityService.likePost(postId);
      return { postId, ...response };
    } catch (error: any) {
      return rejectWithValue(error.detail || 'Failed to like post');
    }
  }
);

export const unlikePost = createAsyncThunk(
  'community/unlikePost',
  async (postId: string, { rejectWithValue }) => {
    try {
      const response = await communityService.unlikePost(postId);
      return { postId, ...response };
    } catch (error: any) {
      return rejectWithValue(error.detail || 'Failed to unlike post');
    }
  }
);

export const sharePost = createAsyncThunk(
  'community/sharePost',
  async (postId: string, { rejectWithValue }) => {
    try {
      const response = await communityService.sharePost(postId);
      return { postId, ...response };
    } catch (error: any) {
      return rejectWithValue(error.detail || 'Failed to share post');
    }
  }
);

export const fetchPostComments = createAsyncThunk(
  'community/fetchComments',
  async ({ postId, skip = 0, limit = 20 }: { postId: string; skip?: number; limit?: number }, { rejectWithValue }) => {
    try {
      const response = await communityService.getPostComments(postId, { skip, limit });
      return { postId, ...response };
    } catch (error: any) {
      return rejectWithValue(error.detail || 'Failed to fetch comments');
    }
  }
);

export const createComment = createAsyncThunk(
  'community/createComment',
  async ({ postId, content }: { postId: string; content: string }, { rejectWithValue }) => {
    try {
      const response = await communityService.createComment(postId, { content });
      return { postId, comment: response };
    } catch (error: any) {
      return rejectWithValue(error.detail || 'Failed to create comment');
    }
  }
);

export const followUser = createAsyncThunk(
  'community/followUser',
  async (userId: string, { rejectWithValue }) => {
    try {
      const response = await communityService.followUser(userId);
      return { userId, ...response };
    } catch (error: any) {
      return rejectWithValue(error.detail || 'Failed to follow user');
    }
  }
);

export const unfollowUser = createAsyncThunk(
  'community/unfollowUser',
  async (userId: string, { rejectWithValue }) => {
    try {
      const response = await communityService.unfollowUser(userId);
      return { userId, ...response };
    } catch (error: any) {
      return rejectWithValue(error.detail || 'Failed to unfollow user');
    }
  }
);

export const fetchUserProfile = createAsyncThunk(
  'community/fetchUserProfile',
  async (userId: string, { rejectWithValue }) => {
    try {
      const response = await communityService.getUserProfile(userId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.detail || 'Failed to fetch user profile');
    }
  }
);

// Create the community slice
const communitySlice = createSlice({
  name: 'community',
  initialState,
  reducers: {
    // Clear error state
    clearError: (state) => {
      state.error = null;
    },
    // Update filters
    updateFilters: (state, action: PayloadAction<Partial<CommunityState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    // Update feed filters
    updateFeedFilters: (state, action: PayloadAction<Partial<CommunityState['feedFilters']>>) => {
      state.feedFilters = { ...state.feedFilters, ...action.payload };
    },
    // Reset feed filters
    resetFeedFilters: (state) => {
      state.feedFilters = initialState.feedFilters;
    },
    // Clear posts
    clearPosts: (state) => {
      state.posts = [];
      state.feedPagination = initialState.feedPagination;
    },
    // Clear current post
    clearCurrentPost: (state) => {
      state.currentPost = null;
      state.comments = [];
    },
    // Set comments for current post
    setComments: (state, action: PayloadAction<PostComment[]>) => {
      state.comments = action.payload;
    },
    // Add comment
    addComment: (state, action: PayloadAction<PostComment>) => {
      state.comments.push(action.payload);
      // Update comment count on current post
      if (state.currentPost) {
        state.currentPost.comment_count += 1;
      }
    },
    // Update post votes
    updatePostVotes: (state, action: PayloadAction<{ postId: string; upvotes: number; downvotes: number }>) => {
      const post = state.posts.find(p => p.id === action.payload.postId);
      if (post) {
        post.upvotes = action.payload.upvotes;
        post.downvotes = action.payload.downvotes;
      }
      if (state.currentPost && state.currentPost.id === action.payload.postId) {
        state.currentPost.upvotes = action.payload.upvotes;
        state.currentPost.downvotes = action.payload.downvotes;
      }
    },
    // Update comment votes
    updateCommentVotes: (state, action: PayloadAction<{ commentId: string; upvotes: number; downvotes: number }>) => {
      const comment = state.comments.find(c => c.id === action.payload.commentId);
      if (comment) {
        comment.upvotes = action.payload.upvotes;
        comment.downvotes = action.payload.downvotes;
      }
    },
    // Set groups
    setGroups: (state, action: PayloadAction<CommunityGroup[]>) => {
      state.groups = action.payload;
    },
    // Set following/followers
    setFollowing: (state, action: PayloadAction<UserFollow[]>) => {
      state.following = action.payload;
    },
    setFollowers: (state, action: PayloadAction<UserFollow[]>) => {
      state.followers = action.payload;
    },
    // Add/remove follow
    addFollow: (state, action: PayloadAction<UserFollow>) => {
      state.following.push(action.payload);
    },
    removeFollow: (state, action: PayloadAction<string>) => {
      state.following = state.following.filter(f => f.id !== action.payload);
    },
  },
  extraReducers: (builder) => {
    // Fetch community posts
    builder
      .addCase(fetchCommunityPosts.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchCommunityPosts.fulfilled, (state, action: PayloadAction<CommunityPost[]>) => {
        state.isLoading = false;
        state.posts = action.payload;
        state.error = null;
      })
      .addCase(fetchCommunityPosts.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch single community post
    builder
      .addCase(fetchCommunityPost.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchCommunityPost.fulfilled, (state, action: PayloadAction<CommunityPost>) => {
        state.isLoading = false;
        state.currentPost = action.payload;
        state.error = null;
      })
      .addCase(fetchCommunityPost.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Create community post
    builder
      .addCase(createCommunityPost.pending, (state) => {
        state.isCreatingPost = true;
        state.error = null;
      })
      .addCase(createCommunityPost.fulfilled, (state, action: PayloadAction<CommunityPost>) => {
        state.isCreatingPost = false;
        state.posts.unshift(action.payload);
        state.feedPagination.total += 1;
        state.error = null;
      })
      .addCase(createCommunityPost.rejected, (state, action) => {
        state.isCreatingPost = false;
        state.error = action.payload as string;
      });

    // Like post
    builder
      .addCase(likePost.pending, (state, action) => {
        state.isLikingPost[action.meta.arg] = true;
      })
      .addCase(likePost.fulfilled, (state, action) => {
        state.isLikingPost[action.payload.postId] = false;

        // Update post in posts array
        const post = state.posts.find(p => p.id === action.payload.postId);
        if (post) {
          post.is_liked = true;
          post.like_count += 1;
        }

        // Update current post
        if (state.currentPost?.id === action.payload.postId) {
          state.currentPost.is_liked = true;
          state.currentPost.like_count += 1;
        }
      })
      .addCase(likePost.rejected, (state, action) => {
        state.isLikingPost[action.meta.arg] = false;
        state.error = action.payload as string;
      });

    // Unlike post
    builder
      .addCase(unlikePost.pending, (state, action) => {
        state.isLikingPost[action.meta.arg] = true;
      })
      .addCase(unlikePost.fulfilled, (state, action) => {
        state.isLikingPost[action.payload.postId] = false;

        // Update post in posts array
        const post = state.posts.find(p => p.id === action.payload.postId);
        if (post) {
          post.is_liked = false;
          post.like_count -= 1;
        }

        // Update current post
        if (state.currentPost?.id === action.payload.postId) {
          state.currentPost.is_liked = false;
          state.currentPost.like_count -= 1;
        }
      })
      .addCase(unlikePost.rejected, (state, action) => {
        state.isLikingPost[action.meta.arg] = false;
        state.error = action.payload as string;
      });

    // Share post
    builder
      .addCase(sharePost.pending, (state, action) => {
        state.isSharingPost[action.meta.arg] = true;
      })
      .addCase(sharePost.fulfilled, (state, action) => {
        state.isSharingPost[action.payload.postId] = false;

        // Update post share count
        const post = state.posts.find(p => p.id === action.payload.postId);
        if (post) {
          post.share_count += 1;
        }

        if (state.currentPost?.id === action.payload.postId) {
          state.currentPost.share_count += 1;
        }
      })
      .addCase(sharePost.rejected, (state, action) => {
        state.isSharingPost[action.meta.arg] = false;
        state.error = action.payload as string;
      });

    // Fetch comments
    builder
      .addCase(fetchPostComments.pending, (state, action) => {
        state.commentsLoading[action.meta.arg.postId] = true;
      })
      .addCase(fetchPostComments.fulfilled, (state, action) => {
        state.commentsLoading[action.payload.postId] = false;
        state.comments[action.payload.postId] = action.payload.items;
      })
      .addCase(fetchPostComments.rejected, (state, action) => {
        state.commentsLoading[action.meta.arg.postId] = false;
        state.error = action.payload as string;
      });

    // Create comment
    builder
      .addCase(createComment.fulfilled, (state, action) => {
        const { postId, comment } = action.payload;

        if (!state.comments[postId]) {
          state.comments[postId] = [];
        }
        state.comments[postId].push(comment);

        // Update comment count in post
        const post = state.posts.find(p => p.id === postId);
        if (post) {
          post.comment_count += 1;
        }

        if (state.currentPost?.id === postId) {
          state.currentPost.comment_count += 1;
        }
      })
      .addCase(createComment.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Follow user
    builder
      .addCase(followUser.pending, (state, action) => {
        state.isFollowingUser[action.meta.arg] = true;
      })
      .addCase(followUser.fulfilled, (state, action) => {
        state.isFollowingUser[action.payload.userId] = false;

        // Update user profile if loaded
        const profile = state.userProfiles[action.payload.userId];
        if (profile) {
          profile.is_following = true;
          profile.follower_count += 1;
        }
      })
      .addCase(followUser.rejected, (state, action) => {
        state.isFollowingUser[action.meta.arg] = false;
        state.error = action.payload as string;
      });

    // Unfollow user
    builder
      .addCase(unfollowUser.pending, (state, action) => {
        state.isFollowingUser[action.meta.arg] = true;
      })
      .addCase(unfollowUser.fulfilled, (state, action) => {
        state.isFollowingUser[action.payload.userId] = false;

        // Update user profile if loaded
        const profile = state.userProfiles[action.payload.userId];
        if (profile) {
          profile.is_following = false;
          profile.follower_count -= 1;
        }
      })
      .addCase(unfollowUser.rejected, (state, action) => {
        state.isFollowingUser[action.meta.arg] = false;
        state.error = action.payload as string;
      });

    // Fetch user profile
    builder
      .addCase(fetchUserProfile.pending, (state, action) => {
        state.profilesLoading[action.meta.arg] = true;
      })
      .addCase(fetchUserProfile.fulfilled, (state, action) => {
        state.profilesLoading[action.payload.id] = false;
        state.userProfiles[action.payload.id] = action.payload;
      })
      .addCase(fetchUserProfile.rejected, (state, action) => {
        state.profilesLoading[action.meta.arg] = false;
        state.error = action.payload as string;
      });
  },
});

// Export actions
export const {
  clearError,
  updateFilters,
  updateFeedFilters,
  resetFeedFilters,
  clearPosts,
  clearCurrentPost,
  setComments,
  addComment,
  updatePostVotes,
  updateCommentVotes,
  setGroups,
  setFollowing,
  setFollowers,
  addFollow,
  removeFollow
} = communitySlice.actions;

// Export selectors
export const selectCommunityPosts = (state: RootState) => state.community.posts;
export const selectCurrentPost = (state: RootState) => state.community.currentPost;
export const selectComments = (state: RootState) => state.community.comments;
export const selectPostComments = (postId: string) => (state: RootState) => state.community.comments[postId] || [];
export const selectCommentsLoading = (postId: string) => (state: RootState) => state.community.commentsLoading[postId] || false;
export const selectGroups = (state: RootState) => state.community.groups;
export const selectFollowing = (state: RootState) => state.community.following;
export const selectFollowers = (state: RootState) => state.community.followers;
export const selectUserProfile = (userId: string) => (state: RootState) => state.community.userProfiles[userId];
export const selectProfileLoading = (userId: string) => (state: RootState) => state.community.profilesLoading[userId] || false;
export const selectCommunityLoading = (state: RootState) => state.community.isLoading;
export const selectPostingComment = (state: RootState) => state.community.isPostingComment;
export const selectIsCreatingPost = (state: RootState) => state.community.isCreatingPost;
export const selectIsLikingPost = (postId: string) => (state: RootState) => state.community.isLikingPost[postId] || false;
export const selectIsSharingPost = (postId: string) => (state: RootState) => state.community.isSharingPost[postId] || false;
export const selectIsFollowingUser = (userId: string) => (state: RootState) => state.community.isFollowingUser[userId] || false;
export const selectCommunityError = (state: RootState) => state.community.error;
export const selectCommunityFilters = (state: RootState) => state.community.filters;
export const selectFeedFilters = (state: RootState) => state.community.feedFilters;
export const selectFeedPagination = (state: RootState) => state.community.feedPagination;

// Export reducer
export default communitySlice.reducer;
