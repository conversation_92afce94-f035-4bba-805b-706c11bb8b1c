/**
 * Community Redux Slice
 * 
 * Manages community-related state including posts, comments, groups, and social interactions.
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { CommunityPost, PostComment, CommunityGroup, UserFollow } from '@/types/api';
import { communityService } from '@/services/api';

// Define the initial state interface
interface CommunityState {
  posts: CommunityPost[];
  currentPost: CommunityPost | null;
  comments: PostComment[];
  groups: CommunityGroup[];
  following: UserFollow[];
  followers: UserFollow[];
  isLoading: boolean;
  isPostingComment: boolean;
  error: string | null;
  filters: {
    post_type: string;
    group_id: string;
    search: string;
  };
}

// Initial state
const initialState: CommunityState = {
  posts: [],
  currentPost: null,
  comments: [],
  groups: [],
  following: [],
  followers: [],
  isLoading: false,
  isPostingComment: false,
  error: null,
  filters: {
    post_type: '',
    group_id: '',
    search: '',
  },
};

// Async thunks for community actions
export const fetchCommunityPosts = createAsyncThunk(
  'community/fetchPosts',
  async (params: { skip?: number; limit?: number; group_id?: string; post_type?: string; author_id?: string } = {}, { rejectWithValue }) => {
    try {
      const response = await communityService.getPosts(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.detail || 'Failed to fetch community posts');
    }
  }
);

export const fetchCommunityPost = createAsyncThunk(
  'community/fetchPost',
  async (postId: string, { rejectWithValue }) => {
    try {
      const response = await communityService.getPost(postId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.detail || 'Failed to fetch community post');
    }
  }
);

export const createCommunityPost = createAsyncThunk(
  'community/createPost',
  async (postData: Partial<CommunityPost>, { rejectWithValue }) => {
    try {
      const response = await communityService.createPost(postData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.detail || 'Failed to create community post');
    }
  }
);

// Create the community slice
const communitySlice = createSlice({
  name: 'community',
  initialState,
  reducers: {
    // Clear error state
    clearError: (state) => {
      state.error = null;
    },
    // Update filters
    updateFilters: (state, action: PayloadAction<Partial<CommunityState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    // Clear current post
    clearCurrentPost: (state) => {
      state.currentPost = null;
      state.comments = [];
    },
    // Set comments for current post
    setComments: (state, action: PayloadAction<PostComment[]>) => {
      state.comments = action.payload;
    },
    // Add comment
    addComment: (state, action: PayloadAction<PostComment>) => {
      state.comments.push(action.payload);
      // Update comment count on current post
      if (state.currentPost) {
        state.currentPost.comment_count += 1;
      }
    },
    // Update post votes
    updatePostVotes: (state, action: PayloadAction<{ postId: string; upvotes: number; downvotes: number }>) => {
      const post = state.posts.find(p => p.id === action.payload.postId);
      if (post) {
        post.upvotes = action.payload.upvotes;
        post.downvotes = action.payload.downvotes;
      }
      if (state.currentPost && state.currentPost.id === action.payload.postId) {
        state.currentPost.upvotes = action.payload.upvotes;
        state.currentPost.downvotes = action.payload.downvotes;
      }
    },
    // Update comment votes
    updateCommentVotes: (state, action: PayloadAction<{ commentId: string; upvotes: number; downvotes: number }>) => {
      const comment = state.comments.find(c => c.id === action.payload.commentId);
      if (comment) {
        comment.upvotes = action.payload.upvotes;
        comment.downvotes = action.payload.downvotes;
      }
    },
    // Set groups
    setGroups: (state, action: PayloadAction<CommunityGroup[]>) => {
      state.groups = action.payload;
    },
    // Set following/followers
    setFollowing: (state, action: PayloadAction<UserFollow[]>) => {
      state.following = action.payload;
    },
    setFollowers: (state, action: PayloadAction<UserFollow[]>) => {
      state.followers = action.payload;
    },
    // Add/remove follow
    addFollow: (state, action: PayloadAction<UserFollow>) => {
      state.following.push(action.payload);
    },
    removeFollow: (state, action: PayloadAction<string>) => {
      state.following = state.following.filter(f => f.id !== action.payload);
    },
  },
  extraReducers: (builder) => {
    // Fetch community posts
    builder
      .addCase(fetchCommunityPosts.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchCommunityPosts.fulfilled, (state, action: PayloadAction<CommunityPost[]>) => {
        state.isLoading = false;
        state.posts = action.payload;
        state.error = null;
      })
      .addCase(fetchCommunityPosts.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch single community post
    builder
      .addCase(fetchCommunityPost.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchCommunityPost.fulfilled, (state, action: PayloadAction<CommunityPost>) => {
        state.isLoading = false;
        state.currentPost = action.payload;
        state.error = null;
      })
      .addCase(fetchCommunityPost.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Create community post
    builder
      .addCase(createCommunityPost.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createCommunityPost.fulfilled, (state, action: PayloadAction<CommunityPost>) => {
        state.isLoading = false;
        state.posts.unshift(action.payload);
        state.error = null;
      })
      .addCase(createCommunityPost.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

// Export actions
export const { 
  clearError, 
  updateFilters, 
  clearCurrentPost, 
  setComments, 
  addComment, 
  updatePostVotes, 
  updateCommentVotes,
  setGroups,
  setFollowing,
  setFollowers,
  addFollow,
  removeFollow
} = communitySlice.actions;

// Export selectors
export const selectCommunityPosts = (state: { community: CommunityState }) => state.community.posts;
export const selectCurrentPost = (state: { community: CommunityState }) => state.community.currentPost;
export const selectComments = (state: { community: CommunityState }) => state.community.comments;
export const selectGroups = (state: { community: CommunityState }) => state.community.groups;
export const selectFollowing = (state: { community: CommunityState }) => state.community.following;
export const selectFollowers = (state: { community: CommunityState }) => state.community.followers;
export const selectCommunityLoading = (state: { community: CommunityState }) => state.community.isLoading;
export const selectPostingComment = (state: { community: CommunityState }) => state.community.isPostingComment;
export const selectCommunityError = (state: { community: CommunityState }) => state.community.error;
export const selectCommunityFilters = (state: { community: CommunityState }) => state.community.filters;

// Export reducer
export default communitySlice.reducer;
