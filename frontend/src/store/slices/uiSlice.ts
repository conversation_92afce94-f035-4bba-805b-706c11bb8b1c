/**
 * UI Redux Slice
 * 
 * Manages global UI state including modals, notifications, loading states, and theme.
 */

import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Define notification interface
interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

// Define modal interface
interface Modal {
  id: string;
  component: string;
  props?: Record<string, any>;
  size?: 'small' | 'medium' | 'large' | 'fullscreen';
}

// Define the initial state interface
interface UIState {
  theme: 'light' | 'dark' | 'system';
  sidebarOpen: boolean;
  notifications: Notification[];
  modals: Modal[];
  globalLoading: boolean;
  pageTitle: string;
  breadcrumbs: Array<{ label: string; href?: string }>;
  searchQuery: string;
  mobileMenuOpen: boolean;
}

// Initial state
const initialState: UIState = {
  theme: (localStorage.getItem('theme') as 'light' | 'dark' | 'system') || 'system',
  sidebarOpen: true,
  notifications: [],
  modals: [],
  globalLoading: false,
  pageTitle: 'Supplement Tracker',
  breadcrumbs: [],
  searchQuery: '',
  mobileMenuOpen: false,
};

// Create the UI slice
const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    // Theme management
    setTheme: (state, action: PayloadAction<'light' | 'dark' | 'system'>) => {
      state.theme = action.payload;
      localStorage.setItem('theme', action.payload);
    },

    // Sidebar management
    toggleSidebar: (state) => {
      state.sidebarOpen = !state.sidebarOpen;
    },
    setSidebarOpen: (state, action: PayloadAction<boolean>) => {
      state.sidebarOpen = action.payload;
    },

    // Mobile menu management
    toggleMobileMenu: (state) => {
      state.mobileMenuOpen = !state.mobileMenuOpen;
    },
    setMobileMenuOpen: (state, action: PayloadAction<boolean>) => {
      state.mobileMenuOpen = action.payload;
    },

    // Notification management
    addNotification: (state, action: PayloadAction<Omit<Notification, 'id'>>) => {
      const notification: Notification = {
        id: Date.now().toString(),
        ...action.payload,
      };
      state.notifications.push(notification);
    },
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(
        (notification) => notification.id !== action.payload
      );
    },
    clearNotifications: (state) => {
      state.notifications = [];
    },

    // Modal management
    openModal: (state, action: PayloadAction<Omit<Modal, 'id'>>) => {
      const modal: Modal = {
        id: Date.now().toString(),
        ...action.payload,
      };
      state.modals.push(modal);
    },
    closeModal: (state, action: PayloadAction<string>) => {
      state.modals = state.modals.filter((modal) => modal.id !== action.payload);
    },
    closeTopModal: (state) => {
      state.modals.pop();
    },
    clearModals: (state) => {
      state.modals = [];
    },

    // Global loading state
    setGlobalLoading: (state, action: PayloadAction<boolean>) => {
      state.globalLoading = action.payload;
    },

    // Page metadata
    setPageTitle: (state, action: PayloadAction<string>) => {
      state.pageTitle = action.payload;
      document.title = `${action.payload} - Supplement Tracker`;
    },
    setBreadcrumbs: (state, action: PayloadAction<Array<{ label: string; href?: string }>>) => {
      state.breadcrumbs = action.payload;
    },

    // Search
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },
    clearSearchQuery: (state) => {
      state.searchQuery = '';
    },
  },
});

// Export actions
export const {
  setTheme,
  toggleSidebar,
  setSidebarOpen,
  toggleMobileMenu,
  setMobileMenuOpen,
  addNotification,
  removeNotification,
  clearNotifications,
  openModal,
  closeModal,
  closeTopModal,
  clearModals,
  setGlobalLoading,
  setPageTitle,
  setBreadcrumbs,
  setSearchQuery,
  clearSearchQuery,
} = uiSlice.actions;

// Export selectors
export const selectTheme = (state: { ui: UIState }) => state.ui.theme;
export const selectSidebarOpen = (state: { ui: UIState }) => state.ui.sidebarOpen;
export const selectMobileMenuOpen = (state: { ui: UIState }) => state.ui.mobileMenuOpen;
export const selectNotifications = (state: { ui: UIState }) => state.ui.notifications;
export const selectModals = (state: { ui: UIState }) => state.ui.modals;
export const selectGlobalLoading = (state: { ui: UIState }) => state.ui.globalLoading;
export const selectPageTitle = (state: { ui: UIState }) => state.ui.pageTitle;
export const selectBreadcrumbs = (state: { ui: UIState }) => state.ui.breadcrumbs;
export const selectSearchQuery = (state: { ui: UIState }) => state.ui.searchQuery;

// Helper action creators for common notification types
export const showSuccessNotification = (message: string, title = 'Success') =>
  addNotification({ title, message, type: 'success', duration: 5000 });

export const showErrorNotification = (message: string, title = 'Error') =>
  addNotification({ title, message, type: 'error', duration: 8000 });

export const showWarningNotification = (message: string, title = 'Warning') =>
  addNotification({ title, message, type: 'warning', duration: 6000 });

export const showInfoNotification = (message: string, title = 'Info') =>
  addNotification({ title, message, type: 'info', duration: 5000 });

// Export reducer
export default uiSlice.reducer;
