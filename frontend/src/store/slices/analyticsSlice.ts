/**
 * Analytics Slice
 * 
 * Redux slice for analytics data, insights, and visualization state management.
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '@/store';
import { analyticsService } from '@/services/api';

// Types
interface AnalyticsMetrics {
  totalSupplements: number;
  adherenceRate: number;
  totalIntakes: number;
  streakDays: number;
  supplementsChange: number;
  adherenceChange: number;
  intakesChange: number;
  streakChange: number;
}

interface TrendPoint {
  date: string;
  value: number;
}

interface DistributionItem {
  name: string;
  count: number;
  color?: string;
}

interface AnalyticsTrends {
  adherence: TrendPoint[];
  intakes: TrendPoint[];
  supplements: TrendPoint[];
}

interface AnalyticsDistribution {
  supplements: DistributionItem[];
  categories: DistributionItem[];
  brands: DistributionItem[];
}

interface AnalyticsInsight {
  type: 'positive' | 'negative' | 'neutral' | 'recommendation';
  text: string;
  confidence: number;
  metadata?: any;
}

interface AnalyticsData {
  metrics: AnalyticsMetrics;
  trends: AnalyticsTrends;
  distribution: AnalyticsDistribution;
  insights: AnalyticsInsight[];
  correlations?: any[];
  recommendations?: any[];
}

interface AnalyticsFilters {
  timeRange: string;
  supplementIds: string[];
  startDate?: string;
  endDate?: string;
  groupBy?: 'day' | 'week' | 'month';
}

interface AnalyticsState {
  data: AnalyticsData | null;
  isLoading: boolean;
  isExporting: boolean;
  error: string | null;
  filters: AnalyticsFilters;
  lastUpdated: string | null;
}

// Async thunks
export const fetchAnalyticsData = createAsyncThunk(
  'analytics/fetchData',
  async (params: {
    timeRange?: string;
    supplementIds?: string[];
    startDate?: string;
    endDate?: string;
    groupBy?: 'day' | 'week' | 'month';
  }) => {
    const response = await analyticsService.getAnalyticsData(params);
    return response;
  }
);

export const fetchCorrelationAnalysis = createAsyncThunk(
  'analytics/fetchCorrelations',
  async (params: {
    supplementIds: string[];
    timeRange?: string;
    metrics?: string[];
  }) => {
    const response = await analyticsService.getCorrelationAnalysis(params);
    return response;
  }
);

export const generateInsights = createAsyncThunk(
  'analytics/generateInsights',
  async (params: {
    timeRange?: string;
    supplementIds?: string[];
  }) => {
    const response = await analyticsService.generateInsights(params);
    return response;
  }
);

export const exportAnalyticsData = createAsyncThunk(
  'analytics/exportData',
  async (params: {
    format: 'csv' | 'json' | 'pdf';
    timeRange?: string;
    supplementIds?: string[];
    includeCharts?: boolean;
  }) => {
    const response = await analyticsService.exportData(params);
    return response;
  }
);

export const fetchSupplementComparison = createAsyncThunk(
  'analytics/fetchComparison',
  async (params: {
    supplementIds: string[];
    metrics: string[];
    timeRange?: string;
  }) => {
    const response = await analyticsService.getSupplementComparison(params);
    return response;
  }
);

export const fetchPredictiveAnalysis = createAsyncThunk(
  'analytics/fetchPredictive',
  async (params: {
    supplementId: string;
    predictionDays: number;
    includeFactors?: string[];
  }) => {
    const response = await analyticsService.getPredictiveAnalysis(params);
    return response;
  }
);

// Initial state
const initialState: AnalyticsState = {
  data: null,
  isLoading: false,
  isExporting: false,
  error: null,
  filters: {
    timeRange: '30d',
    supplementIds: [],
    groupBy: 'day',
  },
  lastUpdated: null,
};

// Analytics slice
const analyticsSlice = createSlice({
  name: 'analytics',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateAnalyticsFilters: (state, action: PayloadAction<Partial<AnalyticsFilters>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    resetAnalyticsFilters: (state) => {
      state.filters = initialState.filters;
    },
    clearAnalyticsData: (state) => {
      state.data = null;
      state.lastUpdated = null;
    },
    setAnalyticsData: (state, action: PayloadAction<AnalyticsData>) => {
      state.data = action.payload;
      state.lastUpdated = new Date().toISOString();
    },
  },
  extraReducers: (builder) => {
    // Fetch analytics data
    builder
      .addCase(fetchAnalyticsData.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchAnalyticsData.fulfilled, (state, action) => {
        state.isLoading = false;
        state.data = action.payload;
        state.lastUpdated = new Date().toISOString();
      })
      .addCase(fetchAnalyticsData.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch analytics data';
      });

    // Fetch correlation analysis
    builder
      .addCase(fetchCorrelationAnalysis.fulfilled, (state, action) => {
        if (state.data) {
          state.data.correlations = action.payload;
        }
      })
      .addCase(fetchCorrelationAnalysis.rejected, (state, action) => {
        state.error = action.error.message || 'Failed to fetch correlation analysis';
      });

    // Generate insights
    builder
      .addCase(generateInsights.fulfilled, (state, action) => {
        if (state.data) {
          state.data.insights = action.payload;
        }
      })
      .addCase(generateInsights.rejected, (state, action) => {
        state.error = action.error.message || 'Failed to generate insights';
      });

    // Export analytics data
    builder
      .addCase(exportAnalyticsData.pending, (state) => {
        state.isExporting = true;
        state.error = null;
      })
      .addCase(exportAnalyticsData.fulfilled, (state, action) => {
        state.isExporting = false;
        // Handle export success (e.g., download file)
      })
      .addCase(exportAnalyticsData.rejected, (state, action) => {
        state.isExporting = false;
        state.error = action.error.message || 'Failed to export data';
      });

    // Fetch supplement comparison
    builder
      .addCase(fetchSupplementComparison.fulfilled, (state, action) => {
        if (state.data) {
          state.data.recommendations = action.payload;
        }
      })
      .addCase(fetchSupplementComparison.rejected, (state, action) => {
        state.error = action.error.message || 'Failed to fetch supplement comparison';
      });

    // Fetch predictive analysis
    builder
      .addCase(fetchPredictiveAnalysis.fulfilled, (state, action) => {
        if (state.data) {
          // Add predictive data to trends or create new section
          state.data.trends = { ...state.data.trends, ...action.payload };
        }
      })
      .addCase(fetchPredictiveAnalysis.rejected, (state, action) => {
        state.error = action.error.message || 'Failed to fetch predictive analysis';
      });
  },
});

// Export actions
export const { 
  clearError, 
  updateAnalyticsFilters, 
  resetAnalyticsFilters,
  clearAnalyticsData,
  setAnalyticsData
} = analyticsSlice.actions;

// Selectors
export const selectAnalyticsData = (state: RootState) => state.analytics.data;
export const selectAnalyticsLoading = (state: RootState) => state.analytics.isLoading;
export const selectAnalyticsExporting = (state: RootState) => state.analytics.isExporting;
export const selectAnalyticsError = (state: RootState) => state.analytics.error;
export const selectAnalyticsFilters = (state: RootState) => state.analytics.filters;
export const selectAnalyticsLastUpdated = (state: RootState) => state.analytics.lastUpdated;

// Computed selectors
export const selectAnalyticsMetrics = (state: RootState) => state.analytics.data?.metrics;
export const selectAnalyticsTrends = (state: RootState) => state.analytics.data?.trends;
export const selectAnalyticsDistribution = (state: RootState) => state.analytics.data?.distribution;
export const selectAnalyticsInsights = (state: RootState) => state.analytics.data?.insights || [];
export const selectAnalyticsCorrelations = (state: RootState) => state.analytics.data?.correlations || [];
export const selectAnalyticsRecommendations = (state: RootState) => state.analytics.data?.recommendations || [];

// Helper selectors
export const selectIsAnalyticsDataStale = (state: RootState) => {
  if (!state.analytics.lastUpdated) return true;
  
  const lastUpdated = new Date(state.analytics.lastUpdated);
  const now = new Date();
  const hoursSinceUpdate = (now.getTime() - lastUpdated.getTime()) / (1000 * 60 * 60);
  
  return hoursSinceUpdate > 1; // Consider data stale after 1 hour
};

export const selectAnalyticsDataForTimeRange = (timeRange: string) => (state: RootState) => {
  const data = state.analytics.data;
  if (!data || state.analytics.filters.timeRange !== timeRange) return null;
  
  return data;
};

export const selectTopSupplementsByAdherence = (state: RootState) => {
  const distribution = state.analytics.data?.distribution.supplements;
  if (!distribution) return [];
  
  return [...distribution]
    .sort((a, b) => b.count - a.count)
    .slice(0, 5);
};

export const selectAdherenceTrend = (state: RootState) => {
  const trends = state.analytics.data?.trends.adherence;
  if (!trends || trends.length < 2) return 'stable';
  
  const recent = trends.slice(-7); // Last 7 data points
  const average = recent.reduce((sum, point) => sum + point.value, 0) / recent.length;
  const previousAverage = trends.slice(-14, -7).reduce((sum, point) => sum + point.value, 0) / 7;
  
  if (average > previousAverage + 5) return 'improving';
  if (average < previousAverage - 5) return 'declining';
  return 'stable';
};

export const selectInsightsByType = (type: AnalyticsInsight['type']) => (state: RootState) => {
  const insights = state.analytics.data?.insights || [];
  return insights.filter(insight => insight.type === type);
};

// Export reducer
export default analyticsSlice.reducer;
