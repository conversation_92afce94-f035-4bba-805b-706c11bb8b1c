/**
 * Line Chart Component
 * 
 * Reusable line chart component for time-series data visualization.
 */

import React, { useRef, useEffect } from 'react';
import { useTheme } from 'styled-components';
import styled from 'styled-components';
import * as d3 from 'd3';

// Types
interface DataPoint {
  date: Date;
  value: number;
  label?: string;
}

interface LineChartProps {
  data: DataPoint[];
  width?: number;
  height?: number;
  margin?: { top: number; right: number; bottom: number; left: number };
  xAxisLabel?: string;
  yAxisLabel?: string;
  title?: string;
  color?: string;
  showDots?: boolean;
  showGrid?: boolean;
  animate?: boolean;
  onPointHover?: (point: DataPoint | null) => void;
  className?: string;
}

// Styled components
const ChartContainer = styled.div`
  position: relative;
  background: ${props => props.theme.colors.background};
  border-radius: ${props => props.theme.borderRadius.medium};
  padding: 1rem;
  box-shadow: 0 2px 8px ${props => props.theme.colors.shadow}10;
`;

const ChartTitle = styled.h3`
  color: ${props => props.theme.colors.text};
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: ${props => props.theme.typography.fontWeight.semibold};
  text-align: center;
`;

const ChartSvg = styled.svg`
  display: block;
  width: 100%;
  height: auto;
  
  .line {
    fill: none;
    stroke-width: 2;
    stroke-linecap: round;
    stroke-linejoin: round;
  }
  
  .dot {
    stroke-width: 2;
    cursor: pointer;
    transition: r 0.2s ease;
    
    &:hover {
      r: 6;
    }
  }
  
  .grid-line {
    stroke: ${props => props.theme.colors.border};
    stroke-width: 1;
    stroke-dasharray: 2,2;
    opacity: 0.5;
  }
  
  .axis {
    .domain {
      stroke: ${props => props.theme.colors.border};
    }
    
    .tick line {
      stroke: ${props => props.theme.colors.border};
    }
    
    .tick text {
      fill: ${props => props.theme.colors.textSecondary};
      font-size: 12px;
    }
  }
  
  .axis-label {
    fill: ${props => props.theme.colors.text};
    font-size: 14px;
    font-weight: 500;
  }
`;

const Tooltip = styled.div<{ $x: number; $y: number; $visible: boolean }>`
  position: absolute;
  background: ${props => props.theme.colors.backgroundSecondary};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.small};
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  color: ${props => props.theme.colors.text};
  pointer-events: none;
  z-index: 1000;
  box-shadow: 0 4px 12px ${props => props.theme.colors.shadow}20;
  opacity: ${props => props.$visible ? 1 : 0};
  transform: translate(${props => props.$x}px, ${props => props.$y}px);
  transition: opacity 0.2s ease;
  
  &::before {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: ${props => props.theme.colors.border};
  }
`;

const LineChart: React.FC<LineChartProps> = ({
  data,
  width = 600,
  height = 300,
  margin = { top: 20, right: 30, bottom: 40, left: 50 },
  xAxisLabel,
  yAxisLabel,
  title,
  color,
  showDots = true,
  showGrid = true,
  animate = true,
  onPointHover,
  className,
}) => {
  const theme = useTheme();
  const svgRef = useRef<SVGSVGElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const [tooltipData, setTooltipData] = React.useState<{
    point: DataPoint | null;
    x: number;
    y: number;
    visible: boolean;
  }>({
    point: null,
    x: 0,
    y: 0,
    visible: false,
  });

  const chartColor = color || theme.colors.primary;
  const innerWidth = width - margin.left - margin.right;
  const innerHeight = height - margin.top - margin.bottom;

  useEffect(() => {
    if (!svgRef.current || data.length === 0) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    // Create scales
    const xScale = d3.scaleTime()
      .domain(d3.extent(data, d => d.date) as [Date, Date])
      .range([0, innerWidth]);

    const yScale = d3.scaleLinear()
      .domain(d3.extent(data, d => d.value) as [number, number])
      .nice()
      .range([innerHeight, 0]);

    // Create main group
    const g = svg.append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Add grid lines
    if (showGrid) {
      // Horizontal grid lines
      g.selectAll('.grid-line-horizontal')
        .data(yScale.ticks())
        .enter()
        .append('line')
        .attr('class', 'grid-line grid-line-horizontal')
        .attr('x1', 0)
        .attr('x2', innerWidth)
        .attr('y1', d => yScale(d))
        .attr('y2', d => yScale(d));

      // Vertical grid lines
      g.selectAll('.grid-line-vertical')
        .data(xScale.ticks())
        .enter()
        .append('line')
        .attr('class', 'grid-line grid-line-vertical')
        .attr('x1', d => xScale(d))
        .attr('x2', d => xScale(d))
        .attr('y1', 0)
        .attr('y2', innerHeight);
    }

    // Create line generator
    const line = d3.line<DataPoint>()
      .x(d => xScale(d.date))
      .y(d => yScale(d.value))
      .curve(d3.curveMonotoneX);

    // Add the line
    const path = g.append('path')
      .datum(data)
      .attr('class', 'line')
      .attr('d', line)
      .attr('stroke', chartColor);

    // Animate line drawing
    if (animate) {
      const totalLength = (path.node() as SVGPathElement).getTotalLength();
      path
        .attr('stroke-dasharray', `${totalLength} ${totalLength}`)
        .attr('stroke-dashoffset', totalLength)
        .transition()
        .duration(1500)
        .ease(d3.easeLinear)
        .attr('stroke-dashoffset', 0);
    }

    // Add dots
    if (showDots) {
      const dots = g.selectAll('.dot')
        .data(data)
        .enter()
        .append('circle')
        .attr('class', 'dot')
        .attr('cx', d => xScale(d.date))
        .attr('cy', d => yScale(d.value))
        .attr('r', 4)
        .attr('fill', chartColor)
        .attr('stroke', theme.colors.background)
        .style('opacity', animate ? 0 : 1);

      // Animate dots
      if (animate) {
        dots.transition()
          .delay((d, i) => i * 50)
          .duration(300)
          .style('opacity', 1);
      }

      // Add hover interactions
      dots
        .on('mouseover', function(event, d) {
          const [mouseX, mouseY] = d3.pointer(event, document.body);
          setTooltipData({
            point: d,
            x: mouseX,
            y: mouseY - 10,
            visible: true,
          });
          onPointHover?.(d);
        })
        .on('mouseout', function() {
          setTooltipData(prev => ({ ...prev, visible: false }));
          onPointHover?.(null);
        });
    }

    // Add X axis
    const xAxis = d3.axisBottom(xScale)
      .tickFormat(d3.timeFormat('%m/%d') as any);

    g.append('g')
      .attr('class', 'axis x-axis')
      .attr('transform', `translate(0,${innerHeight})`)
      .call(xAxis);

    // Add Y axis
    const yAxis = d3.axisLeft(yScale);

    g.append('g')
      .attr('class', 'axis y-axis')
      .call(yAxis);

    // Add axis labels
    if (xAxisLabel) {
      g.append('text')
        .attr('class', 'axis-label x-axis-label')
        .attr('text-anchor', 'middle')
        .attr('x', innerWidth / 2)
        .attr('y', innerHeight + margin.bottom - 5)
        .text(xAxisLabel);
    }

    if (yAxisLabel) {
      g.append('text')
        .attr('class', 'axis-label y-axis-label')
        .attr('text-anchor', 'middle')
        .attr('transform', 'rotate(-90)')
        .attr('x', -innerHeight / 2)
        .attr('y', -margin.left + 15)
        .text(yAxisLabel);
    }

  }, [data, width, height, margin, chartColor, showDots, showGrid, animate, theme, innerWidth, innerHeight, xAxisLabel, yAxisLabel, onPointHover]);

  const formatTooltipValue = (value: number) => {
    return value.toLocaleString(undefined, { 
      minimumFractionDigits: 0, 
      maximumFractionDigits: 2 
    });
  };

  const formatTooltipDate = (date: Date) => {
    return date.toLocaleDateString(undefined, { 
      month: 'short', 
      day: 'numeric',
      year: 'numeric'
    });
  };

  return (
    <ChartContainer className={className}>
      {title && <ChartTitle>{title}</ChartTitle>}
      <ChartSvg
        ref={svgRef}
        width={width}
        height={height}
        viewBox={`0 0 ${width} ${height}`}
      />
      <Tooltip
        ref={tooltipRef}
        $x={tooltipData.x}
        $y={tooltipData.y}
        $visible={tooltipData.visible}
      >
        {tooltipData.point && (
          <>
            <div><strong>{tooltipData.point.label || 'Value'}</strong></div>
            <div>{formatTooltipValue(tooltipData.point.value)}</div>
            <div>{formatTooltipDate(tooltipData.point.date)}</div>
          </>
        )}
      </Tooltip>
    </ChartContainer>
  );
};

export default LineChart;
