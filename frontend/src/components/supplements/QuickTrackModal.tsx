/**
 * Quick Track Modal Component
 * 
 * Modal for quickly logging supplement intake with dosage and notes.
 */

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import styled from 'styled-components';
import { Supplement } from '@/types/api';

// Components
import Button from '@/components/common/Button';
import Input from '@/components/common/Input';
import LoadingSpinner from '@/components/common/LoadingSpinner';

// Styled components
const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: ${props => props.theme.zIndex.modal};
  padding: 1rem;
`;

const ModalContent = styled.div`
  background: ${props => props.theme.colors.surface};
  border-radius: ${props => props.theme.borderRadius.large};
  padding: 2rem;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
`;

const ModalHeader = styled.div`
  margin-bottom: 1.5rem;
  
  h2 {
    color: ${props => props.theme.colors.text};
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
  }
  
  p {
    color: ${props => props.theme.colors.textSecondary};
    margin: 0;
  }
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const Label = styled.label`
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  color: ${props => props.theme.colors.text};
  font-size: 0.9rem;
`;

const DosageRow = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 0.75rem;
  
  @media (max-width: ${props => props.theme.breakpoints.mobile}) {
    grid-template-columns: 1fr;
  }
`;

const UnitSelect = styled.select`
  padding: 0.75rem 1rem;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.medium};
  background: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
  font-size: 0.9375rem;
  font-family: inherit;
  
  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 2px ${props => props.theme.colors.primary}20;
  }
  
  &:disabled {
    background: ${props => props.theme.colors.backgroundSecondary};
    color: ${props => props.theme.colors.textSecondary};
    cursor: not-allowed;
    opacity: 0.6;
  }
`;

const TextArea = styled.textarea`
  padding: 0.75rem 1rem;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.medium};
  background: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
  font-size: 0.9375rem;
  font-family: inherit;
  resize: vertical;
  min-height: 80px;
  
  &::placeholder {
    color: ${props => props.theme.colors.textSecondary};
  }
  
  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 2px ${props => props.theme.colors.primary}20;
  }
  
  &:disabled {
    background: ${props => props.theme.colors.backgroundSecondary};
    color: ${props => props.theme.colors.textSecondary};
    cursor: not-allowed;
    opacity: 0.6;
  }
`;

const ErrorMessage = styled.div`
  color: ${props => props.theme.colors.error};
  font-size: 0.8rem;
  margin-top: 0.25rem;
`;

const QuickDosages = styled.div`
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-top: 0.5rem;
`;

const QuickDosageButton = styled.button<{ $active: boolean }>`
  background: ${props => props.$active ? props.theme.colors.primary : props.theme.colors.backgroundSecondary};
  color: ${props => props.$active ? 'white' : props.theme.colors.text};
  border: 1px solid ${props => props.$active ? props.theme.colors.primary : props.theme.colors.border};
  padding: 0.5rem 0.75rem;
  border-radius: ${props => props.theme.borderRadius.small};
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: ${props => props.$active ? props.theme.colors.primaryDark : props.theme.colors.hover};
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const Actions = styled.div`
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
  
  @media (max-width: ${props => props.theme.breakpoints.mobile}) {
    flex-direction: column;
  }
`;

// Common dosage units
const DOSAGE_UNITS = [
  'mg',
  'g',
  'mcg',
  'IU',
  'ml',
  'capsules',
  'tablets',
  'drops',
  'scoops',
];

// Common dosage amounts (will be filtered based on unit)
const QUICK_DOSAGES = {
  mg: [100, 250, 500, 1000],
  g: [1, 2, 5, 10],
  mcg: [25, 50, 100, 200],
  IU: [400, 1000, 2000, 5000],
  ml: [5, 10, 15, 30],
  capsules: [1, 2, 3, 4],
  tablets: [1, 2, 3, 4],
  drops: [1, 2, 3, 5],
  scoops: [1, 2],
};

interface QuickTrackFormData {
  dosage: number;
  dosage_unit: string;
  notes: string;
}

interface QuickTrackModalProps {
  supplement: Supplement;
  onTrack: (dosage: number, dosageUnit: string, notes?: string) => void;
  onClose: () => void;
  isLoading?: boolean;
}

const QuickTrackModal: React.FC<QuickTrackModalProps> = ({
  supplement,
  onTrack,
  onClose,
  isLoading = false,
}) => {
  const [selectedQuickDosage, setSelectedQuickDosage] = useState<number | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    setFocus,
  } = useForm<QuickTrackFormData>({
    defaultValues: {
      dosage: supplement.serving_size ? parseFloat(supplement.serving_size) : 1,
      dosage_unit: supplement.serving_unit || 'mg',
      notes: '',
    },
  });

  const watchedDosage = watch('dosage');
  const watchedUnit = watch('dosage_unit');

  // Focus dosage input on mount
  useEffect(() => {
    setFocus('dosage');
  }, [setFocus]);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [onClose]);

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleQuickDosage = (dosage: number) => {
    setValue('dosage', dosage);
    setSelectedQuickDosage(dosage);
  };

  const onSubmit = (data: QuickTrackFormData) => {
    onTrack(data.dosage, data.dosage_unit, data.notes || undefined);
  };

  const quickDosages = QUICK_DOSAGES[watchedUnit as keyof typeof QUICK_DOSAGES] || [];

  return (
    <ModalOverlay onClick={handleOverlayClick}>
      <ModalContent>
        <ModalHeader>
          <h2>Log Supplement Intake</h2>
          <p>
            {supplement.name}
            {supplement.brand && ` by ${supplement.brand}`}
          </p>
        </ModalHeader>

        <Form onSubmit={handleSubmit(onSubmit)}>
          <FormGroup>
            <Label htmlFor="dosage">Dosage</Label>
            <DosageRow>
              <Input
                id="dosage"
                type="number"
                step="0.1"
                min="0"
                placeholder="Enter dosage"
                {...register('dosage', {
                  required: 'Dosage is required',
                  min: { value: 0.1, message: 'Dosage must be greater than 0' },
                })}
                error={!!errors.dosage}
                disabled={isLoading}
              />
              <UnitSelect
                {...register('dosage_unit', { required: 'Unit is required' })}
                disabled={isLoading}
              >
                {DOSAGE_UNITS.map(unit => (
                  <option key={unit} value={unit}>
                    {unit}
                  </option>
                ))}
              </UnitSelect>
            </DosageRow>
            {errors.dosage && (
              <ErrorMessage>{errors.dosage.message}</ErrorMessage>
            )}
            
            {quickDosages.length > 0 && (
              <QuickDosages>
                {quickDosages.map(dosage => (
                  <QuickDosageButton
                    key={dosage}
                    type="button"
                    $active={selectedQuickDosage === dosage || watchedDosage === dosage}
                    onClick={() => handleQuickDosage(dosage)}
                    disabled={isLoading}
                  >
                    {dosage} {watchedUnit}
                  </QuickDosageButton>
                ))}
              </QuickDosages>
            )}
          </FormGroup>

          <FormGroup>
            <Label htmlFor="notes">Notes (Optional)</Label>
            <TextArea
              id="notes"
              placeholder="Add any notes about this intake (e.g., taken with food, time of day, etc.)"
              {...register('notes')}
              disabled={isLoading}
            />
          </FormGroup>

          <Actions>
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
              fullWidth
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={isLoading}
              fullWidth
            >
              {isLoading ? <LoadingSpinner size="small" /> : 'Log Intake'}
            </Button>
          </Actions>
        </Form>
      </ModalContent>
    </ModalOverlay>
  );
};

export default QuickTrackModal;
