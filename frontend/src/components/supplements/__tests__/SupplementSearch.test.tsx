/**
 * SupplementSearch Component Tests
 * 
 * Test-driven development tests for the SupplementSearch component.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Provider } from 'react-redux';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { ThemeProvider } from 'styled-components';
import { configureStore } from '@reduxjs/toolkit';

// Components and store
import SupplementSearch from '../SupplementSearch';
import supplementReducer from '@/store/slices/supplementSlice';
import uiReducer from '@/store/slices/uiSlice';
import { lightTheme } from '@/styles/theme';

// Mock API service
jest.mock('@/services/api', () => ({
  supplementService: {
    getSupplements: jest.fn(),
  },
}));

// Mock hooks
jest.mock('@/hooks/useDebounce', () => ({
  useDebounce: jest.fn((value) => value), // Return value immediately for tests
}));

// Test data
const mockSupplements = [
  {
    id: '1',
    name: 'Vitamin D3',
    brand: 'Nature Made',
    category: 'vitamins',
    description: 'Supports bone health and immune function',
    serving_size: '2000',
    serving_unit: 'IU',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: '2',
    name: 'Omega-3 Fish Oil',
    brand: 'Nordic Naturals',
    category: 'omega-3',
    description: 'High-quality fish oil supplement',
    serving_size: '1000',
    serving_unit: 'mg',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
];

// Test store setup
const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      supplements: supplementReducer,
      ui: uiReducer,
    },
    preloadedState: {
      supplements: {
        supplements: [],
        currentSupplement: null,
        intakeHistory: [],
        recentIntakes: [],
        isLoading: false,
        isTrackingIntake: false,
        error: null,
        pagination: {
          total: 0,
          page: 1,
          size: 20,
          pages: 0,
        },
        filters: {
          search: '',
          category: '',
        },
      },
      ui: {
        theme: 'light',
        sidebarOpen: true,
        notifications: [],
        modals: [],
        globalLoading: false,
        pageTitle: 'Supplement Tracker',
        breadcrumbs: [],
        searchQuery: '',
        mobileMenuOpen: false,
      },
      ...initialState,
    },
  });
};

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode; store?: any }> = ({ 
  children, 
  store = createTestStore() 
}) => (
  <Provider store={store}>
    <BrowserRouter>
      <ThemeProvider theme={lightTheme}>
        {children}
      </ThemeProvider>
    </BrowserRouter>
  </Provider>
);

describe('SupplementSearch', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render search input and filters', () => {
      render(
        <TestWrapper>
          <SupplementSearch />
        </TestWrapper>
      );

      expect(screen.getByPlaceholderText(/search supplements/i)).toBeInTheDocument();
      expect(screen.getByDisplayValue(/all categories/i)).toBeInTheDocument();
    });

    it('should render compact mode correctly', () => {
      render(
        <TestWrapper>
          <SupplementSearch compact />
        </TestWrapper>
      );

      expect(screen.getByRole('button', { name: /filters/i })).toBeInTheDocument();
    });

    it('should show quick add buttons when enabled', () => {
      const store = createTestStore({
        supplements: {
          supplements: mockSupplements,
          currentSupplement: null,
          intakeHistory: [],
          recentIntakes: [],
          isLoading: false,
          isTrackingIntake: false,
          error: null,
          pagination: {
            total: 2,
            page: 1,
            size: 20,
            pages: 1,
          },
          filters: {
            search: '',
            category: '',
          },
        },
      });

      render(
        <TestWrapper store={store}>
          <SupplementSearch showQuickAdd />
        </TestWrapper>
      );

      expect(screen.getAllByText(/quick add/i)).toHaveLength(2);
    });
  });

  describe('Search Functionality', () => {
    it('should update search input value', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <SupplementSearch />
        </TestWrapper>
      );

      const searchInput = screen.getByPlaceholderText(/search supplements/i);
      await user.type(searchInput, 'vitamin');

      expect(searchInput).toHaveValue('vitamin');
    });

    it('should show clear button when search has value', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <SupplementSearch />
        </TestWrapper>
      );

      const searchInput = screen.getByPlaceholderText(/search supplements/i);
      await user.type(searchInput, 'vitamin');

      expect(screen.getByRole('button')).toBeInTheDocument(); // Clear button
    });

    it('should clear search when clear button is clicked', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <SupplementSearch />
        </TestWrapper>
      );

      const searchInput = screen.getByPlaceholderText(/search supplements/i);
      await user.type(searchInput, 'vitamin');
      
      const clearButton = screen.getByRole('button');
      await user.click(clearButton);

      expect(searchInput).toHaveValue('');
    });
  });

  describe('Category Filtering', () => {
    it('should update category filter', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <SupplementSearch />
        </TestWrapper>
      );

      const categorySelect = screen.getByDisplayValue(/all categories/i);
      await user.selectOptions(categorySelect, 'vitamins');

      expect(categorySelect).toHaveValue('vitamins');
    });

    it('should show clear filters button when filters are active', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <SupplementSearch />
        </TestWrapper>
      );

      const categorySelect = screen.getByDisplayValue(/all categories/i);
      await user.selectOptions(categorySelect, 'vitamins');

      await waitFor(() => {
        expect(screen.getByRole('button', { name: /clear filters/i })).toBeInTheDocument();
      });
    });

    it('should clear all filters when clear button is clicked', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <SupplementSearch />
        </TestWrapper>
      );

      const searchInput = screen.getByPlaceholderText(/search supplements/i);
      const categorySelect = screen.getByDisplayValue(/all categories/i);

      await user.type(searchInput, 'vitamin');
      await user.selectOptions(categorySelect, 'vitamins');

      const clearButton = screen.getByRole('button', { name: /clear filters/i });
      await user.click(clearButton);

      expect(searchInput).toHaveValue('');
      expect(categorySelect).toHaveValue('');
    });
  });

  describe('Results Display', () => {
    it('should show loading state', () => {
      const store = createTestStore({
        supplements: {
          supplements: [],
          currentSupplement: null,
          intakeHistory: [],
          recentIntakes: [],
          isLoading: true,
          isTrackingIntake: false,
          error: null,
          pagination: {
            total: 0,
            page: 1,
            size: 20,
            pages: 0,
          },
          filters: {
            search: '',
            category: '',
          },
        },
      });

      render(
        <TestWrapper store={store}>
          <SupplementSearch />
        </TestWrapper>
      );

      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
    });

    it('should show empty state when no supplements found', () => {
      const store = createTestStore({
        supplements: {
          supplements: [],
          currentSupplement: null,
          intakeHistory: [],
          recentIntakes: [],
          isLoading: false,
          isTrackingIntake: false,
          error: null,
          pagination: {
            total: 0,
            page: 1,
            size: 20,
            pages: 0,
          },
          filters: {
            search: 'nonexistent',
            category: '',
          },
        },
      });

      render(
        <TestWrapper store={store}>
          <SupplementSearch />
        </TestWrapper>
      );

      expect(screen.getByText(/no supplements found/i)).toBeInTheDocument();
      expect(screen.getByText(/try adjusting your search terms/i)).toBeInTheDocument();
    });

    it('should display supplement results', () => {
      const store = createTestStore({
        supplements: {
          supplements: mockSupplements,
          currentSupplement: null,
          intakeHistory: [],
          recentIntakes: [],
          isLoading: false,
          isTrackingIntake: false,
          error: null,
          pagination: {
            total: 2,
            page: 1,
            size: 20,
            pages: 1,
          },
          filters: {
            search: '',
            category: '',
          },
        },
      });

      render(
        <TestWrapper store={store}>
          <SupplementSearch />
        </TestWrapper>
      );

      expect(screen.getByText('Vitamin D3')).toBeInTheDocument();
      expect(screen.getByText('Omega-3 Fish Oil')).toBeInTheDocument();
      expect(screen.getByText('2 supplements found')).toBeInTheDocument();
    });

    it('should show load more button when there are more results', () => {
      const store = createTestStore({
        supplements: {
          supplements: mockSupplements,
          currentSupplement: null,
          intakeHistory: [],
          recentIntakes: [],
          isLoading: false,
          isTrackingIntake: false,
          error: null,
          pagination: {
            total: 50,
            page: 1,
            size: 20,
            pages: 3,
          },
          filters: {
            search: '',
            category: '',
          },
        },
      });

      render(
        <TestWrapper store={store}>
          <SupplementSearch />
        </TestWrapper>
      );

      expect(screen.getByRole('button', { name: /load more/i })).toBeInTheDocument();
    });
  });

  describe('Supplement Selection', () => {
    it('should call onSupplementSelect when supplement is selected', async () => {
      const mockOnSelect = jest.fn();
      const user = userEvent.setup();
      
      const store = createTestStore({
        supplements: {
          supplements: mockSupplements,
          currentSupplement: null,
          intakeHistory: [],
          recentIntakes: [],
          isLoading: false,
          isTrackingIntake: false,
          error: null,
          pagination: {
            total: 2,
            page: 1,
            size: 20,
            pages: 1,
          },
          filters: {
            search: '',
            category: '',
          },
        },
      });

      render(
        <TestWrapper store={store}>
          <SupplementSearch onSupplementSelect={mockOnSelect} />
        </TestWrapper>
      );

      const supplementCard = screen.getByText('Vitamin D3').closest('div');
      await user.click(supplementCard!);

      expect(mockOnSelect).toHaveBeenCalledWith('1');
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      render(
        <TestWrapper>
          <SupplementSearch />
        </TestWrapper>
      );

      const searchInput = screen.getByPlaceholderText(/search supplements/i);
      const categorySelect = screen.getByDisplayValue(/all categories/i);

      expect(searchInput).toHaveAttribute('type', 'text');
      expect(categorySelect).toHaveRole('combobox');
    });

    it('should be keyboard navigable', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <SupplementSearch />
        </TestWrapper>
      );

      const searchInput = screen.getByPlaceholderText(/search supplements/i);
      
      // Tab to search input
      await user.tab();
      expect(searchInput).toHaveFocus();

      // Tab to category select
      await user.tab();
      expect(screen.getByDisplayValue(/all categories/i)).toHaveFocus();
    });
  });
});
