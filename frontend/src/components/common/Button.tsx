/**
 * Button Component
 * 
 * Reusable button component with multiple variants, sizes, and states.
 */

import React from 'react';
import styled, { css } from 'styled-components';

// Button variant types
type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
type ButtonSize = 'small' | 'medium' | 'large';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  fullWidth?: boolean;
  loading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  children: React.ReactNode;
}

// Styled button component
const StyledButton = styled.button<{
  $variant: ButtonVariant;
  $size: ButtonSize;
  $fullWidth: boolean;
  $loading: boolean;
}>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border: none;
  border-radius: ${props => props.theme.borderRadius.medium};
  font-family: inherit;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  
  ${props => props.$fullWidth && css`
    width: 100%;
  `}
  
  ${props => props.$loading && css`
    pointer-events: none;
  `}
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
  }
  
  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px ${props => props.theme.colors.primary}40;
  }
  
  /* Size variants */
  ${props => {
    switch (props.$size) {
      case 'small':
        return css`
          padding: 0.5rem 1rem;
          font-size: 0.875rem;
          min-height: 2rem;
        `;
      case 'large':
        return css`
          padding: 0.875rem 1.5rem;
          font-size: 1rem;
          min-height: 3rem;
        `;
      default: // medium
        return css`
          padding: 0.75rem 1.25rem;
          font-size: 0.9375rem;
          min-height: 2.5rem;
        `;
    }
  }}
  
  /* Color variants */
  ${props => {
    switch (props.$variant) {
      case 'primary':
        return css`
          background: ${props.theme.colors.primary};
          color: white;
          
          &:hover:not(:disabled) {
            background: ${props.theme.colors.primaryDark};
            transform: translateY(-1px);
            box-shadow: 0 4px 12px ${props.theme.colors.primary}40;
          }
          
          &:active {
            transform: translateY(0);
          }
        `;
        
      case 'secondary':
        return css`
          background: ${props.theme.colors.secondary};
          color: white;
          
          &:hover:not(:disabled) {
            background: ${props.theme.colors.secondaryDark};
            transform: translateY(-1px);
            box-shadow: 0 4px 12px ${props.theme.colors.secondary}40;
          }
          
          &:active {
            transform: translateY(0);
          }
        `;
        
      case 'outline':
        return css`
          background: transparent;
          color: ${props.theme.colors.primary};
          border: 1px solid ${props.theme.colors.primary};
          
          &:hover:not(:disabled) {
            background: ${props.theme.colors.primary};
            color: white;
            transform: translateY(-1px);
          }
          
          &:active {
            transform: translateY(0);
          }
        `;
        
      case 'ghost':
        return css`
          background: transparent;
          color: ${props.theme.colors.text};
          
          &:hover:not(:disabled) {
            background: ${props.theme.colors.background};
            color: ${props.theme.colors.primary};
          }
        `;
        
      case 'danger':
        return css`
          background: ${props.theme.colors.error};
          color: white;
          
          &:hover:not(:disabled) {
            background: ${props.theme.colors.errorDark};
            transform: translateY(-1px);
            box-shadow: 0 4px 12px ${props.theme.colors.error}40;
          }
          
          &:active {
            transform: translateY(0);
          }
        `;
        
      default:
        return css`
          background: ${props.theme.colors.background};
          color: ${props.theme.colors.text};
          border: 1px solid ${props.theme.colors.border};
          
          &:hover:not(:disabled) {
            background: ${props.theme.colors.backgroundSecondary};
            border-color: ${props.theme.colors.primary};
          }
        `;
    }
  }}
`;

const IconWrapper = styled.span<{ $position: 'left' | 'right' }>`
  display: flex;
  align-items: center;
  
  ${props => props.$position === 'left' && css`
    margin-right: -0.25rem;
  `}
  
  ${props => props.$position === 'right' && css`
    margin-left: -0.25rem;
  `}
`;

const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'medium',
  fullWidth = false,
  loading = false,
  leftIcon,
  rightIcon,
  children,
  disabled,
  ...props
}) => {
  return (
    <StyledButton
      $variant={variant}
      $size={size}
      $fullWidth={fullWidth}
      $loading={loading}
      disabled={disabled || loading}
      {...props}
    >
      {leftIcon && (
        <IconWrapper $position="left">
          {leftIcon}
        </IconWrapper>
      )}
      
      {children}
      
      {rightIcon && (
        <IconWrapper $position="right">
          {rightIcon}
        </IconWrapper>
      )}
    </StyledButton>
  );
};

export default Button;
