/**
 * Main Dashboard Component
 * 
 * Central hub for supplement tracking and research activities
 */

import React, { useState, useEffect } from 'react';
import { Button } from './ui/button';
import { Card } from './ui/card';
import { Badge } from './ui/badge';
import { 
  Activity, 
  Plus, 
  Calendar, 
  TrendingUp, 
  Users, 
  Search,
  Bell,
  Settings,
  BarChart3,
  Pill,
  FlaskConical
} from 'lucide-react';

interface DashboardProps {
  user?: {
    name: string;
    email: string;
    joinDate: string;
  };
}

const Dashboard: React.FC<DashboardProps> = ({ user }) => {
  const [todayIntakes, setTodayIntakes] = useState(3);
  const [activeStudies, setActiveStudies] = useState(2);
  const [weeklyProgress, setWeeklyProgress] = useState(85);

  // Mock data for demonstration
  const recentSupplements = [
    { id: 1, name: 'Vitamin D3', dosage: '2000 IU', time: '8:00 AM', taken: true },
    { id: 2, name: 'Omega-3', dosage: '1000mg', time: '8:00 AM', taken: true },
    { id: 3, name: 'Magnesium', dosage: '400mg', time: '9:00 PM', taken: false },
  ];

  const activeResearch = [
    { id: 1, title: 'Vitamin D and Sleep Quality', progress: 60, participants: 156 },
    { id: 2, title: 'Omega-3 Cognitive Benefits', progress: 30, participants: 89 },
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Activity className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                Supplement Tracker
              </h1>
            </div>
            
            <div className="flex items-center space-x-4">
              <Button variant="outline" size="sm">
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
              <Button variant="outline" size="sm">
                <Bell className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            Welcome back, {user?.name || 'User'}!
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            Track your supplements and participate in evidence-based research
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <Pill className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Today's Intakes</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{todayIntakes}</p>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                <FlaskConical className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active Studies</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{activeStudies}</p>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                <TrendingUp className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Weekly Progress</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{weeklyProgress}%</p>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 dark:bg-orange-900 rounded-lg">
                <Users className="h-6 w-6 text-orange-600 dark:text-orange-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Community Rank</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">#42</p>
              </div>
            </div>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Today's Supplements */}
          <Card className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Today's Supplements
              </h3>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Intake
              </Button>
            </div>

            <div className="space-y-4">
              {recentSupplements.map((supplement) => (
                <div key={supplement.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex items-center">
                    <div className={`w-3 h-3 rounded-full mr-3 ${supplement.taken ? 'bg-green-500' : 'bg-gray-300'}`} />
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">{supplement.name}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{supplement.dosage} at {supplement.time}</p>
                    </div>
                  </div>
                  <Badge variant={supplement.taken ? "default" : "outline"}>
                    {supplement.taken ? "Taken" : "Pending"}
                  </Badge>
                </div>
              ))}
            </div>

            <div className="mt-6">
              <Button variant="outline" className="w-full">
                <Calendar className="h-4 w-4 mr-2" />
                View Full Schedule
              </Button>
            </div>
          </Card>

          {/* Active Research */}
          <Card className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Active Research
              </h3>
              <Button size="sm" variant="outline">
                <Search className="h-4 w-4 mr-2" />
                Browse Studies
              </Button>
            </div>

            <div className="space-y-4">
              {activeResearch.map((study) => (
                <div key={study.id} className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-gray-900 dark:text-white">{study.title}</h4>
                    <Badge variant="outline">{study.participants} participants</Badge>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2 mb-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ width: `${study.progress}%` }}
                    />
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{study.progress}% complete</p>
                </div>
              ))}
            </div>

            <div className="mt-6">
              <Button variant="outline" className="w-full">
                <BarChart3 className="h-4 w-4 mr-2" />
                View Research Dashboard
              </Button>
            </div>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="mt-8">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick Actions</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button className="h-20 flex-col">
              <Plus className="h-6 w-6 mb-2" />
              Log Supplement
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <Search className="h-6 w-6 mb-2" />
              Find Studies
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <BarChart3 className="h-6 w-6 mb-2" />
              View Analytics
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <Users className="h-6 w-6 mb-2" />
              Community
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
