/**
 * Community Feed Component
 * 
 * Main feed displaying community posts with filtering and infinite scroll.
 */

import React, { useEffect, useState, useCallback } from 'react';
import { useAppDispatch, useAppSelector } from '@/store';
import { 
  fetchCommunityPosts, 
  updateFeedFilters,
  selectCommunityPosts, 
  selectCommunityLoading, 
  selectFeedFilters,
  selectFeedPagination 
} from '@/store/slices/communitySlice';
import { selectUser } from '@/store/slices/authSlice';
import { openModal } from '@/store/slices/uiSlice';
import styled from 'styled-components';

// Components
import CommunityPostCard from './CommunityPostCard';
import CreatePostModal from './CreatePostModal';
import Button from '@/components/common/Button';
import Input from '@/components/common/Input';
import LoadingSpinner from '@/components/common/LoadingSpinner';

// Icons
const PlusIcon = () => <span>+</span>;
const SearchIcon = () => <span>🔍</span>;
const FilterIcon = () => <span>🔽</span>;
const ClearIcon = () => <span>✕</span>;

// Styled components
const FeedContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: 0 1rem;
`;

const FeedHeader = styled.div`
  margin-bottom: 2rem;
  
  h1 {
    color: ${props => props.theme.colors.text};
    margin: 0 0 0.5rem 0;
    font-size: 2rem;
  }
  
  p {
    color: ${props => props.theme.colors.textSecondary};
    font-size: 1.1rem;
    margin: 0;
  }
`;

const FeedControls = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  align-items: center;
  
  @media (max-width: ${props => props.theme.breakpoints.tablet}) {
    flex-direction: column;
    align-items: stretch;
  }
`;

const SearchSection = styled.div`
  display: flex;
  gap: 1rem;
  flex: 1;
  
  @media (max-width: ${props => props.theme.breakpoints.mobile}) {
    flex-direction: column;
  }
`;

const SearchInput = styled(Input)`
  flex: 1;
  max-width: 400px;
`;

const FilterSelect = styled.select`
  padding: 0.75rem 1rem;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.medium};
  background: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
  font-size: 0.9375rem;
  min-width: 150px;
  
  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 2px ${props => props.theme.colors.primary}20;
  }
`;

const CreatePostButton = styled(Button)`
  white-space: nowrap;
  
  @media (max-width: ${props => props.theme.breakpoints.tablet}) {
    width: 100%;
  }
`;

const FeedStats = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background: ${props => props.theme.colors.backgroundSecondary};
  border-radius: ${props => props.theme.borderRadius.large};
`;

const StatItem = styled.div`
  text-align: center;
  
  .stat-value {
    font-size: 1.5rem;
    font-weight: ${props => props.theme.typography.fontWeight.bold};
    color: ${props => props.theme.colors.primary};
    margin-bottom: 0.25rem;
  }
  
  .stat-label {
    font-size: 0.8rem;
    color: ${props => props.theme.colors.textSecondary};
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
`;

const PostsList = styled.div`
  display: flex;
  flex-direction: column;
`;

const LoadMoreContainer = styled.div`
  display: flex;
  justify-content: center;
  margin: 2rem 0;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 4rem 2rem;
  
  h3 {
    color: ${props => props.theme.colors.text};
    margin-bottom: 1rem;
  }
  
  p {
    color: ${props => props.theme.colors.textSecondary};
    margin-bottom: 2rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
`;

// Filter options
const POST_TYPE_OPTIONS = [
  { value: '', label: 'All Posts' },
  { value: 'supplement_review', label: 'Supplement Reviews' },
  { value: 'research_finding', label: 'Research Findings' },
  { value: 'experience_share', label: 'Experiences' },
  { value: 'general', label: 'General Discussion' },
];

const SORT_OPTIONS = [
  { value: 'recent', label: 'Most Recent' },
  { value: 'popular', label: 'Most Popular' },
  { value: 'trending', label: 'Trending' },
];

const CommunityFeed: React.FC = () => {
  const dispatch = useAppDispatch();
  const posts = useAppSelector(selectCommunityPosts);
  const isLoading = useAppSelector(selectCommunityLoading);
  const filters = useAppSelector(selectFeedFilters);
  const pagination = useAppSelector(selectFeedPagination);
  const currentUser = useAppSelector(selectUser);
  
  const [localSearch, setLocalSearch] = useState(filters.search);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  useEffect(() => {
    // Load initial posts
    dispatch(fetchCommunityPosts({
      post_type: filters.post_type,
      search: filters.search,
      sort: filters.sort,
      skip: 0,
      limit: 10,
    }));
  }, [filters, dispatch]);

  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setLocalSearch(value);
    
    // Debounce search
    const timeoutId = setTimeout(() => {
      dispatch(updateFeedFilters({ search: value }));
    }, 300);
    
    return () => clearTimeout(timeoutId);
  }, [dispatch]);

  const handlePostTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    dispatch(updateFeedFilters({ post_type: e.target.value }));
  };

  const handleSortChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    dispatch(updateFeedFilters({ sort: e.target.value }));
  };

  const handleClearFilters = () => {
    setLocalSearch('');
    dispatch(updateFeedFilters({ search: '', post_type: '', sort: 'recent' }));
  };

  const handleCreatePost = () => {
    if (!currentUser) {
      // Redirect to login or show message
      return;
    }
    
    dispatch(openModal({
      component: 'create-post',
      props: {},
      size: 'large',
    }));
  };

  const handleLoadMore = async () => {
    if (isLoadingMore || !pagination.hasMore) return;

    setIsLoadingMore(true);
    
    try {
      await dispatch(fetchCommunityPosts({
        post_type: filters.post_type,
        search: filters.search,
        sort: filters.sort,
        skip: posts.length,
        limit: 10,
        append: true,
      })).unwrap();
    } catch (error) {
      console.error('Failed to load more posts:', error);
    } finally {
      setIsLoadingMore(false);
    }
  };

  const handleCommentClick = (postId: string) => {
    // Navigate to post detail with comments focused
    // This would be implemented with navigation
    console.log('Comment on post:', postId);
  };

  const hasActiveFilters = filters.search || filters.post_type || filters.sort !== 'recent';
  
  // Calculate stats (these would come from API in real implementation)
  const stats = {
    totalPosts: pagination.total,
    todayPosts: posts.filter(p => {
      const today = new Date();
      const postDate = new Date(p.created_at);
      return postDate.toDateString() === today.toDateString();
    }).length,
    activeUsers: 156, // This would come from API
    totalLikes: posts.reduce((sum, post) => sum + post.like_count, 0),
  };

  return (
    <FeedContainer>
      <FeedHeader>
        <h1>Community Feed</h1>
        <p>
          Connect with fellow supplement enthusiasts, share experiences, and discover insights
        </p>
      </FeedHeader>

      <FeedControls>
        <SearchSection>
          <SearchInput
            type="text"
            placeholder="Search posts..."
            value={localSearch}
            onChange={handleSearchChange}
            leftIcon={<SearchIcon />}
            rightIcon={
              localSearch ? (
                <button 
                  onClick={() => {
                    setLocalSearch('');
                    dispatch(updateFeedFilters({ search: '' }));
                  }}
                  style={{ background: 'none', border: 'none', cursor: 'pointer' }}
                >
                  <ClearIcon />
                </button>
              ) : undefined
            }
          />
          
          <FilterSelect
            value={filters.post_type}
            onChange={handlePostTypeChange}
          >
            {POST_TYPE_OPTIONS.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </FilterSelect>
          
          <FilterSelect
            value={filters.sort}
            onChange={handleSortChange}
          >
            {SORT_OPTIONS.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </FilterSelect>
          
          {hasActiveFilters && (
            <Button
              variant="ghost"
              size="small"
              onClick={handleClearFilters}
              leftIcon={<ClearIcon />}
            >
              Clear
            </Button>
          )}
        </SearchSection>

        <CreatePostButton
          variant="primary"
          onClick={handleCreatePost}
          leftIcon={<PlusIcon />}
        >
          Create Post
        </CreatePostButton>
      </FeedControls>

      <FeedStats>
        <StatItem>
          <div className="stat-value">{stats.totalPosts}</div>
          <div className="stat-label">Total Posts</div>
        </StatItem>
        <StatItem>
          <div className="stat-value">{stats.todayPosts}</div>
          <div className="stat-label">Today</div>
        </StatItem>
        <StatItem>
          <div className="stat-value">{stats.activeUsers}</div>
          <div className="stat-label">Active Users</div>
        </StatItem>
        <StatItem>
          <div className="stat-value">{stats.totalLikes}</div>
          <div className="stat-label">Total Likes</div>
        </StatItem>
      </FeedStats>

      {isLoading && posts.length === 0 ? (
        <LoadingContainer>
          <LoadingSpinner size="large" />
        </LoadingContainer>
      ) : posts.length === 0 ? (
        <EmptyState>
          <h3>
            {hasActiveFilters ? 'No posts found' : 'No posts yet'}
          </h3>
          <p>
            {hasActiveFilters 
              ? 'Try adjusting your search terms or filters to find relevant posts.'
              : 'Be the first to share your supplement experience with the community!'
            }
          </p>
          {!hasActiveFilters && (
            <CreatePostButton
              variant="primary"
              onClick={handleCreatePost}
              leftIcon={<PlusIcon />}
            >
              Create First Post
            </CreatePostButton>
          )}
          {hasActiveFilters && (
            <Button
              variant="outline"
              onClick={handleClearFilters}
            >
              Clear Filters
            </Button>
          )}
        </EmptyState>
      ) : (
        <>
          <PostsList>
            {posts.map(post => (
              <CommunityPostCard
                key={post.id}
                post={post}
                onCommentClick={handleCommentClick}
              />
            ))}
          </PostsList>

          {pagination.hasMore && (
            <LoadMoreContainer>
              <Button
                variant="outline"
                onClick={handleLoadMore}
                disabled={isLoadingMore}
                leftIcon={isLoadingMore ? <LoadingSpinner size="small" /> : undefined}
              >
                {isLoadingMore ? 'Loading...' : 'Load More Posts'}
              </Button>
            </LoadMoreContainer>
          )}
        </>
      )}

      <CreatePostModal isOpen={false} />
    </FeedContainer>
  );
};

export default CommunityFeed;
