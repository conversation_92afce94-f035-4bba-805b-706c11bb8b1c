/**
 * Health Metrics Card Component
 * 
 * Displays individual health metrics with trends and correlation indicators.
 */

import React, { useState } from 'react';
import { useAppDispatch } from '@/store';
import { updateHealthMetric, deleteHealthMetric } from '@/store/slices/healthSlice';
import { showSuccessNotification, showErrorNotification } from '@/store/slices/uiSlice';
import { HealthMetric } from '@/types/api';
import styled from 'styled-components';
import { format, parseISO } from 'date-fns';

// Components
import Card from '@/components/common/Card';
import Button from '@/components/common/Button';
import Modal from '@/components/common/Modal';
import Input from '@/components/common/Input';

// Icons
const TrendUpIcon = () => <span style={{ color: '#10b981' }}>📈</span>;
const TrendDownIcon = () => <span style={{ color: '#ef4444' }}>📉</span>;
const TrendStableIcon = () => <span style={{ color: '#6b7280' }}>➡️</span>;
const EditIcon = () => <span>✏️</span>;
const DeleteIcon = () => <span>🗑️</span>;
const SyncIcon = () => <span>🔄</span>;
const AlertIcon = () => <span>⚠️</span>;
const CheckIcon = () => <span>✅</span>;

// Styled components
const MetricCardContainer = styled(Card)`
  padding: 1.5rem;
  position: relative;
  transition: all 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px ${props => props.theme.colors.shadow}15;
  }
`;

const MetricHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
`;

const MetricInfo = styled.div`
  flex: 1;
`;

const MetricName = styled.h3`
  color: ${props => props.theme.colors.text};
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  font-weight: ${props => props.theme.typography.fontWeight.semibold};
`;

const MetricCategory = styled.span`
  display: inline-block;
  padding: 0.25rem 0.75rem;
  background: ${props => props.theme.colors.primary}15;
  color: ${props => props.theme.colors.primary};
  border-radius: ${props => props.theme.borderRadius.full};
  font-size: 0.75rem;
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const MetricActions = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const ActionButton = styled(Button)`
  padding: 0.5rem;
  min-width: auto;
`;

const MetricValue = styled.div`
  display: flex;
  align-items: baseline;
  gap: 0.5rem;
  margin-bottom: 1rem;
`;

const CurrentValue = styled.span`
  font-size: 2rem;
  font-weight: ${props => props.theme.typography.fontWeight.bold};
  color: ${props => props.theme.colors.text};
`;

const MetricUnit = styled.span`
  font-size: 1rem;
  color: ${props => props.theme.colors.textSecondary};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
`;

const MetricTrend = styled.div<{ $trend: 'up' | 'down' | 'stable' }>`
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  color: ${props => {
    switch (props.$trend) {
      case 'up': return props.theme.colors.success;
      case 'down': return props.theme.colors.error;
      default: return props.theme.colors.textSecondary;
    }
  }};
`;

const MetricDetails = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
  
  @media (max-width: ${props => props.theme.breakpoints.mobile}) {
    grid-template-columns: 1fr;
  }
`;

const DetailItem = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
`;

const DetailLabel = styled.span`
  font-size: 0.8rem;
  color: ${props => props.theme.colors.textSecondary};
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const DetailValue = styled.span`
  font-size: 0.9rem;
  color: ${props => props.theme.colors.text};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
`;

const MetricStatus = styled.div<{ $status: 'optimal' | 'warning' | 'critical' | 'unknown' }>`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: ${props => props.theme.borderRadius.medium};
  font-size: 0.875rem;
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  
  ${props => {
    switch (props.$status) {
      case 'optimal':
        return `
          background: ${props.theme.colors.success}15;
          color: ${props.theme.colors.success};
        `;
      case 'warning':
        return `
          background: ${props.theme.colors.warning}15;
          color: ${props.theme.colors.warning};
        `;
      case 'critical':
        return `
          background: ${props.theme.colors.error}15;
          color: ${props.theme.colors.error};
        `;
      default:
        return `
          background: ${props.theme.colors.backgroundSecondary};
          color: ${props.theme.colors.textSecondary};
        `;
    }
  }}
`;

const SyncStatus = styled.div<{ $synced: boolean }>`
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.8rem;
  color: ${props => props.$synced ? props.theme.colors.success : props.theme.colors.textSecondary};
  margin-top: 0.5rem;
`;

const CorrelationIndicator = styled.div<{ $strength: 'strong' | 'moderate' | 'weak' | 'none' }>`
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  
  ${props => {
    switch (props.$strength) {
      case 'strong':
        return `background: ${props.theme.colors.success};`;
      case 'moderate':
        return `background: ${props.theme.colors.warning};`;
      case 'weak':
        return `background: ${props.theme.colors.error};`;
      default:
        return `background: ${props.theme.colors.border};`;
    }
  }}
`;

interface HealthMetricsCardProps {
  metric: HealthMetric;
  onEdit?: (metric: HealthMetric) => void;
  onDelete?: (metricId: string) => void;
  showCorrelation?: boolean;
}

const HealthMetricsCard: React.FC<HealthMetricsCardProps> = ({
  metric,
  onEdit,
  onDelete,
  showCorrelation = true,
}) => {
  const dispatch = useAppDispatch();
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editValue, setEditValue] = useState(metric.current_value?.toString() || '');
  const [isUpdating, setIsUpdating] = useState(false);

  const getTrendIcon = (trend: number) => {
    if (trend > 0.05) return <TrendUpIcon />;
    if (trend < -0.05) return <TrendDownIcon />;
    return <TrendStableIcon />;
  };

  const getTrendDirection = (trend: number): 'up' | 'down' | 'stable' => {
    if (trend > 0.05) return 'up';
    if (trend < -0.05) return 'down';
    return 'stable';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'optimal': return <CheckIcon />;
      case 'warning': return <AlertIcon />;
      case 'critical': return <AlertIcon />;
      default: return null;
    }
  };

  const getCorrelationStrength = (correlation?: number): 'strong' | 'moderate' | 'weak' | 'none' => {
    if (!correlation) return 'none';
    const abs = Math.abs(correlation);
    if (abs >= 0.7) return 'strong';
    if (abs >= 0.4) return 'moderate';
    if (abs >= 0.2) return 'weak';
    return 'none';
  };

  const handleEdit = () => {
    setIsEditModalOpen(true);
    setEditValue(metric.current_value?.toString() || '');
  };

  const handleSaveEdit = async () => {
    if (!editValue.trim()) return;

    setIsUpdating(true);
    try {
      await dispatch(updateHealthMetric({
        id: metric.id,
        current_value: parseFloat(editValue),
        recorded_at: new Date().toISOString(),
      })).unwrap();

      dispatch(showSuccessNotification(
        'Health metric updated successfully',
        'Health Tracking'
      ));
      
      setIsEditModalOpen(false);
      onEdit?.(metric);
    } catch (error: any) {
      dispatch(showErrorNotification(
        error.message || 'Failed to update health metric',
        'Update Error'
      ));
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDelete = async () => {
    if (!window.confirm('Are you sure you want to delete this health metric?')) {
      return;
    }

    try {
      await dispatch(deleteHealthMetric(metric.id)).unwrap();
      dispatch(showSuccessNotification(
        'Health metric deleted successfully',
        'Health Tracking'
      ));
      onDelete?.(metric.id);
    } catch (error: any) {
      dispatch(showErrorNotification(
        error.message || 'Failed to delete health metric',
        'Delete Error'
      ));
    }
  };

  const formatValue = (value: number | null | undefined) => {
    if (value === null || value === undefined) return 'N/A';
    return value.toLocaleString(undefined, { 
      minimumFractionDigits: 0, 
      maximumFractionDigits: 2 
    });
  };

  const formatDate = (dateString: string) => {
    return format(parseISO(dateString), 'MMM d, yyyy');
  };

  return (
    <>
      <MetricCardContainer>
        {showCorrelation && (
          <CorrelationIndicator 
            $strength={getCorrelationStrength(metric.supplement_correlation)}
            title={`Supplement correlation: ${metric.supplement_correlation ? 
              (metric.supplement_correlation * 100).toFixed(1) + '%' : 'None'}`}
          />
        )}

        <MetricHeader>
          <MetricInfo>
            <MetricName>{metric.name}</MetricName>
            <MetricCategory>{metric.category}</MetricCategory>
          </MetricInfo>
          <MetricActions>
            <ActionButton
              variant="ghost"
              size="small"
              leftIcon={<EditIcon />}
              onClick={handleEdit}
              aria-label="Edit metric"
            />
            <ActionButton
              variant="ghost"
              size="small"
              leftIcon={<DeleteIcon />}
              onClick={handleDelete}
              aria-label="Delete metric"
            />
          </MetricActions>
        </MetricHeader>

        <MetricValue>
          <CurrentValue>{formatValue(metric.current_value)}</CurrentValue>
          <MetricUnit>{metric.unit}</MetricUnit>
          {metric.trend !== undefined && (
            <MetricTrend $trend={getTrendDirection(metric.trend)}>
              {getTrendIcon(metric.trend)}
              {Math.abs(metric.trend * 100).toFixed(1)}%
            </MetricTrend>
          )}
        </MetricValue>

        <MetricDetails>
          <DetailItem>
            <DetailLabel>Target Range</DetailLabel>
            <DetailValue>
              {metric.target_min && metric.target_max 
                ? `${formatValue(metric.target_min)} - ${formatValue(metric.target_max)}`
                : 'Not set'
              }
            </DetailValue>
          </DetailItem>
          <DetailItem>
            <DetailLabel>Last Updated</DetailLabel>
            <DetailValue>
              {metric.recorded_at ? formatDate(metric.recorded_at) : 'Never'}
            </DetailValue>
          </DetailItem>
        </MetricDetails>

        {metric.status && (
          <MetricStatus $status={metric.status as any}>
            {getStatusIcon(metric.status)}
            {metric.status.charAt(0).toUpperCase() + metric.status.slice(1)}
          </MetricStatus>
        )}

        {metric.data_source && (
          <SyncStatus $synced={metric.is_synced || false}>
            <SyncIcon />
            {metric.is_synced ? 'Synced' : 'Manual'} from {metric.data_source}
          </SyncStatus>
        )}
      </MetricCardContainer>

      <Modal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        title={`Edit ${metric.name}`}
      >
        <div style={{ padding: '1rem', display: 'flex', flexDirection: 'column', gap: '1rem' }}>
          <Input
            label={`${metric.name} (${metric.unit})`}
            type="number"
            value={editValue}
            onChange={(e) => setEditValue(e.target.value)}
            placeholder={`Enter ${metric.name.toLowerCase()}`}
            step="0.01"
          />
          <div style={{ display: 'flex', gap: '0.75rem', justifyContent: 'flex-end' }}>
            <Button
              variant="outline"
              onClick={() => setIsEditModalOpen(false)}
              disabled={isUpdating}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleSaveEdit}
              disabled={isUpdating || !editValue.trim()}
            >
              {isUpdating ? 'Saving...' : 'Save'}
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default HealthMetricsCard;
