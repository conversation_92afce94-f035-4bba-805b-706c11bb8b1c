/**
 * Research Protocol Card Component
 * 
 * Displays research protocol information with participation actions.
 */

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '@/store';
import { selectUser } from '@/store/slices/authSlice';
import { showSuccessNotification, showErrorNotification } from '@/store/slices/uiSlice';
import { ResearchProtocol } from '@/types/api';
import styled from 'styled-components';
import { format, parseISO, differenceInWeeks } from 'date-fns';

// Components
import Card from '@/components/common/Card';
import Button from '@/components/common/Button';
import LoadingSpinner from '@/components/common/LoadingSpinner';

// Icons (placeholder)
const UsersIcon = () => <span>👥</span>;
const CalendarIcon = () => <span>📅</span>;
const ClockIcon = () => <span>⏰</span>;
const CheckIcon = () => <span>✓</span>;
const InfoIcon = () => <span>ℹ️</span>;
const JoinIcon = () => <span>🚀</span>;

// Styled components
const ProtocolCardContainer = styled(Card)`
  position: relative;
  transition: all 0.2s ease;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px ${props => props.theme.colors.shadow}20;
  }
`;

const ProtocolHeader = styled.div`
  margin-bottom: 1rem;
`;

const ProtocolTitle = styled.h3`
  font-size: 1.2rem;
  font-weight: ${props => props.theme.typography.fontWeight.semibold};
  color: ${props => props.theme.colors.text};
  margin: 0 0 0.5rem 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;

const ProtocolMeta = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
`;

const MetaItem = styled.span`
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.9rem;
  color: ${props => props.theme.colors.textSecondary};
`;

const StatusBadge = styled.span<{ $status: string }>`
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: ${props => props.theme.borderRadius.full};
  font-size: 0.8rem;
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  text-transform: uppercase;
  letter-spacing: 0.5px;
  
  ${props => {
    switch (props.$status) {
      case 'recruiting':
        return `
          background: ${props.theme.colors.success}15;
          color: ${props.theme.colors.success};
        `;
      case 'active':
        return `
          background: ${props.theme.colors.primary}15;
          color: ${props.theme.colors.primary};
        `;
      case 'completed':
        return `
          background: ${props.theme.colors.textSecondary}15;
          color: ${props.theme.colors.textSecondary};
        `;
      case 'draft':
        return `
          background: ${props.theme.colors.warning}15;
          color: ${props.theme.colors.warning};
        `;
      default:
        return `
          background: ${props.theme.colors.backgroundSecondary};
          color: ${props.theme.colors.textSecondary};
        `;
    }
  }}
`;

const ProtocolDescription = styled.p`
  font-size: 0.9rem;
  color: ${props => props.theme.colors.textSecondary};
  line-height: 1.5;
  margin: 0.75rem 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;

const ProtocolStats = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin: 1rem 0;
  padding: 1rem;
  background: ${props => props.theme.colors.backgroundSecondary};
  border-radius: ${props => props.theme.borderRadius.medium};
`;

const StatItem = styled.div`
  text-align: center;
  
  .stat-value {
    font-size: 1.2rem;
    font-weight: ${props => props.theme.typography.fontWeight.bold};
    color: ${props => props.theme.colors.primary};
    margin-bottom: 0.25rem;
  }
  
  .stat-label {
    font-size: 0.8rem;
    color: ${props => props.theme.colors.textSecondary};
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
`;

const ProtocolObjectives = styled.div`
  margin: 1rem 0;
  
  h4 {
    font-size: 0.9rem;
    font-weight: ${props => props.theme.typography.fontWeight.semibold};
    color: ${props => props.theme.colors.text};
    margin: 0 0 0.5rem 0;
  }
  
  ul {
    margin: 0;
    padding-left: 1.5rem;
    
    li {
      font-size: 0.9rem;
      color: ${props => props.theme.colors.textSecondary};
      margin-bottom: 0.25rem;
      line-height: 1.4;
    }
  }
`;

const Actions = styled.div`
  display: flex;
  gap: 0.75rem;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid ${props => props.theme.colors.border};
  
  @media (max-width: ${props => props.theme.breakpoints.mobile}) {
    flex-direction: column;
  }
`;

const JoinButton = styled(Button)`
  flex: 1;
`;

const DetailsButton = styled(Button)`
  flex: 1;
`;

const CreatorInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  font-size: 0.9rem;
  color: ${props => props.theme.colors.textSecondary};
  
  .creator-name {
    font-weight: ${props => props.theme.typography.fontWeight.medium};
    color: ${props => props.theme.colors.text};
  }
`;

interface ResearchProtocolCardProps {
  protocol: ResearchProtocol;
  onJoin?: (protocolId: string) => void;
  showJoinButton?: boolean;
  compact?: boolean;
}

const ResearchProtocolCard: React.FC<ResearchProtocolCardProps> = ({
  protocol,
  onJoin,
  showJoinButton = true,
  compact = false,
}) => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const currentUser = useAppSelector(selectUser);
  const [isJoining, setIsJoining] = useState(false);

  const handleCardClick = (e: React.MouseEvent) => {
    // Don't trigger if clicking on buttons
    if ((e.target as HTMLElement).closest('button')) {
      return;
    }
    navigate(`/research/protocols/${protocol.id}`);
  };

  const handleJoinStudy = async (e: React.MouseEvent) => {
    e.stopPropagation();
    
    if (!currentUser) {
      dispatch(showErrorNotification('Please log in to join studies'));
      return;
    }

    setIsJoining(true);
    
    try {
      if (onJoin) {
        await onJoin(protocol.id);
      }
      
      dispatch(showSuccessNotification(
        `Successfully joined "${protocol.title}"`,
        'Study Enrollment'
      ));
    } catch (error: any) {
      dispatch(showErrorNotification(
        error.message || 'Failed to join study',
        'Enrollment Error'
      ));
    } finally {
      setIsJoining(false);
    }
  };

  const handleViewDetails = (e: React.MouseEvent) => {
    e.stopPropagation();
    navigate(`/research/protocols/${protocol.id}`);
  };

  const formatDuration = (weeks: number) => {
    if (weeks < 4) return `${weeks} weeks`;
    if (weeks < 52) return `${Math.round(weeks / 4)} months`;
    return `${Math.round(weeks / 52)} years`;
  };

  const getParticipationRate = () => {
    if (!protocol.max_participants) return null;
    return Math.round((protocol.participant_count / protocol.max_participants) * 100);
  };

  const canJoinStudy = () => {
    if (protocol.status !== 'recruiting') return false;
    if (protocol.max_participants && protocol.participant_count >= protocol.max_participants) return false;
    return true;
  };

  const participationRate = getParticipationRate();

  return (
    <ProtocolCardContainer
      onClick={handleCardClick}
      padding={compact ? 'small' : 'medium'}
      hover
    >
      <ProtocolHeader>
        <ProtocolMeta>
          <StatusBadge $status={protocol.status}>
            {protocol.status}
          </StatusBadge>
          <MetaItem>
            <CalendarIcon />
            {formatDuration(protocol.duration_weeks)}
          </MetaItem>
          <MetaItem>
            <UsersIcon />
            {protocol.participant_count}
            {protocol.max_participants && ` / ${protocol.max_participants}`}
          </MetaItem>
        </ProtocolMeta>
        
        <ProtocolTitle>{protocol.title}</ProtocolTitle>
        
        <CreatorInfo>
          Led by <span className="creator-name">{protocol.created_by_user.full_name}</span>
        </CreatorInfo>
      </ProtocolHeader>

      {!compact && (
        <ProtocolDescription>
          {protocol.description}
        </ProtocolDescription>
      )}

      {!compact && (
        <ProtocolStats>
          <StatItem>
            <div className="stat-value">{protocol.duration_weeks}</div>
            <div className="stat-label">Weeks</div>
          </StatItem>
          <StatItem>
            <div className="stat-value">{protocol.participant_count}</div>
            <div className="stat-label">Participants</div>
          </StatItem>
          {participationRate && (
            <StatItem>
              <div className="stat-value">{participationRate}%</div>
              <div className="stat-label">Full</div>
            </StatItem>
          )}
          <StatItem>
            <div className="stat-value">{protocol.objectives.length}</div>
            <div className="stat-label">Objectives</div>
          </StatItem>
        </ProtocolStats>
      )}

      {!compact && protocol.objectives.length > 0 && (
        <ProtocolObjectives>
          <h4>Key Objectives</h4>
          <ul>
            {protocol.objectives.slice(0, 3).map((objective, index) => (
              <li key={index}>{objective}</li>
            ))}
            {protocol.objectives.length > 3 && (
              <li>+ {protocol.objectives.length - 3} more objectives</li>
            )}
          </ul>
        </ProtocolObjectives>
      )}

      <Actions>
        {showJoinButton && canJoinStudy() && (
          <JoinButton
            variant="primary"
            size="small"
            onClick={handleJoinStudy}
            disabled={isJoining}
            leftIcon={isJoining ? <LoadingSpinner size="small" /> : <JoinIcon />}
          >
            {isJoining ? 'Joining...' : 'Join Study'}
          </JoinButton>
        )}
        
        {showJoinButton && !canJoinStudy() && protocol.status === 'recruiting' && (
          <JoinButton
            variant="outline"
            size="small"
            disabled
            leftIcon={<CheckIcon />}
          >
            Study Full
          </JoinButton>
        )}
        
        <DetailsButton
          variant="outline"
          size="small"
          onClick={handleViewDetails}
          leftIcon={<InfoIcon />}
        >
          View Details
        </DetailsButton>
      </Actions>
    </ProtocolCardContainer>
  );
};

export default ResearchProtocolCard;
