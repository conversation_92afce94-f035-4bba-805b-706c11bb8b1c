/**
 * Modern UI Components Tests
 * 
 * Test suite for the modern UI components integrated from MVP implementation.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { I18nextProvider } from 'react-i18next';
import i18n from '@/i18n';

// Components
import { Button } from '../button';
import { Input } from '../input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../card';
import LanguageSwitcher from '../language-switcher';
import NotificationSystem from '../notification-system';
import { useNotificationStore } from '@/stores/notification-store';

// Test wrapper
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <BrowserRouter>
    <I18nextProvider i18n={i18n}>
      {children}
    </I18nextProvider>
  </BrowserRouter>
);

describe('Modern UI Components', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Button Component', () => {
    it('should render with default variant', () => {
      render(
        <TestWrapper>
          <Button>Click me</Button>
        </TestWrapper>
      );

      const button = screen.getByRole('button', { name: /click me/i });
      expect(button).toBeInTheDocument();
      expect(button).toHaveClass('bg-primary');
    });

    it('should render with different variants', () => {
      const { rerender } = render(
        <TestWrapper>
          <Button variant="outline">Outline</Button>
        </TestWrapper>
      );

      expect(screen.getByRole('button')).toHaveClass('border');

      rerender(
        <TestWrapper>
          <Button variant="ghost">Ghost</Button>
        </TestWrapper>
      );

      expect(screen.getByRole('button')).toHaveClass('hover:bg-accent');

      rerender(
        <TestWrapper>
          <Button variant="destructive">Destructive</Button>
        </TestWrapper>
      );

      expect(screen.getByRole('button')).toHaveClass('bg-destructive');
    });

    it('should render with different sizes', () => {
      const { rerender } = render(
        <TestWrapper>
          <Button size="sm">Small</Button>
        </TestWrapper>
      );

      expect(screen.getByRole('button')).toHaveClass('h-9');

      rerender(
        <TestWrapper>
          <Button size="lg">Large</Button>
        </TestWrapper>
      );

      expect(screen.getByRole('button')).toHaveClass('h-11');

      rerender(
        <TestWrapper>
          <Button size="icon">Icon</Button>
        </TestWrapper>
      );

      expect(screen.getByRole('button')).toHaveClass('h-10', 'w-10');
    });

    it('should show loading state', () => {
      render(
        <TestWrapper>
          <Button loading>Loading</Button>
        </TestWrapper>
      );

      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
      expect(screen.getByRole('button')).toContainHTML('animate-spin');
    });

    it('should render with icons', () => {
      const LeftIcon = () => <span data-testid="left-icon">←</span>;
      const RightIcon = () => <span data-testid="right-icon">→</span>;

      render(
        <TestWrapper>
          <Button leftIcon={<LeftIcon />} rightIcon={<RightIcon />}>
            With Icons
          </Button>
        </TestWrapper>
      );

      expect(screen.getByTestId('left-icon')).toBeInTheDocument();
      expect(screen.getByTestId('right-icon')).toBeInTheDocument();
    });

    it('should handle click events', async () => {
      const handleClick = jest.fn();
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <Button onClick={handleClick}>Click me</Button>
        </TestWrapper>
      );

      await user.click(screen.getByRole('button'));
      expect(handleClick).toHaveBeenCalledTimes(1);
    });

    it('should be disabled when disabled prop is true', () => {
      render(
        <TestWrapper>
          <Button disabled>Disabled</Button>
        </TestWrapper>
      );

      expect(screen.getByRole('button')).toBeDisabled();
    });
  });

  describe('Input Component', () => {
    it('should render with label', () => {
      render(
        <TestWrapper>
          <Input label="Email" placeholder="Enter email" />
        </TestWrapper>
      );

      expect(screen.getByLabelText('Email')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Enter email')).toBeInTheDocument();
    });

    it('should show error message', () => {
      render(
        <TestWrapper>
          <Input label="Email" error="Email is required" />
        </TestWrapper>
      );

      expect(screen.getByText('Email is required')).toBeInTheDocument();
      expect(screen.getByRole('textbox')).toHaveClass('border-destructive');
    });

    it('should show helper text', () => {
      render(
        <TestWrapper>
          <Input label="Password" helperText="Must be at least 8 characters" />
        </TestWrapper>
      );

      expect(screen.getByText('Must be at least 8 characters')).toBeInTheDocument();
    });

    it('should render with icons', () => {
      const LeftIcon = () => <span data-testid="left-icon">@</span>;
      const RightIcon = () => <span data-testid="right-icon">👁</span>;

      render(
        <TestWrapper>
          <Input leftIcon={<LeftIcon />} rightIcon={<RightIcon />} />
        </TestWrapper>
      );

      expect(screen.getByTestId('left-icon')).toBeInTheDocument();
      expect(screen.getByTestId('right-icon')).toBeInTheDocument();
    });

    it('should handle input changes', async () => {
      const handleChange = jest.fn();
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <Input onChange={handleChange} />
        </TestWrapper>
      );

      const input = screen.getByRole('textbox');
      await user.type(input, '<EMAIL>');

      expect(handleChange).toHaveBeenCalled();
      expect(input).toHaveValue('<EMAIL>');
    });

    it('should support different input types', () => {
      const { rerender } = render(
        <TestWrapper>
          <Input type="password" />
        </TestWrapper>
      );

      expect(screen.getByRole('textbox')).toHaveAttribute('type', 'password');

      rerender(
        <TestWrapper>
          <Input type="email" />
        </TestWrapper>
      );

      expect(screen.getByRole('textbox')).toHaveAttribute('type', 'email');
    });
  });

  describe('Card Component', () => {
    it('should render card with all parts', () => {
      render(
        <TestWrapper>
          <Card>
            <CardHeader>
              <CardTitle>Card Title</CardTitle>
              <CardDescription>Card description</CardDescription>
            </CardHeader>
            <CardContent>
              <p>Card content</p>
            </CardContent>
          </Card>
        </TestWrapper>
      );

      expect(screen.getByText('Card Title')).toBeInTheDocument();
      expect(screen.getByText('Card description')).toBeInTheDocument();
      expect(screen.getByText('Card content')).toBeInTheDocument();
    });

    it('should apply custom className', () => {
      render(
        <TestWrapper>
          <Card className="custom-class">
            <CardContent>Content</CardContent>
          </Card>
        </TestWrapper>
      );

      const card = screen.getByText('Content').closest('div');
      expect(card).toHaveClass('custom-class');
    });
  });

  describe('Language Switcher', () => {
    it('should render dropdown variant by default', () => {
      render(
        <TestWrapper>
          <LanguageSwitcher />
        </TestWrapper>
      );

      expect(screen.getByRole('button', { name: /change language/i })).toBeInTheDocument();
      expect(screen.getByText('English')).toBeInTheDocument();
    });

    it('should render buttons variant', () => {
      render(
        <TestWrapper>
          <LanguageSwitcher variant="buttons" />
        </TestWrapper>
      );

      expect(screen.getByRole('button', { name: /english/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /ไทย/i })).toBeInTheDocument();
    });

    it('should open dropdown on click', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <LanguageSwitcher />
        </TestWrapper>
      );

      const button = screen.getByRole('button', { name: /change language/i });
      await user.click(button);

      expect(screen.getByRole('menuitem', { name: /english/i })).toBeInTheDocument();
      expect(screen.getByRole('menuitem', { name: /ไทย/i })).toBeInTheDocument();
    });

    it('should change language on selection', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <LanguageSwitcher />
        </TestWrapper>
      );

      const button = screen.getByRole('button', { name: /change language/i });
      await user.click(button);

      const thaiOption = screen.getByRole('menuitem', { name: /ไทย/i });
      await user.click(thaiOption);

      // Language should change (would need to verify i18n state)
      expect(localStorage.getItem('i18nextLng')).toBe('th');
    });

    it('should close dropdown when clicking outside', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <div>
            <LanguageSwitcher />
            <div data-testid="outside">Outside</div>
          </div>
        </TestWrapper>
      );

      const button = screen.getByRole('button', { name: /change language/i });
      await user.click(button);

      expect(screen.getByRole('menuitem', { name: /english/i })).toBeInTheDocument();

      await user.click(screen.getByTestId('outside'));

      await waitFor(() => {
        expect(screen.queryByRole('menuitem', { name: /english/i })).not.toBeInTheDocument();
      });
    });
  });

  describe('Notification System', () => {
    beforeEach(() => {
      // Clear notifications before each test
      useNotificationStore.getState().clearAllNotifications();
    });

    it('should not render when no notifications', () => {
      render(
        <TestWrapper>
          <NotificationSystem />
        </TestWrapper>
      );

      expect(screen.queryByRole('alert')).not.toBeInTheDocument();
    });

    it('should render notifications', () => {
      // Add a notification
      useNotificationStore.getState().addNotification({
        type: 'success',
        message: 'Test notification',
        title: 'Success',
      });

      render(
        <TestWrapper>
          <NotificationSystem />
        </TestWrapper>
      );

      expect(screen.getByRole('alert')).toBeInTheDocument();
      expect(screen.getByText('Success')).toBeInTheDocument();
      expect(screen.getByText('Test notification')).toBeInTheDocument();
    });

    it('should render different notification types', () => {
      const { rerender } = render(
        <TestWrapper>
          <NotificationSystem />
        </TestWrapper>
      );

      // Success notification
      useNotificationStore.getState().addNotification({
        type: 'success',
        message: 'Success message',
      });

      rerender(
        <TestWrapper>
          <NotificationSystem />
        </TestWrapper>
      );

      expect(screen.getByText('Success message')).toBeInTheDocument();

      // Clear and add error notification
      useNotificationStore.getState().clearAllNotifications();
      useNotificationStore.getState().addNotification({
        type: 'error',
        message: 'Error message',
      });

      rerender(
        <TestWrapper>
          <NotificationSystem />
        </TestWrapper>
      );

      expect(screen.getByText('Error message')).toBeInTheDocument();
    });

    it('should close notification on close button click', async () => {
      const user = userEvent.setup();

      useNotificationStore.getState().addNotification({
        type: 'info',
        message: 'Info message',
      });

      render(
        <TestWrapper>
          <NotificationSystem />
        </TestWrapper>
      );

      expect(screen.getByText('Info message')).toBeInTheDocument();

      const closeButton = screen.getByRole('button', { name: /close notification/i });
      await user.click(closeButton);

      await waitFor(() => {
        expect(screen.queryByText('Info message')).not.toBeInTheDocument();
      });
    });

    it('should handle notification with action', async () => {
      const actionHandler = jest.fn();
      const user = userEvent.setup();

      useNotificationStore.getState().addNotification({
        type: 'warning',
        message: 'Warning message',
        action: {
          label: 'Undo',
          onClick: actionHandler,
        },
      });

      render(
        <TestWrapper>
          <NotificationSystem />
        </TestWrapper>
      );

      const actionButton = screen.getByRole('button', { name: /undo/i });
      await user.click(actionButton);

      expect(actionHandler).toHaveBeenCalledTimes(1);
    });

    it('should auto-remove timed notifications', async () => {
      useNotificationStore.getState().addNotification({
        type: 'info',
        message: 'Timed message',
        duration: 100, // 100ms for testing
      });

      render(
        <TestWrapper>
          <NotificationSystem />
        </TestWrapper>
      );

      expect(screen.getByText('Timed message')).toBeInTheDocument();

      await waitFor(
        () => {
          expect(screen.queryByText('Timed message')).not.toBeInTheDocument();
        },
        { timeout: 200 }
      );
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      render(
        <TestWrapper>
          <Button aria-label="Custom button">Button</Button>
          <Input label="Email" aria-describedby="email-help" />
          <div id="email-help">Enter your email address</div>
        </TestWrapper>
      );

      const button = screen.getByRole('button');
      const input = screen.getByRole('textbox');

      expect(button).toHaveAttribute('aria-label', 'Custom button');
      expect(input).toHaveAttribute('aria-describedby', 'email-help');
    });

    it('should support keyboard navigation', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <Button>First</Button>
          <Button>Second</Button>
          <Input />
        </TestWrapper>
      );

      // Tab through elements
      await user.tab();
      expect(screen.getByRole('button', { name: /first/i })).toHaveFocus();

      await user.tab();
      expect(screen.getByRole('button', { name: /second/i })).toHaveFocus();

      await user.tab();
      expect(screen.getByRole('textbox')).toHaveFocus();
    });

    it('should have proper focus management', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <LanguageSwitcher />
        </TestWrapper>
      );

      const button = screen.getByRole('button', { name: /change language/i });
      
      // Focus and activate with keyboard
      button.focus();
      expect(button).toHaveFocus();

      await user.keyboard('{Enter}');
      expect(screen.getByRole('menuitem', { name: /english/i })).toBeInTheDocument();

      // Escape should close dropdown
      await user.keyboard('{Escape}');
      await waitFor(() => {
        expect(screen.queryByRole('menuitem')).not.toBeInTheDocument();
      });
    });
  });
});
