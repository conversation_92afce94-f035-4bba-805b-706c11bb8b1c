/**
 * Theme Configuration
 * 
 * Defines light and dark themes for the application.
 */

export interface Theme {
  colors: {
    // Primary colors
    primary: string;
    primaryLight: string;
    primaryDark: string;
    secondary: string;
    secondaryLight: string;
    secondaryDark: string;
    
    // Status colors
    success: string;
    successLight: string;
    successDark: string;
    warning: string;
    warningLight: string;
    warningDark: string;
    error: string;
    errorLight: string;
    errorDark: string;
    info: string;
    infoLight: string;
    infoDark: string;
    
    // Neutral colors
    text: string;
    textSecondary: string;
    textTertiary: string;
    background: string;
    backgroundSecondary: string;
    surface: string;
    border: string;
    borderLight: string;
    shadow: string;
    
    // Interactive colors
    hover: string;
    active: string;
    focus: string;
    disabled: string;
  };
  
  typography: {
    fontFamily: {
      primary: string;
      secondary: string;
      mono: string;
    };
    fontSize: {
      xs: string;
      sm: string;
      base: string;
      lg: string;
      xl: string;
      '2xl': string;
      '3xl': string;
      '4xl': string;
    };
    fontWeight: {
      light: number;
      normal: number;
      medium: number;
      semibold: number;
      bold: number;
    };
    lineHeight: {
      tight: number;
      normal: number;
      relaxed: number;
    };
  };
  
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    '2xl': string;
    '3xl': string;
    '4xl': string;
  };
  
  borderRadius: {
    none: string;
    small: string;
    medium: string;
    large: string;
    full: string;
  };
  
  breakpoints: {
    mobile: string;
    tablet: string;
    desktop: string;
    wide: string;
  };
  
  zIndex: {
    dropdown: number;
    sticky: number;
    fixed: number;
    modal: number;
    popover: number;
    tooltip: number;
  };
  
  transitions: {
    fast: string;
    normal: string;
    slow: string;
  };
}

// Light theme
export const lightTheme: Theme = {
  colors: {
    // Primary colors - Health/Medical theme
    primary: '#2563eb', // Blue
    primaryLight: '#60a5fa',
    primaryDark: '#1d4ed8',
    secondary: '#059669', // Green
    secondaryLight: '#34d399',
    secondaryDark: '#047857',
    
    // Status colors
    success: '#10b981',
    successLight: '#6ee7b7',
    successDark: '#059669',
    warning: '#f59e0b',
    warningLight: '#fbbf24',
    warningDark: '#d97706',
    error: '#ef4444',
    errorLight: '#f87171',
    errorDark: '#dc2626',
    info: '#3b82f6',
    infoLight: '#93c5fd',
    infoDark: '#2563eb',
    
    // Neutral colors
    text: '#111827',
    textSecondary: '#6b7280',
    textTertiary: '#9ca3af',
    background: '#ffffff',
    backgroundSecondary: '#f9fafb',
    surface: '#ffffff',
    border: '#e5e7eb',
    borderLight: '#f3f4f6',
    shadow: '#000000',
    
    // Interactive colors
    hover: '#f3f4f6',
    active: '#e5e7eb',
    focus: '#2563eb',
    disabled: '#d1d5db',
  },
  
  typography: {
    fontFamily: {
      primary: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
      secondary: 'Georgia, "Times New Roman", Times, serif',
      mono: '"SF Mono", Monaco, Inconsolata, "Roboto Mono", Consolas, "Courier New", monospace',
    },
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
      '4xl': '2.25rem',
    },
    fontWeight: {
      light: 300,
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
    },
    lineHeight: {
      tight: 1.25,
      normal: 1.5,
      relaxed: 1.75,
    },
  },
  
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '3rem',
    '3xl': '4rem',
    '4xl': '6rem',
  },
  
  borderRadius: {
    none: '0',
    small: '0.25rem',
    medium: '0.5rem',
    large: '0.75rem',
    full: '9999px',
  },
  
  breakpoints: {
    mobile: '640px',
    tablet: '768px',
    desktop: '1024px',
    wide: '1280px',
  },
  
  zIndex: {
    dropdown: 1000,
    sticky: 1020,
    fixed: 1030,
    modal: 1040,
    popover: 1050,
    tooltip: 1060,
  },
  
  transitions: {
    fast: '0.15s ease',
    normal: '0.2s ease',
    slow: '0.3s ease',
  },
};

// Dark theme
export const darkTheme: Theme = {
  ...lightTheme,
  colors: {
    ...lightTheme.colors,
    
    // Neutral colors for dark mode
    text: '#f9fafb',
    textSecondary: '#d1d5db',
    textTertiary: '#9ca3af',
    background: '#111827',
    backgroundSecondary: '#1f2937',
    surface: '#1f2937',
    border: '#374151',
    borderLight: '#4b5563',
    shadow: '#000000',
    
    // Interactive colors for dark mode
    hover: '#374151',
    active: '#4b5563',
    focus: '#60a5fa',
    disabled: '#6b7280',
  },
};

// Theme type for styled-components
declare module 'styled-components' {
  export interface DefaultTheme extends Theme {}
}
