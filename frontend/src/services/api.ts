/**
 * API Service Layer
 * 
 * This file contains the main API client and service functions for communicating
 * with the Supplement Tracker backend API.
 */

import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';
import { 
  ApiResponse, 
  ApiError, 
  LoginRequest, 
  RegisterRequest, 
  AuthResponse,
  User,
  Supplement,
  SupplementIntake,
  SupplementIntakeCreate,
  ResearchProtocol,
  CommunityPost,
  PaginatedResponse
} from '@/types/api';

// API Configuration
const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000/api/v1';

class ApiClient {
  private client: AxiosInstance;
  private authToken: string | null = null;

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000,
    });

    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (config) => {
        if (this.authToken) {
          config.headers.Authorization = `Bearer ${this.authToken}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response: AxiosResponse) => response,
      (error: AxiosError) => {
        if (error.response?.status === 401) {
          this.clearAuth();
          window.location.href = '/login';
        }
        return Promise.reject(this.handleApiError(error));
      }
    );

    // Load token from localStorage on initialization
    this.loadAuthToken();
  }

  private handleApiError(error: AxiosError): ApiError {
    if (error.response?.data) {
      return error.response.data as ApiError;
    }
    return {
      detail: error.message || 'An unexpected error occurred',
      status_code: error.response?.status || 500,
    };
  }

  private loadAuthToken(): void {
    const token = localStorage.getItem('auth_token');
    if (token) {
      this.authToken = token;
    }
  }

  public setAuthToken(token: string): void {
    this.authToken = token;
    localStorage.setItem('auth_token', token);
  }

  public clearAuth(): void {
    this.authToken = null;
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user');
  }

  // Authentication endpoints
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response = await this.client.post<AuthResponse>('/auth/login', credentials);
    this.setAuthToken(response.data.access_token);
    localStorage.setItem('user', JSON.stringify(response.data.user));
    return response.data;
  }

  async register(userData: RegisterRequest): Promise<AuthResponse> {
    const response = await this.client.post<AuthResponse>('/auth/register', userData);
    this.setAuthToken(response.data.access_token);
    localStorage.setItem('user', JSON.stringify(response.data.user));
    return response.data;
  }

  async logout(): Promise<void> {
    try {
      await this.client.post('/auth/logout');
    } finally {
      this.clearAuth();
    }
  }

  async refreshToken(): Promise<AuthResponse> {
    const response = await this.client.post<AuthResponse>('/auth/refresh');
    this.setAuthToken(response.data.access_token);
    return response.data;
  }

  // User endpoints
  async getCurrentUser(): Promise<User> {
    const response = await this.client.get<User>('/users/me');
    return response.data;
  }

  async updateUser(userData: Partial<User>): Promise<User> {
    const response = await this.client.put<User>('/users/me', userData);
    localStorage.setItem('user', JSON.stringify(response.data));
    return response.data;
  }

  async getUser(userId: string): Promise<User> {
    const response = await this.client.get<User>(`/users/${userId}`);
    return response.data;
  }

  // Supplement endpoints
  async getSupplements(params?: {
    search?: string;
    category?: string;
    skip?: number;
    limit?: number;
  }): Promise<PaginatedResponse<Supplement>> {
    const response = await this.client.get<PaginatedResponse<Supplement>>('/supplements', { params });
    return response.data;
  }

  async getSupplement(supplementId: string): Promise<Supplement> {
    const response = await this.client.get<Supplement>(`/supplements/${supplementId}`);
    return response.data;
  }

  async createSupplement(supplementData: Partial<Supplement>): Promise<Supplement> {
    const response = await this.client.post<Supplement>('/supplements', supplementData);
    return response.data;
  }

  async trackSupplementIntake(
    supplementId: string, 
    intakeData: SupplementIntakeCreate
  ): Promise<SupplementIntake> {
    const response = await this.client.post<SupplementIntake>(
      `/supplements/${supplementId}/track`, 
      intakeData
    );
    return response.data;
  }

  async getSupplementHistory(params?: {
    supplement_id?: string;
    skip?: number;
    limit?: number;
  }): Promise<SupplementIntake[]> {
    const response = await this.client.get<SupplementIntake[]>('/supplements/intake/history', { params });
    return response.data;
  }

  // Research endpoints
  async getResearchProtocols(params?: {
    status?: string;
    creator_id?: string;
    search?: string;
    skip?: number;
    limit?: number;
  }): Promise<ResearchProtocol[]> {
    const response = await this.client.get<ResearchProtocol[]>('/research/protocols', { params });
    return response.data;
  }

  async getResearchProtocol(protocolId: string): Promise<ResearchProtocol> {
    const response = await this.client.get<ResearchProtocol>(`/research/protocols/${protocolId}`);
    return response.data;
  }

  async createResearchProtocol(protocolData: Partial<ResearchProtocol>): Promise<ResearchProtocol> {
    const response = await this.client.post<ResearchProtocol>('/research/protocols', protocolData);
    return response.data;
  }

  async updateResearchProtocol(protocolId: string, updates: Partial<ResearchProtocol>): Promise<ResearchProtocol> {
    const response = await this.client.put<ResearchProtocol>(`/research/protocols/${protocolId}`, updates);
    return response.data;
  }

  async joinStudy(protocolId: string): Promise<StudyParticipation> {
    const response = await this.client.post<StudyParticipation>(`/research/protocols/${protocolId}/join`);
    return response.data;
  }

  async leaveStudy(protocolId: string): Promise<void> {
    await this.client.post(`/research/protocols/${protocolId}/leave`);
  }

  async getMyParticipations(): Promise<StudyParticipation[]> {
    const response = await this.client.get<StudyParticipation[]>('/research/my-participations');
    return response.data;
  }

  async submitDataCollection(protocolId: string, data: Partial<DataCollection>): Promise<DataCollection> {
    const response = await this.client.post<DataCollection>(`/research/protocols/${protocolId}/data`, data);
    return response.data;
  }

  async getProtocolData(protocolId: string, params?: { skip?: number; limit?: number }): Promise<DataCollection[]> {
    const response = await this.client.get<DataCollection[]>(`/research/protocols/${protocolId}/data`, { params });
    return response.data;
  }

  // Community endpoints
  async getCommunityPosts(params?: {
    skip?: number;
    limit?: number;
    group_id?: string;
    post_type?: string;
    author_id?: string;
  }): Promise<CommunityPost[]> {
    const response = await this.client.get<CommunityPost[]>('/community/posts', { params });
    return response.data;
  }

  async getCommunityPost(postId: string): Promise<CommunityPost> {
    const response = await this.client.get<CommunityPost>(`/community/posts/${postId}`);
    return response.data;
  }

  async createCommunityPost(postData: Partial<CommunityPost>): Promise<CommunityPost> {
    const response = await this.client.post<CommunityPost>('/community/posts', postData);
    return response.data;
  }

  async updateCommunityPost(postId: string, updates: Partial<CommunityPost>): Promise<CommunityPost> {
    const response = await this.client.put<CommunityPost>(`/community/posts/${postId}`, updates);
    return response.data;
  }

  async deleteCommunityPost(postId: string): Promise<void> {
    await this.client.delete(`/community/posts/${postId}`);
  }

  async likePost(postId: string): Promise<{ like_count: number }> {
    const response = await this.client.post<{ like_count: number }>(`/community/posts/${postId}/like`);
    return response.data;
  }

  async unlikePost(postId: string): Promise<{ like_count: number }> {
    const response = await this.client.delete<{ like_count: number }>(`/community/posts/${postId}/like`);
    return response.data;
  }

  async sharePost(postId: string): Promise<{ share_count: number }> {
    const response = await this.client.post<{ share_count: number }>(`/community/posts/${postId}/share`);
    return response.data;
  }

  async getPostComments(postId: string, params?: { skip?: number; limit?: number }): Promise<{ items: Comment[]; total: number }> {
    const response = await this.client.get<{ items: Comment[]; total: number }>(`/community/posts/${postId}/comments`, { params });
    return response.data;
  }

  async createComment(postId: string, commentData: { content: string }): Promise<Comment> {
    const response = await this.client.post<Comment>(`/community/posts/${postId}/comments`, commentData);
    return response.data;
  }

  async followUser(userId: string): Promise<{ follower_count: number }> {
    const response = await this.client.post<{ follower_count: number }>(`/users/${userId}/follow`);
    return response.data;
  }

  async unfollowUser(userId: string): Promise<{ follower_count: number }> {
    const response = await this.client.delete<{ follower_count: number }>(`/users/${userId}/follow`);
    return response.data;
  }

  async getUserProfile(userId: string): Promise<UserProfile> {
    const response = await this.client.get<UserProfile>(`/users/${userId}/profile`);
    return response.data;
  }

  // Analytics methods
  async getAnalyticsData(params?: {
    timeRange?: string;
    supplementIds?: string[];
    startDate?: string;
    endDate?: string;
    groupBy?: 'day' | 'week' | 'month';
  }): Promise<any> {
    const response = await this.client.get('/analytics/dashboard', { params });
    return response.data;
  }

  async getCorrelationAnalysis(params: {
    supplementIds: string[];
    timeRange?: string;
    metrics?: string[];
  }): Promise<any> {
    const response = await this.client.post('/analytics/correlations', params);
    return response.data;
  }

  async generateInsights(params?: {
    timeRange?: string;
    supplementIds?: string[];
  }): Promise<any> {
    const response = await this.client.post('/analytics/insights', params);
    return response.data;
  }

  async exportData(params: {
    format: 'csv' | 'json' | 'pdf';
    timeRange?: string;
    supplementIds?: string[];
    includeCharts?: boolean;
  }): Promise<any> {
    const response = await this.client.post('/analytics/export', params, {
      responseType: 'blob',
    });
    return response.data;
  }

  async getSupplementComparison(params: {
    supplementIds: string[];
    metrics: string[];
    timeRange?: string;
  }): Promise<any> {
    const response = await this.client.post('/analytics/comparison', params);
    return response.data;
  }

  async getPredictiveAnalysis(params: {
    supplementId: string;
    predictionDays: number;
    includeFactors?: string[];
  }): Promise<any> {
    const response = await this.client.post('/analytics/predictive', params);
    return response.data;
  }

  // Health metrics methods
  async getHealthMetrics(params?: {
    category?: string;
    timeRange?: string;
    deviceId?: string;
  }): Promise<any> {
    const response = await this.client.get('/health/metrics', { params });
    return response.data;
  }

  async createHealthMetric(metricData: any): Promise<any> {
    const response = await this.client.post('/health/metrics', metricData);
    return response.data;
  }

  async updateHealthMetric(metricId: string, updateData: any): Promise<any> {
    const response = await this.client.put(`/health/metrics/${metricId}`, updateData);
    return response.data;
  }

  async deleteHealthMetric(metricId: string): Promise<void> {
    await this.client.delete(`/health/metrics/${metricId}`);
  }

  async getConnectedDevices(): Promise<any> {
    const response = await this.client.get('/health/devices');
    return response.data;
  }

  async connectDevice(deviceData: {
    device_id: string;
    device_name: string;
    auth_token: string;
  }): Promise<any> {
    const response = await this.client.post('/health/devices/connect', deviceData);
    return response.data;
  }

  async disconnectDevice(deviceId: string): Promise<void> {
    await this.client.delete(`/health/devices/${deviceId}`);
  }

  async syncDeviceData(deviceId: string): Promise<any> {
    const response = await this.client.post(`/health/devices/${deviceId}/sync`);
    return response.data;
  }

  async getHealthCorrelations(params?: {
    timeRange?: string;
    metricIds?: string[];
    supplementIds?: string[];
  }): Promise<any> {
    const response = await this.client.get('/health/correlations', { params });
    return response.data;
  }

  async getBiomarkers(params?: {
    timeRange?: string;
    biomarkerType?: string;
  }): Promise<any> {
    const response = await this.client.get('/health/biomarkers', { params });
    return response.data;
  }

  async createBiomarkerReading(biomarkerData: any): Promise<any> {
    const response = await this.client.post('/health/biomarkers', biomarkerData);
    return response.data;
  }

  async generateHealthInsights(params?: {
    metricIds?: string[];
    timeRange?: string;
  }): Promise<any> {
    const response = await this.client.post('/health/insights', params);
    return response.data;
  }

  // Health check
  async healthCheck(): Promise<{ status: string; service: string; version: string }> {
    const response = await this.client.get('/health');
    return response.data;
  }
}

// Create and export singleton instance
export const apiClient = new ApiClient();

// Export individual service functions for easier testing
export const authService = {
  login: (credentials: LoginRequest) => apiClient.login(credentials),
  register: (userData: RegisterRequest) => apiClient.register(userData),
  logout: () => apiClient.logout(),
  refreshToken: () => apiClient.refreshToken(),
};

export const userService = {
  getCurrentUser: () => apiClient.getCurrentUser(),
  updateUser: (userData: Partial<User>) => apiClient.updateUser(userData),
  getUser: (userId: string) => apiClient.getUser(userId),
};

export const supplementService = {
  getSupplements: (params?: any) => apiClient.getSupplements(params),
  getSupplement: (supplementId: string) => apiClient.getSupplement(supplementId),
  createSupplement: (supplementData: Partial<Supplement>) => apiClient.createSupplement(supplementData),
  trackIntake: (supplementId: string, intakeData: SupplementIntakeCreate) => 
    apiClient.trackSupplementIntake(supplementId, intakeData),
  getHistory: (params?: any) => apiClient.getSupplementHistory(params),
};

export const researchService = {
  getProtocols: (params?: any) => apiClient.getResearchProtocols(params),
  getProtocol: (protocolId: string) => apiClient.getResearchProtocol(protocolId),
  createProtocol: (protocolData: Partial<ResearchProtocol>) => apiClient.createResearchProtocol(protocolData),
  updateProtocol: (protocolId: string, updates: Partial<ResearchProtocol>) =>
    apiClient.updateResearchProtocol(protocolId, updates),
  joinStudy: (protocolId: string) => apiClient.joinStudy(protocolId),
  leaveStudy: (protocolId: string) => apiClient.leaveStudy(protocolId),
  getMyParticipations: () => apiClient.getMyParticipations(),
  submitDataCollection: (protocolId: string, data: Partial<DataCollection>) =>
    apiClient.submitDataCollection(protocolId, data),
  getProtocolData: (protocolId: string, params?: any) => apiClient.getProtocolData(protocolId, params),
};

export const communityService = {
  getPosts: (params?: any) => apiClient.getCommunityPosts(params),
  getPost: (postId: string) => apiClient.getCommunityPost(postId),
  createPost: (postData: Partial<CommunityPost>) => apiClient.createCommunityPost(postData),
  updatePost: (postId: string, updates: Partial<CommunityPost>) =>
    apiClient.updateCommunityPost(postId, updates),
  deletePost: (postId: string) => apiClient.deleteCommunityPost(postId),
  likePost: (postId: string) => apiClient.likePost(postId),
  unlikePost: (postId: string) => apiClient.unlikePost(postId),
  sharePost: (postId: string) => apiClient.sharePost(postId),
  getPostComments: (postId: string, params?: any) => apiClient.getPostComments(postId, params),
  createComment: (postId: string, commentData: any) => apiClient.createComment(postId, commentData),
  followUser: (userId: string) => apiClient.followUser(userId),
  unfollowUser: (userId: string) => apiClient.unfollowUser(userId),
  getUserProfile: (userId: string) => apiClient.getUserProfile(userId),
};

export const analyticsService = {
  getAnalyticsData: (params?: any) => apiClient.getAnalyticsData(params),
  getCorrelationAnalysis: (params: any) => apiClient.getCorrelationAnalysis(params),
  generateInsights: (params?: any) => apiClient.generateInsights(params),
  exportData: (params: any) => apiClient.exportData(params),
  getSupplementComparison: (params: any) => apiClient.getSupplementComparison(params),
  getPredictiveAnalysis: (params: any) => apiClient.getPredictiveAnalysis(params),
};

export const healthService = {
  getHealthMetrics: (params?: any) => apiClient.getHealthMetrics(params),
  createHealthMetric: (metricData: any) => apiClient.createHealthMetric(metricData),
  updateHealthMetric: (metricId: string, updateData: any) => apiClient.updateHealthMetric(metricId, updateData),
  deleteHealthMetric: (metricId: string) => apiClient.deleteHealthMetric(metricId),
  getConnectedDevices: () => apiClient.getConnectedDevices(),
  connectDevice: (deviceData: any) => apiClient.connectDevice(deviceData),
  disconnectDevice: (deviceId: string) => apiClient.disconnectDevice(deviceId),
  syncDeviceData: (deviceId: string) => apiClient.syncDeviceData(deviceId),
  getHealthCorrelations: (params?: any) => apiClient.getHealthCorrelations(params),
  getBiomarkers: (params?: any) => apiClient.getBiomarkers(params),
  createBiomarkerReading: (biomarkerData: any) => apiClient.createBiomarkerReading(biomarkerData),
  generateHealthInsights: (params?: any) => apiClient.generateHealthInsights(params),
};

export default apiClient;
