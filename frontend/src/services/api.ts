/**
 * API Service Layer
 * 
 * This file contains the main API client and service functions for communicating
 * with the Supplement Tracker backend API.
 */

import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';
import { 
  ApiResponse, 
  ApiError, 
  LoginRequest, 
  RegisterRequest, 
  AuthResponse,
  User,
  Supplement,
  SupplementIntake,
  SupplementIntakeCreate,
  ResearchProtocol,
  CommunityPost,
  PaginatedResponse
} from '@/types/api';

// API Configuration
const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000/api/v1';

class ApiClient {
  private client: AxiosInstance;
  private authToken: string | null = null;

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000,
    });

    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (config) => {
        if (this.authToken) {
          config.headers.Authorization = `Bearer ${this.authToken}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response: AxiosResponse) => response,
      (error: AxiosError) => {
        if (error.response?.status === 401) {
          this.clearAuth();
          window.location.href = '/login';
        }
        return Promise.reject(this.handleApiError(error));
      }
    );

    // Load token from localStorage on initialization
    this.loadAuthToken();
  }

  private handleApiError(error: AxiosError): ApiError {
    if (error.response?.data) {
      return error.response.data as ApiError;
    }
    return {
      detail: error.message || 'An unexpected error occurred',
      status_code: error.response?.status || 500,
    };
  }

  private loadAuthToken(): void {
    const token = localStorage.getItem('auth_token');
    if (token) {
      this.authToken = token;
    }
  }

  public setAuthToken(token: string): void {
    this.authToken = token;
    localStorage.setItem('auth_token', token);
  }

  public clearAuth(): void {
    this.authToken = null;
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user');
  }

  // Authentication endpoints
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response = await this.client.post<AuthResponse>('/auth/login', credentials);
    this.setAuthToken(response.data.access_token);
    localStorage.setItem('user', JSON.stringify(response.data.user));
    return response.data;
  }

  async register(userData: RegisterRequest): Promise<AuthResponse> {
    const response = await this.client.post<AuthResponse>('/auth/register', userData);
    this.setAuthToken(response.data.access_token);
    localStorage.setItem('user', JSON.stringify(response.data.user));
    return response.data;
  }

  async logout(): Promise<void> {
    try {
      await this.client.post('/auth/logout');
    } finally {
      this.clearAuth();
    }
  }

  async refreshToken(): Promise<AuthResponse> {
    const response = await this.client.post<AuthResponse>('/auth/refresh');
    this.setAuthToken(response.data.access_token);
    return response.data;
  }

  // User endpoints
  async getCurrentUser(): Promise<User> {
    const response = await this.client.get<User>('/users/me');
    return response.data;
  }

  async updateUser(userData: Partial<User>): Promise<User> {
    const response = await this.client.put<User>('/users/me', userData);
    localStorage.setItem('user', JSON.stringify(response.data));
    return response.data;
  }

  async getUser(userId: string): Promise<User> {
    const response = await this.client.get<User>(`/users/${userId}`);
    return response.data;
  }

  // Supplement endpoints
  async getSupplements(params?: {
    search?: string;
    category?: string;
    skip?: number;
    limit?: number;
  }): Promise<PaginatedResponse<Supplement>> {
    const response = await this.client.get<PaginatedResponse<Supplement>>('/supplements', { params });
    return response.data;
  }

  async getSupplement(supplementId: string): Promise<Supplement> {
    const response = await this.client.get<Supplement>(`/supplements/${supplementId}`);
    return response.data;
  }

  async createSupplement(supplementData: Partial<Supplement>): Promise<Supplement> {
    const response = await this.client.post<Supplement>('/supplements', supplementData);
    return response.data;
  }

  async trackSupplementIntake(
    supplementId: string, 
    intakeData: SupplementIntakeCreate
  ): Promise<SupplementIntake> {
    const response = await this.client.post<SupplementIntake>(
      `/supplements/${supplementId}/track`, 
      intakeData
    );
    return response.data;
  }

  async getSupplementHistory(params?: {
    supplement_id?: string;
    skip?: number;
    limit?: number;
  }): Promise<SupplementIntake[]> {
    const response = await this.client.get<SupplementIntake[]>('/supplements/intake/history', { params });
    return response.data;
  }

  // Research endpoints
  async getResearchProtocols(params?: {
    status?: string;
    creator_id?: string;
    search?: string;
    skip?: number;
    limit?: number;
  }): Promise<ResearchProtocol[]> {
    const response = await this.client.get<ResearchProtocol[]>('/research/protocols', { params });
    return response.data;
  }

  async getResearchProtocol(protocolId: string): Promise<ResearchProtocol> {
    const response = await this.client.get<ResearchProtocol>(`/research/protocols/${protocolId}`);
    return response.data;
  }

  async createResearchProtocol(protocolData: Partial<ResearchProtocol>): Promise<ResearchProtocol> {
    const response = await this.client.post<ResearchProtocol>('/research/protocols', protocolData);
    return response.data;
  }

  // Community endpoints
  async getCommunityPosts(params?: {
    skip?: number;
    limit?: number;
    group_id?: string;
    post_type?: string;
    author_id?: string;
  }): Promise<CommunityPost[]> {
    const response = await this.client.get<CommunityPost[]>('/community/posts', { params });
    return response.data;
  }

  async getCommunityPost(postId: string): Promise<CommunityPost> {
    const response = await this.client.get<CommunityPost>(`/community/posts/${postId}`);
    return response.data;
  }

  async createCommunityPost(postData: Partial<CommunityPost>): Promise<CommunityPost> {
    const response = await this.client.post<CommunityPost>('/community/posts', postData);
    return response.data;
  }

  // Health check
  async healthCheck(): Promise<{ status: string; service: string; version: string }> {
    const response = await this.client.get('/health');
    return response.data;
  }
}

// Create and export singleton instance
export const apiClient = new ApiClient();

// Export individual service functions for easier testing
export const authService = {
  login: (credentials: LoginRequest) => apiClient.login(credentials),
  register: (userData: RegisterRequest) => apiClient.register(userData),
  logout: () => apiClient.logout(),
  refreshToken: () => apiClient.refreshToken(),
};

export const userService = {
  getCurrentUser: () => apiClient.getCurrentUser(),
  updateUser: (userData: Partial<User>) => apiClient.updateUser(userData),
  getUser: (userId: string) => apiClient.getUser(userId),
};

export const supplementService = {
  getSupplements: (params?: any) => apiClient.getSupplements(params),
  getSupplement: (supplementId: string) => apiClient.getSupplement(supplementId),
  createSupplement: (supplementData: Partial<Supplement>) => apiClient.createSupplement(supplementData),
  trackIntake: (supplementId: string, intakeData: SupplementIntakeCreate) => 
    apiClient.trackSupplementIntake(supplementId, intakeData),
  getHistory: (params?: any) => apiClient.getSupplementHistory(params),
};

export const researchService = {
  getProtocols: (params?: any) => apiClient.getResearchProtocols(params),
  getProtocol: (protocolId: string) => apiClient.getResearchProtocol(protocolId),
  createProtocol: (protocolData: Partial<ResearchProtocol>) => apiClient.createResearchProtocol(protocolData),
};

export const communityService = {
  getPosts: (params?: any) => apiClient.getCommunityPosts(params),
  getPost: (postId: string) => apiClient.getCommunityPost(postId),
  createPost: (postData: Partial<CommunityPost>) => apiClient.createCommunityPost(postData),
};

export default apiClient;
