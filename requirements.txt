# Core Framework
fastapi==0.115.6
uvicorn[standard]==0.32.1
pydantic==2.10.4
pydantic-settings==2.7.0

# Database
sqlalchemy==2.0.36
alembic==1.14.0
asyncpg==0.30.0
psycopg2-binary==2.9.10

# Authentication & Security
python-jose[cryptography]==3.3.0  # Note: Consider migrating to PyJWT for better security
passlib[bcrypt]==1.7.4
python-multipart==0.0.18

# Caching & Background Tasks
redis==5.2.1
celery==5.4.0

# HTTP Client
httpx==0.28.1

# Data Processing & ML
pandas==2.2.3
numpy==1.26.4
scikit-learn==1.6.0
tensorflow==2.18.0

# NLP
spacy==3.8.3
transformers==4.50.0

# Search
elasticsearch==8.17.0

# File Storage
boto3==1.35.95

# Monitoring & Logging
structlog==25.4.0
sentry-sdk[fastapi]==2.21.0

# Development Dependencies
pytest==8.3.4
pytest-asyncio==0.25.0
pytest-cov==6.0.0
black==24.10.0
flake8==7.1.1
mypy==1.14.1
pre-commit==4.0.1

# Documentation
mkdocs==1.6.1
mkdocs-material==9.5.50
