"""
Tests for community features functionality.

This module contains tests for community features including user following,
groups, posts, comments, peer reviews, and notifications.
"""

import pytest
from uuid import uuid4

from app.modules.community_features.models import (
    CommunityGroup, CommunityPost, Notification, PeerReview, PostComment, UserFollow
)
from app.modules.community_features.schemas import (
    CommunityGroupCreate, CommunityPostCreate, NotificationCreate,
    PeerReviewCreate, PostCommentCreate, UserFollowCreate
)
from app.modules.community_features.services import (
    CommunityGroupService, CommunityPostService, NotificationService,
    PeerReviewService, PostCommentService, UserFollowService
)


class TestUserFollowService:
    """Test cases for UserFollowService."""

    @pytest.mark.asyncio
    async def test_follow_user(self, db_session):
        """Test following a user."""
        follower_id = uuid4()
        followed_id = uuid4()
        
        follow_data = UserFollowCreate(followed_id=followed_id)
        follow = await UserFollowService.follow_user(db_session, follower_id, follow_data)
        
        assert follow.follower_id == follower_id
        assert follow.followed_id == followed_id
        assert follow.created_at is not None

    @pytest.mark.asyncio
    async def test_follow_user_already_following(self, db_session):
        """Test following a user that is already being followed."""
        follower_id = uuid4()
        followed_id = uuid4()
        
        # Create initial follow
        follow_data = UserFollowCreate(followed_id=followed_id)
        await UserFollowService.follow_user(db_session, follower_id, follow_data)
        
        # Try to follow again
        with pytest.raises(ValueError, match="Already following this user"):
            await UserFollowService.follow_user(db_session, follower_id, follow_data)

    @pytest.mark.asyncio
    async def test_follow_self(self, db_session):
        """Test that users cannot follow themselves."""
        user_id = uuid4()
        
        follow_data = UserFollowCreate(followed_id=user_id)
        with pytest.raises(ValueError, match="Cannot follow yourself"):
            await UserFollowService.follow_user(db_session, user_id, follow_data)

    @pytest.mark.asyncio
    async def test_unfollow_user(self, db_session):
        """Test unfollowing a user."""
        follower_id = uuid4()
        followed_id = uuid4()
        
        # Create follow relationship
        follow_data = UserFollowCreate(followed_id=followed_id)
        await UserFollowService.follow_user(db_session, follower_id, follow_data)
        
        # Unfollow
        success = await UserFollowService.unfollow_user(db_session, follower_id, followed_id)
        assert success is True

    @pytest.mark.asyncio
    async def test_unfollow_user_not_following(self, db_session):
        """Test unfollowing a user that is not being followed."""
        follower_id = uuid4()
        followed_id = uuid4()
        
        success = await UserFollowService.unfollow_user(db_session, follower_id, followed_id)
        assert success is False


class TestCommunityGroupService:
    """Test cases for CommunityGroupService."""

    @pytest.mark.asyncio
    async def test_create_group(self, db_session):
        """Test creating a community group."""
        creator_id = uuid4()
        group_data = CommunityGroupCreate(
            name="Test Group",
            description="A test group",
            category="General",
            is_public=True,
            is_moderated=False
        )
        
        group = await CommunityGroupService.create_group(db_session, group_data, creator_id)
        
        assert group.name == "Test Group"
        assert group.description == "A test group"
        assert group.category == "General"
        assert group.is_public is True
        assert group.is_moderated is False
        assert group.created_by_user_id == creator_id
        assert group.member_count == 0

    @pytest.mark.asyncio
    async def test_get_group(self, db_session):
        """Test getting a community group by ID."""
        creator_id = uuid4()
        group_data = CommunityGroupCreate(
            name="Test Group",
            description="A test group",
            category="General"
        )
        
        created_group = await CommunityGroupService.create_group(db_session, group_data, creator_id)
        retrieved_group = await CommunityGroupService.get_group(db_session, created_group.id)
        
        assert retrieved_group is not None
        assert retrieved_group.id == created_group.id
        assert retrieved_group.name == "Test Group"

    @pytest.mark.asyncio
    async def test_get_groups_with_filtering(self, db_session):
        """Test getting groups with category filtering."""
        creator_id = uuid4()
        
        # Create groups in different categories
        group1_data = CommunityGroupCreate(name="Group 1", category="Vitamins")
        group2_data = CommunityGroupCreate(name="Group 2", category="Minerals")
        group3_data = CommunityGroupCreate(name="Group 3", category="Vitamins")
        
        await CommunityGroupService.create_group(db_session, group1_data, creator_id)
        await CommunityGroupService.create_group(db_session, group2_data, creator_id)
        await CommunityGroupService.create_group(db_session, group3_data, creator_id)
        
        # Get groups filtered by category
        vitamin_groups = await CommunityGroupService.get_groups(
            db_session, category="Vitamins"
        )
        
        assert len(vitamin_groups) == 2
        for group in vitamin_groups:
            assert group.category == "Vitamins"


class TestCommunityPostService:
    """Test cases for CommunityPostService."""

    @pytest.mark.asyncio
    async def test_create_post(self, db_session):
        """Test creating a community post."""
        author_id = uuid4()
        post_data = CommunityPostCreate(
            title="Test Post",
            content="This is a test post content",
            post_type="discussion"
        )
        
        post = await CommunityPostService.create_post(db_session, post_data, author_id)
        
        assert post.title == "Test Post"
        assert post.content == "This is a test post content"
        assert post.post_type == "discussion"
        assert post.author_id == author_id
        assert post.view_count == 0
        assert post.like_count == 0
        assert post.comment_count == 0

    @pytest.mark.asyncio
    async def test_increment_view_count(self, db_session):
        """Test incrementing post view count."""
        author_id = uuid4()
        post_data = CommunityPostCreate(
            title="Test Post",
            content="This is a test post content"
        )
        
        post = await CommunityPostService.create_post(db_session, post_data, author_id)
        initial_views = post.view_count
        
        await CommunityPostService.increment_view_count(db_session, post.id)
        
        updated_post = await CommunityPostService.get_post(db_session, post.id)
        assert updated_post.view_count == initial_views + 1


class TestNotificationService:
    """Test cases for NotificationService."""

    @pytest.mark.asyncio
    async def test_create_notification(self, db_session):
        """Test creating a notification."""
        user_id = uuid4()
        notification_data = NotificationCreate(
            user_id=user_id,
            type="follow",
            title="New Follower",
            message="Someone started following you"
        )
        
        notification = await NotificationService.create_notification(db_session, notification_data)
        
        assert notification.user_id == user_id
        assert notification.type == "follow"
        assert notification.title == "New Follower"
        assert notification.message == "Someone started following you"
        assert notification.is_read is False
        assert notification.is_sent is False

    @pytest.mark.asyncio
    async def test_mark_as_read(self, db_session):
        """Test marking a notification as read."""
        user_id = uuid4()
        notification_data = NotificationCreate(
            user_id=user_id,
            type="comment",
            title="New Comment",
            message="Someone commented on your post"
        )
        
        notification = await NotificationService.create_notification(db_session, notification_data)
        assert notification.is_read is False
        
        success = await NotificationService.mark_as_read(db_session, notification.id, user_id)
        assert success is True
        
        # Verify it's marked as read
        updated_notification = await NotificationService.get_user_notifications(
            db_session, user_id
        )
        assert len(updated_notification) == 1
        assert updated_notification[0].is_read is True

    @pytest.mark.asyncio
    async def test_get_unread_count(self, db_session):
        """Test getting unread notification count."""
        user_id = uuid4()
        
        # Create multiple notifications
        for i in range(3):
            notification_data = NotificationCreate(
                user_id=user_id,
                type="test",
                title=f"Test {i}",
                message=f"Test message {i}"
            )
            await NotificationService.create_notification(db_session, notification_data)
        
        unread_count = await NotificationService.get_unread_count(db_session, user_id)
        assert unread_count == 3
        
        # Mark one as read
        notifications = await NotificationService.get_user_notifications(db_session, user_id)
        await NotificationService.mark_as_read(db_session, notifications[0].id, user_id)
        
        unread_count = await NotificationService.get_unread_count(db_session, user_id)
        assert unread_count == 2


class TestPeerReviewService:
    """Test cases for PeerReviewService."""

    @pytest.mark.asyncio
    async def test_create_review(self, db_session):
        """Test creating a peer review."""
        reviewer_id = uuid4()
        content_id = uuid4()
        
        review_data = PeerReviewCreate(
            content_type="post",
            content_id=content_id,
            score=8,
            feedback="Good quality content",
            expertise_level="intermediate"
        )
        
        review = await PeerReviewService.create_review(db_session, review_data, reviewer_id)
        
        assert review.content_type == "post"
        assert review.content_id == content_id
        assert review.reviewer_id == reviewer_id
        assert review.score == 8
        assert review.feedback == "Good quality content"
        assert review.expertise_level == "intermediate"
        assert review.status == "pending"

    @pytest.mark.asyncio
    async def test_create_duplicate_review(self, db_session):
        """Test that users cannot review the same content twice."""
        reviewer_id = uuid4()
        content_id = uuid4()
        
        review_data = PeerReviewCreate(
            content_type="post",
            content_id=content_id,
            score=8,
            feedback="Good quality content"
        )
        
        # Create first review
        await PeerReviewService.create_review(db_session, review_data, reviewer_id)
        
        # Try to create duplicate review
        with pytest.raises(ValueError, match="You have already reviewed this content"):
            await PeerReviewService.create_review(db_session, review_data, reviewer_id)
