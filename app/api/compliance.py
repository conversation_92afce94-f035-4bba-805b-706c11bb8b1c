"""
Compliance API Endpoints

This module provides API endpoints for legal compliance features including
consent management, user rights, and data protection.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, Depends, HTTPException, Request, Response
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session
from io import BytesIO

from app.database.session import get_db
from app.models.user import User
from app.auth.dependencies import get_current_user
from app.compliance.consent_manager import (
    ConsentManager, ConsentType, ConsentValidator, ConsentRecord
)
from app.compliance.user_rights import (
    UserRightsManager, RequestType, DataExportConfig, UserRightsRequest
)
from app.compliance.data_anonymizer import DataAnonymizer, AnonymizationConfig, AnonymizationLevel


router = APIRouter(prefix="/api/compliance", tags=["compliance"])


# Pydantic Models for API

class ConsentRequest(BaseModel):
    """Request model for consent submission."""
    consent_type: ConsentType
    consent_version: str
    consent_text: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class ConsentWithdrawal(BaseModel):
    """Request model for consent withdrawal."""
    consent_type: ConsentType
    withdrawal_reason: Optional[str] = None


class UserRightsRequestModel(BaseModel):
    """Request model for user rights requests."""
    request_type: RequestType
    description: Optional[str] = None
    request_data: Optional[Dict[str, Any]] = None


class DataExportRequest(BaseModel):
    """Request model for data export."""
    include_research_data: bool = True
    include_health_data: bool = True
    include_activity_logs: bool = False
    anonymize_shared_data: bool = True
    format: str = Field(default="json", regex="^(json|csv|xml)$")


class ConsentResponse(BaseModel):
    """Response model for consent operations."""
    consent_id: int
    consent_type: str
    status: str
    version: str
    given_at: Optional[datetime]
    expires_at: Optional[datetime]


class UserRightsResponse(BaseModel):
    """Response model for user rights requests."""
    request_id: int
    request_type: str
    status: str
    requested_at: datetime
    due_date: datetime
    completed_at: Optional[datetime]


# Consent Management Endpoints

@router.post("/consent", response_model=ConsentResponse)
async def record_consent(
    consent_request: ConsentRequest,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Record user consent for a specific type and version."""
    consent_manager = ConsentManager(db)
    
    # Get client information
    ip_address = request.client.host
    user_agent = request.headers.get("user-agent")
    
    try:
        consent_record = consent_manager.record_consent(
            user_id=current_user.id,
            consent_type=consent_request.consent_type,
            consent_version=consent_request.consent_version,
            consent_text=consent_request.consent_text,
            metadata=consent_request.metadata,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        return ConsentResponse(
            consent_id=consent_record.id,
            consent_type=consent_record.consent_type,
            status=consent_record.consent_status,
            version=consent_record.consent_version,
            given_at=consent_record.given_at,
            expires_at=consent_record.expires_at
        )
    
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Failed to record consent: {str(e)}")


@router.post("/consent/withdraw")
async def withdraw_consent(
    withdrawal: ConsentWithdrawal,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Withdraw user consent for a specific type."""
    consent_manager = ConsentManager(db)
    
    ip_address = request.client.host
    
    success = consent_manager.withdraw_consent(
        user_id=current_user.id,
        consent_type=withdrawal.consent_type,
        withdrawal_reason=withdrawal.withdrawal_reason,
        ip_address=ip_address
    )
    
    if not success:
        raise HTTPException(status_code=400, detail="Failed to withdraw consent")
    
    return {"message": "Consent withdrawn successfully"}


@router.get("/consent/status")
async def get_consent_status(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get current consent status for all consent types."""
    consent_manager = ConsentManager(db)
    
    consent_summary = consent_manager.get_consent_summary(current_user.id)
    
    return {"consent_status": consent_summary}


@router.get("/consent/history")
async def get_consent_history(
    consent_type: Optional[ConsentType] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get consent history for the user."""
    consent_manager = ConsentManager(db)
    
    consent_history = consent_manager.get_consent_history(current_user.id, consent_type)
    
    return {
        "consent_history": [
            {
                "consent_type": record.consent_type,
                "status": record.consent_status,
                "version": record.consent_version,
                "given_at": record.given_at,
                "withdrawn_at": record.withdrawn_at,
                "expires_at": record.expires_at
            }
            for record in consent_history
        ]
    }


# User Rights Management Endpoints

@router.post("/rights/request", response_model=UserRightsResponse)
async def submit_rights_request(
    rights_request: UserRightsRequestModel,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Submit a user rights request (GDPR, CCPA, HIPAA)."""
    rights_manager = UserRightsManager(db)
    
    ip_address = request.client.host
    user_agent = request.headers.get("user-agent")
    
    try:
        request_record = rights_manager.submit_request(
            user_id=current_user.id,
            request_type=rights_request.request_type,
            description=rights_request.description,
            request_data=rights_request.request_data,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        return UserRightsResponse(
            request_id=request_record.id,
            request_type=request_record.request_type,
            status=request_record.request_status,
            requested_at=request_record.requested_at,
            due_date=request_record.due_date,
            completed_at=request_record.completed_at
        )
    
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Failed to submit request: {str(e)}")


@router.get("/rights/requests")
async def get_rights_requests(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all user rights requests for the current user."""
    rights_manager = UserRightsManager(db)
    
    requests = rights_manager._get_user_requests(current_user.id)
    
    return {
        "requests": [
            {
                "request_id": req.id,
                "request_type": req.request_type,
                "status": req.request_status,
                "requested_at": req.requested_at,
                "due_date": req.due_date,
                "completed_at": req.completed_at,
                "description": req.request_description
            }
            for req in requests
        ]
    }


@router.get("/rights/request/{request_id}/status")
async def get_request_status(
    request_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get status of a specific user rights request."""
    rights_manager = UserRightsManager(db)
    
    try:
        status = rights_manager.get_request_status(request_id)
        
        # Verify the request belongs to the current user
        request_record = rights_manager._get_request(request_id)
        if not request_record or request_record.user_id != current_user.id:
            raise HTTPException(status_code=404, detail="Request not found")
        
        return status
    
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))


# Data Export and Portability Endpoints

@router.post("/data/export")
async def export_user_data(
    export_request: DataExportRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Export user data in requested format (GDPR Article 20)."""
    rights_manager = UserRightsManager(db)
    
    config = DataExportConfig(
        include_research_data=export_request.include_research_data,
        include_health_data=export_request.include_health_data,
        include_activity_logs=export_request.include_activity_logs,
        anonymize_shared_data=export_request.anonymize_shared_data,
        format=export_request.format
    )
    
    try:
        exported_data = rights_manager.export_user_data(current_user.id, config)
        
        # Determine content type and filename
        if config.format == "json":
            media_type = "application/json"
            filename = f"user_data_{current_user.id}.json"
        elif config.format == "csv":
            media_type = "text/csv"
            filename = f"user_data_{current_user.id}.csv"
        elif config.format == "xml":
            media_type = "application/xml"
            filename = f"user_data_{current_user.id}.xml"
        
        return StreamingResponse(
            BytesIO(exported_data),
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to export data: {str(e)}")


@router.get("/data/package")
async def download_data_package(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Download complete data package as ZIP file."""
    rights_manager = UserRightsManager(db)
    
    config = DataExportConfig(
        include_research_data=True,
        include_health_data=True,
        include_activity_logs=True,
        format="json"
    )
    
    try:
        data_package = rights_manager.create_data_package(current_user.id, config)
        
        return StreamingResponse(
            BytesIO(data_package),
            media_type="application/zip",
            headers={
                "Content-Disposition": f"attachment; filename=user_data_package_{current_user.id}.zip"
            }
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create data package: {str(e)}")


# Compliance Validation Endpoints

@router.get("/validation/research/{study_id}")
async def validate_research_consent(
    study_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Validate user consent for research participation."""
    consent_manager = ConsentManager(db)
    consent_validator = ConsentValidator(consent_manager)
    
    is_valid = consent_validator.validate_research_participation(current_user.id, study_id)
    
    if not is_valid:
        missing_consents = consent_validator.get_missing_consents(
            current_user.id, "research_participation"
        )
        
        return {
            "valid": False,
            "missing_consents": [consent.value for consent in missing_consents]
        }
    
    return {"valid": True, "missing_consents": []}


@router.get("/validation/marketing")
async def validate_marketing_consent(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Validate user consent for marketing communications."""
    consent_manager = ConsentManager(db)
    consent_validator = ConsentValidator(consent_manager)
    
    is_valid = consent_validator.validate_marketing_communication(current_user.id)
    
    return {"valid": is_valid}


@router.get("/validation/analytics")
async def validate_analytics_consent(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Validate user consent for analytics tracking."""
    consent_manager = ConsentManager(db)
    consent_validator = ConsentValidator(consent_manager)
    
    is_valid = consent_validator.validate_analytics_tracking(current_user.id)
    
    return {"valid": is_valid}


# Legal Document Endpoints

@router.get("/documents/privacy-policy")
async def get_privacy_policy():
    """Get current privacy policy."""
    # In production, this would fetch from database or file system
    return {
        "document_type": "privacy_policy",
        "version": "1.0",
        "effective_date": "2024-12-17",
        "url": "/legal/privacy-policy",
        "content_summary": "Comprehensive privacy policy covering data collection, use, and user rights"
    }


@router.get("/documents/terms-of-service")
async def get_terms_of_service():
    """Get current terms of service."""
    return {
        "document_type": "terms_of_service",
        "version": "1.0",
        "effective_date": "2024-12-17",
        "url": "/legal/terms-of-service",
        "content_summary": "Terms governing platform use, research participation, and user responsibilities"
    }


@router.get("/documents/consent-forms/{study_id}")
async def get_consent_form(study_id: str):
    """Get consent form for a specific research study."""
    # In production, this would fetch study-specific consent forms
    return {
        "study_id": study_id,
        "consent_form_version": "1.0",
        "last_updated": "2024-12-17",
        "url": f"/legal/consent-forms/{study_id}",
        "required_consents": [
            "research_participation",
            "health_data_collection",
            "data_sharing"
        ]
    }
