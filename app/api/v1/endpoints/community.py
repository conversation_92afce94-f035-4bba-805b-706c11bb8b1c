"""
Community features endpoints for the supplement tracking platform.

This module provides endpoints for community interactions, discussions,
and social features.
"""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.modules.user_management.dependencies import get_current_user
from app.modules.user_management.models import User
from app.modules.community_features.schemas import (
    CommunityGroupCreate, CommunityGroupListResponse, CommunityGroupResponse, CommunityGroupUpdate,
    CommunityPostCreate, CommunityPostListResponse, CommunityPostResponse, CommunityPostUpdate,
    NotificationListResponse, NotificationResponse,
    PeerReviewCreate, PeerReviewListResponse, PeerReviewResponse, PeerReviewUpdate,
    PostCommentCreate, PostCommentListResponse, PostCommentResponse, PostCommentUpdate,
    UserFollowCreate, UserFollowResponse
)
from app.modules.community_features.services import (
    CommunityGroupService, CommunityPostService, NotificationService,
    PeerReviewService, PostCommentService, UserFollowService
)

router = APIRouter()


# User Follow Endpoints
@router.post("/follow", response_model=UserFollowResponse)
async def follow_user(
    follow_data: UserFollowCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Follow a user.

    Creates a new follow relationship between the current user and the specified user.
    """
    try:
        follow = await UserFollowService.follow_user(db, current_user.id, follow_data)
        return follow
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.delete("/follow/{user_id}")
async def unfollow_user(
    user_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Unfollow a user.

    Removes the follow relationship between the current user and the specified user.
    """
    success = await UserFollowService.unfollow_user(db, current_user.id, user_id)
    if not success:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Follow relationship not found")
    return {"message": "Successfully unfollowed user"}


@router.get("/followers/{user_id}", response_model=List[UserFollowResponse])
async def get_user_followers(
    user_id: UUID,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: AsyncSession = Depends(get_db)
):
    """
    Get followers of a user.

    Returns a list of users following the specified user.
    """
    followers = await UserFollowService.get_followers(db, user_id, skip, limit)
    return followers


@router.get("/following/{user_id}", response_model=List[UserFollowResponse])
async def get_user_following(
    user_id: UUID,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: AsyncSession = Depends(get_db)
):
    """
    Get users that a user is following.

    Returns a list of users that the specified user is following.
    """
    following = await UserFollowService.get_following(db, user_id, skip, limit)
    return following


# Community Group Endpoints
@router.post("/groups", response_model=CommunityGroupResponse)
async def create_community_group(
    group_data: CommunityGroupCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new community group.

    Creates a new community group for organizing discussions around specific topics.
    """
    group = await CommunityGroupService.create_group(db, group_data, current_user.id)
    return group


@router.get("/groups", response_model=List[CommunityGroupResponse])
async def list_community_groups(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    category: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db)
):
    """
    List community groups.

    Returns available community groups and research topics with optional filtering.
    """
    groups = await CommunityGroupService.get_groups(db, skip, limit, category)
    return groups


@router.get("/groups/{group_id}", response_model=CommunityGroupResponse)
async def get_community_group(
    group_id: UUID,
    db: AsyncSession = Depends(get_db)
):
    """
    Get a specific community group.

    Returns details of a specific community group.
    """
    group = await CommunityGroupService.get_group(db, group_id)
    if not group:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Group not found")
    return group


@router.put("/groups/{group_id}", response_model=CommunityGroupResponse)
async def update_community_group(
    group_id: UUID,
    group_data: CommunityGroupUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update a community group.

    Updates a community group. Only the group creator can update the group.
    """
    group = await CommunityGroupService.update_group(db, group_id, group_data, current_user.id)
    if not group:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Group not found or not authorized")
    return group


@router.delete("/groups/{group_id}")
async def delete_community_group(
    group_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Delete a community group.

    Deletes a community group. Only the group creator can delete the group.
    """
    success = await CommunityGroupService.delete_group(db, group_id, current_user.id)
    if not success:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Group not found or not authorized")
    return {"message": "Group deleted successfully"}


# Community Post Endpoints
@router.post("/posts", response_model=CommunityPostResponse)
async def create_community_post(
    post_data: CommunityPostCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new community post.

    Creates a new community post or discussion in a group or general forum.
    """
    post = await CommunityPostService.create_post(db, post_data, current_user.id)
    return post


@router.get("/posts", response_model=List[CommunityPostResponse])
async def list_community_posts(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    group_id: Optional[UUID] = Query(None),
    post_type: Optional[str] = Query(None),
    author_id: Optional[UUID] = Query(None),
    db: AsyncSession = Depends(get_db)
):
    """
    List community posts.

    Returns a paginated list of community posts and discussions with optional filtering.
    """
    posts = await CommunityPostService.get_posts(db, skip, limit, group_id, post_type, author_id)
    return posts


@router.get("/posts/{post_id}", response_model=CommunityPostResponse)
async def get_community_post(
    post_id: UUID,
    db: AsyncSession = Depends(get_db)
):
    """
    Get a specific community post.

    Returns details of a specific community post and increments view count.
    """
    post = await CommunityPostService.get_post(db, post_id)
    if not post:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Post not found")

    # Increment view count
    await CommunityPostService.increment_view_count(db, post_id)

    return post


@router.put("/posts/{post_id}", response_model=CommunityPostResponse)
async def update_community_post(
    post_id: UUID,
    post_data: CommunityPostUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update a community post.

    Updates a community post. Only the post author can update the post.
    """
    post = await CommunityPostService.update_post(db, post_id, post_data, current_user.id)
    if not post:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Post not found or not authorized")
    return post


@router.delete("/posts/{post_id}")
async def delete_community_post(
    post_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Delete a community post.

    Deletes a community post. Only the post author can delete the post.
    """
    success = await CommunityPostService.delete_post(db, post_id, current_user.id)
    if not success:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Post not found or not authorized")
    return {"message": "Post deleted successfully"}


# Notification Endpoints
@router.get("/notifications", response_model=List[NotificationResponse])
async def get_user_notifications(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    unread_only: bool = Query(False),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get user notifications.

    Returns a list of notifications for the current user.
    """
    notifications = await NotificationService.get_user_notifications(
        db, current_user.id, skip, limit, unread_only
    )
    return notifications


@router.put("/notifications/{notification_id}/read")
async def mark_notification_as_read(
    notification_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Mark a notification as read.

    Marks a specific notification as read for the current user.
    """
    success = await NotificationService.mark_as_read(db, notification_id, current_user.id)
    if not success:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Notification not found")
    return {"message": "Notification marked as read"}


@router.get("/notifications/unread-count")
async def get_unread_notifications_count(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get unread notifications count.

    Returns the count of unread notifications for the current user.
    """
    count = await NotificationService.get_unread_count(db, current_user.id)
    return {"unread_count": count}


# Post Comment Endpoints
@router.post("/posts/{post_id}/comments", response_model=PostCommentResponse)
async def create_post_comment(
    post_id: UUID,
    comment_data: PostCommentCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create a comment on a post.

    Creates a new comment on the specified post.
    """
    # Ensure the post_id in the URL matches the comment data
    comment_data.post_id = post_id
    comment = await PostCommentService.create_comment(db, comment_data, current_user.id)
    return comment


@router.get("/posts/{post_id}/comments", response_model=List[PostCommentResponse])
async def get_post_comments(
    post_id: UUID,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    parent_only: bool = Query(False),
    db: AsyncSession = Depends(get_db)
):
    """
    Get comments for a post.

    Returns comments for the specified post with optional filtering for top-level comments only.
    """
    comments = await PostCommentService.get_post_comments(db, post_id, skip, limit, parent_only)
    return comments


@router.put("/comments/{comment_id}", response_model=PostCommentResponse)
async def update_post_comment(
    comment_id: UUID,
    comment_data: PostCommentUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update a comment.

    Updates a comment. Only the comment author can update the comment.
    """
    comment = await PostCommentService.update_comment(db, comment_id, comment_data, current_user.id)
    if not comment:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Comment not found or not authorized")
    return comment


@router.delete("/comments/{comment_id}")
async def delete_post_comment(
    comment_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Delete a comment.

    Soft deletes a comment. Only the comment author can delete the comment.
    """
    success = await PostCommentService.delete_comment(db, comment_id, current_user.id)
    if not success:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Comment not found or not authorized")
    return {"message": "Comment deleted successfully"}


# Peer Review Endpoints
@router.post("/reviews", response_model=PeerReviewResponse)
async def create_peer_review(
    review_data: PeerReviewCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create a peer review.

    Creates a new peer review for content validation.
    """
    try:
        review = await PeerReviewService.create_review(db, review_data, current_user.id)
        return review
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.get("/reviews/content/{content_type}/{content_id}", response_model=List[PeerReviewResponse])
async def get_content_reviews(
    content_type: str,
    content_id: UUID,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: AsyncSession = Depends(get_db)
):
    """
    Get reviews for specific content.

    Returns peer reviews for the specified content.
    """
    reviews = await PeerReviewService.get_content_reviews(db, content_type, content_id, skip, limit)
    return reviews


@router.get("/reviews/user/{user_id}", response_model=List[PeerReviewResponse])
async def get_user_reviews(
    user_id: UUID,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: AsyncSession = Depends(get_db)
):
    """
    Get reviews by a user.

    Returns peer reviews created by the specified user.
    """
    reviews = await PeerReviewService.get_user_reviews(db, user_id, skip, limit)
    return reviews


@router.put("/reviews/{review_id}", response_model=PeerReviewResponse)
async def update_peer_review(
    review_id: UUID,
    review_data: PeerReviewUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update a peer review.

    Updates a peer review. Only the review author can update the review.
    """
    review = await PeerReviewService.update_review(db, review_id, review_data, current_user.id)
    if not review:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Review not found or not authorized")
    return review
