"""
Research tools endpoints for the supplement tracking platform.

This module provides endpoints for research protocol management,
literature references, statistical analysis, and participant management.
"""

from typing import List, Optional, Dict, Any
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.modules.user_management.dependencies import get_current_user
from app.modules.user_management.models import User
from app.modules.research_tools.schemas import (
    ResearchProtocolCreate, ResearchProtocolListResponse, ResearchProtocolResponse, ResearchProtocolUpdate,
    LiteratureReferenceCreate, LiteratureReferenceListResponse, LiteratureReferenceResponse, LiteratureReferenceUpdate,
    StudyParticipantCreate, StudyParticipantListResponse, StudyParticipantResponse, StudyParticipantUpdate,
    StatisticalAnalysisCreate, StatisticalAnalysisListResponse, StatisticalAnalysisResponse, StatisticalAnalysisUpdate,
    ResearchCollaborationCreate, ResearchCollaborationListResponse, ResearchCollaborationResponse, ResearchCollaborationUpdate
)
from app.modules.research_tools.services import (
    ResearchProtocolService, LiteratureReferenceService, StudyParticipantService,
    StatisticalAnalysisService, ResearchCollaborationService
)

router = APIRouter()


# Research Protocol Endpoints
@router.post("/protocols", response_model=ResearchProtocolResponse)
async def create_research_protocol(
    protocol_data: ResearchProtocolCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new research protocol.

    Creates a new research protocol for experimental design and management.
    """
    protocol = await ResearchProtocolService.create_protocol(db, protocol_data, current_user.id)
    return protocol


@router.get("/protocols", response_model=List[ResearchProtocolResponse])
async def list_research_protocols(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None),
    creator_id: Optional[UUID] = Query(None),
    search: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db)
):
    """
    List research protocols.

    Returns research protocols with optional filtering by status, creator, or search terms.
    """
    protocols = await ResearchProtocolService.get_protocols(
        db, skip, limit, status, creator_id, search
    )
    return protocols


@router.get("/protocols/{protocol_id}", response_model=ResearchProtocolResponse)
async def get_research_protocol(
    protocol_id: UUID,
    db: AsyncSession = Depends(get_db)
):
    """
    Get a specific research protocol.

    Returns details of a specific research protocol including participants and analyses.
    """
    protocol = await ResearchProtocolService.get_protocol(db, protocol_id)
    if not protocol:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Protocol not found")
    return protocol


@router.put("/protocols/{protocol_id}", response_model=ResearchProtocolResponse)
async def update_research_protocol(
    protocol_id: UUID,
    protocol_data: ResearchProtocolUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update a research protocol.

    Updates a research protocol. Only the protocol creator can update the protocol.
    """
    protocol = await ResearchProtocolService.update_protocol(db, protocol_id, protocol_data, current_user.id)
    if not protocol:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Protocol not found or not authorized")
    return protocol


@router.delete("/protocols/{protocol_id}")
async def delete_research_protocol(
    protocol_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Delete a research protocol.

    Deletes a research protocol. Only the protocol creator can delete the protocol.
    """
    success = await ResearchProtocolService.delete_protocol(db, protocol_id, current_user.id)
    if not success:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Protocol not found or not authorized")
    return {"message": "Protocol deleted successfully"}


@router.get("/protocols/{protocol_id}/statistics")
async def get_protocol_statistics(
    protocol_id: UUID,
    db: AsyncSession = Depends(get_db)
):
    """
    Get statistics for a research protocol.

    Returns participant counts, analysis counts, and other protocol statistics.
    """
    stats = await ResearchProtocolService.get_protocol_statistics(db, protocol_id)
    return stats


# Literature Reference Endpoints
@router.post("/literature", response_model=LiteratureReferenceResponse)
async def create_literature_reference(
    reference_data: LiteratureReferenceCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new literature reference.

    Adds a new scientific literature reference to the database.
    """
    reference = await LiteratureReferenceService.create_reference(db, reference_data, current_user.id)
    return reference


@router.get("/literature", response_model=List[LiteratureReferenceResponse])
async def list_literature_references(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None),
    journal: Optional[str] = Query(None),
    study_type: Optional[str] = Query(None),
    min_quality_score: Optional[float] = Query(None, ge=0, le=10),
    db: AsyncSession = Depends(get_db)
):
    """
    List literature references.

    Returns literature references with optional filtering and search capabilities.
    """
    references = await LiteratureReferenceService.get_references(
        db, skip, limit, search, journal, study_type, min_quality_score
    )
    return references


@router.get("/literature/{reference_id}", response_model=LiteratureReferenceResponse)
async def get_literature_reference(
    reference_id: UUID,
    db: AsyncSession = Depends(get_db)
):
    """
    Get a specific literature reference.

    Returns details of a specific literature reference.
    """
    reference = await LiteratureReferenceService.get_reference(db, reference_id)
    if not reference:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Reference not found")
    return reference


@router.put("/literature/{reference_id}", response_model=LiteratureReferenceResponse)
async def update_literature_reference(
    reference_id: UUID,
    reference_data: LiteratureReferenceUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update a literature reference.

    Updates a literature reference. Only the user who added it can update it.
    """
    reference = await LiteratureReferenceService.update_reference(db, reference_id, reference_data, current_user.id)
    if not reference:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Reference not found or not authorized")
    return reference


@router.delete("/literature/{reference_id}")
async def delete_literature_reference(
    reference_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Delete a literature reference.

    Deletes a literature reference. Only the user who added it can delete it.
    """
    success = await LiteratureReferenceService.delete_reference(db, reference_id, current_user.id)
    if not success:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Reference not found or not authorized")
    return {"message": "Reference deleted successfully"}


@router.get("/literature/search/doi/{doi}", response_model=LiteratureReferenceResponse)
async def search_literature_by_doi(
    doi: str,
    db: AsyncSession = Depends(get_db)
):
    """
    Search literature by DOI.

    Finds a literature reference by its Digital Object Identifier.
    """
    reference = await LiteratureReferenceService.search_by_doi(db, doi)
    if not reference:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Reference not found")
    return reference


@router.get("/literature/search/pmid/{pmid}", response_model=LiteratureReferenceResponse)
async def search_literature_by_pmid(
    pmid: str,
    db: AsyncSession = Depends(get_db)
):
    """
    Search literature by PubMed ID.

    Finds a literature reference by its PubMed identifier.
    """
    reference = await LiteratureReferenceService.search_by_pmid(db, pmid)
    if not reference:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Reference not found")
    return reference


# Study Participant Endpoints
@router.post("/protocols/{protocol_id}/participants", response_model=StudyParticipantResponse)
async def create_study_participant(
    protocol_id: UUID,
    participant_data: StudyParticipantCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new study participant.

    Adds a new participant to a research protocol.
    """
    # Ensure the protocol_id in the URL matches the participant data
    participant_data.protocol_id = protocol_id

    # Generate participant code if not provided
    if not participant_data.participant_code:
        participant_data.participant_code = await StudyParticipantService.generate_participant_code(db, protocol_id)

    participant = await StudyParticipantService.create_participant(db, participant_data, current_user.id)
    return participant


@router.get("/protocols/{protocol_id}/participants", response_model=List[StudyParticipantResponse])
async def list_protocol_participants(
    protocol_id: UUID,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db)
):
    """
    List participants for a protocol.

    Returns participants for a specific research protocol with optional status filtering.
    """
    participants = await StudyParticipantService.get_protocol_participants(
        db, protocol_id, skip, limit, status
    )
    return participants


@router.get("/participants/{participant_id}", response_model=StudyParticipantResponse)
async def get_study_participant(
    participant_id: UUID,
    db: AsyncSession = Depends(get_db)
):
    """
    Get a specific study participant.

    Returns details of a specific study participant.
    """
    participant = await StudyParticipantService.get_participant(db, participant_id)
    if not participant:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Participant not found")
    return participant


@router.put("/participants/{participant_id}", response_model=StudyParticipantResponse)
async def update_study_participant(
    participant_id: UUID,
    participant_data: StudyParticipantUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update a study participant.

    Updates participant information and status.
    """
    participant = await StudyParticipantService.update_participant(db, participant_id, participant_data)
    if not participant:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Participant not found")
    return participant


# Statistical Analysis Endpoints
@router.post("/analyses", response_model=StatisticalAnalysisResponse)
async def create_statistical_analysis(
    analysis_data: StatisticalAnalysisCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new statistical analysis.

    Creates a new statistical analysis for research data.
    """
    analysis = await StatisticalAnalysisService.create_analysis(db, analysis_data, current_user.id)
    return analysis


@router.get("/analyses", response_model=List[StatisticalAnalysisResponse])
async def list_statistical_analyses(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    protocol_id: Optional[UUID] = Query(None),
    analysis_type: Optional[str] = Query(None),
    analyst_id: Optional[UUID] = Query(None),
    db: AsyncSession = Depends(get_db)
):
    """
    List statistical analyses.

    Returns statistical analyses with optional filtering.
    """
    analyses = await StatisticalAnalysisService.get_analyses(
        db, skip, limit, protocol_id, analysis_type, analyst_id
    )
    return analyses


@router.get("/analyses/{analysis_id}", response_model=StatisticalAnalysisResponse)
async def get_statistical_analysis(
    analysis_id: UUID,
    db: AsyncSession = Depends(get_db)
):
    """
    Get a specific statistical analysis.

    Returns details of a specific statistical analysis.
    """
    analysis = await StatisticalAnalysisService.get_analysis(db, analysis_id)
    if not analysis:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Analysis not found")
    return analysis


@router.put("/analyses/{analysis_id}", response_model=StatisticalAnalysisResponse)
async def update_statistical_analysis(
    analysis_id: UUID,
    analysis_data: StatisticalAnalysisUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update a statistical analysis.

    Updates a statistical analysis. Only the analyst can update it.
    """
    analysis = await StatisticalAnalysisService.update_analysis(db, analysis_id, analysis_data, current_user.id)
    if not analysis:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Analysis not found or not authorized")
    return analysis


@router.delete("/analyses/{analysis_id}")
async def delete_statistical_analysis(
    analysis_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Delete a statistical analysis.

    Deletes a statistical analysis. Only the analyst can delete it.
    """
    success = await StatisticalAnalysisService.delete_analysis(db, analysis_id, current_user.id)
    if not success:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Analysis not found or not authorized")
    return {"message": "Analysis deleted successfully"}


# Research Collaboration Endpoints
@router.post("/collaborations", response_model=ResearchCollaborationResponse)
async def create_research_collaboration(
    collaboration_data: ResearchCollaborationCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new research collaboration.

    Invites a user to collaborate on a research protocol.
    """
    collaboration = await ResearchCollaborationService.create_collaboration(db, collaboration_data, current_user.id)
    return collaboration


@router.get("/protocols/{protocol_id}/collaborations", response_model=List[ResearchCollaborationResponse])
async def list_protocol_collaborations(
    protocol_id: UUID,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db)
):
    """
    List collaborations for a protocol.

    Returns collaborations for a specific research protocol.
    """
    collaborations = await ResearchCollaborationService.get_protocol_collaborations(
        db, protocol_id, skip, limit, status
    )
    return collaborations


@router.get("/users/{user_id}/collaborations", response_model=List[ResearchCollaborationResponse])
async def list_user_collaborations(
    user_id: UUID,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db)
):
    """
    List collaborations for a user.

    Returns collaborations for a specific user.
    """
    collaborations = await ResearchCollaborationService.get_user_collaborations(
        db, user_id, skip, limit, status
    )
    return collaborations


@router.put("/collaborations/{collaboration_id}", response_model=ResearchCollaborationResponse)
async def update_research_collaboration(
    collaboration_id: UUID,
    collaboration_data: ResearchCollaborationUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update a research collaboration.

    Updates collaboration details. Only the collaborator or inviter can update.
    """
    collaboration = await ResearchCollaborationService.update_collaboration(
        db, collaboration_id, collaboration_data, current_user.id
    )
    if not collaboration:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Collaboration not found or not authorized")
    return collaboration


@router.post("/collaborations/{collaboration_id}/accept", response_model=ResearchCollaborationResponse)
async def accept_research_collaboration(
    collaboration_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Accept a collaboration invitation.

    Accepts an invitation to collaborate on a research protocol.
    """
    collaboration = await ResearchCollaborationService.accept_collaboration(db, collaboration_id, current_user.id)
    if not collaboration:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Collaboration invitation not found")
    return collaboration


@router.post("/collaborations/{collaboration_id}/decline")
async def decline_research_collaboration(
    collaboration_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Decline a collaboration invitation.

    Declines an invitation to collaborate on a research protocol.
    """
    success = await ResearchCollaborationService.decline_collaboration(db, collaboration_id, current_user.id)
    if not success:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Collaboration invitation not found")
    return {"message": "Collaboration invitation declined"}
