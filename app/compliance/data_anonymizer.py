"""
Data Anonymization System for Privacy Compliance

This module provides tools for anonymizing and pseudonymizing user data
to protect privacy while enabling research and analytics.
"""

import hashlib
import secrets
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum

import pandas as pd
from sqlalchemy.orm import Session


class AnonymizationLevel(str, Enum):
    """Levels of data anonymization."""
    NONE = "none"                    # No anonymization
    PSEUDONYMIZATION = "pseudo"      # Replace identifiers with pseudonyms
    ANONYMIZATION = "anonymous"      # Remove all identifying information
    K_ANONYMITY = "k_anonymity"     # Ensure k-anonymity
    DIFFERENTIAL_PRIVACY = "diff_privacy"  # Add statistical noise


class DataType(str, Enum):
    """Types of data that can be anonymized."""
    PERSONAL_IDENTIFIER = "personal_id"
    HEALTH_DATA = "health_data"
    RESEARCH_DATA = "research_data"
    BEHAVIORAL_DATA = "behavioral_data"
    DEMOGRAPHIC_DATA = "demographic_data"


@dataclass
class AnonymizationConfig:
    """Configuration for anonymization operations."""
    level: AnonymizationLevel
    preserve_relationships: bool = True
    k_value: int = 5  # For k-anonymity
    epsilon: float = 1.0  # For differential privacy
    delta: float = 1e-5  # For differential privacy
    date_precision: str = "month"  # year, month, day
    location_precision: str = "state"  # country, state, city, zip
    age_binning: int = 5  # Age bins (e.g., 5-year groups)


class DataAnonymizer:
    """Handles data anonymization and pseudonymization."""
    
    def __init__(self, db_session: Session):
        self.db = db_session
        self._pseudonym_cache = {}
        self._salt = self._get_or_create_salt()
    
    def anonymize_user_data(
        self,
        user_data: Dict[str, Any],
        config: AnonymizationConfig
    ) -> Dict[str, Any]:
        """
        Anonymize user data according to configuration.
        
        Args:
            user_data: Dictionary containing user data
            config: Anonymization configuration
            
        Returns:
            Dict: Anonymized user data
        """
        anonymized_data = user_data.copy()
        
        if config.level == AnonymizationLevel.NONE:
            return anonymized_data
        
        # Remove direct identifiers
        anonymized_data = self._remove_direct_identifiers(anonymized_data)
        
        if config.level == AnonymizationLevel.PSEUDONYMIZATION:
            anonymized_data = self._pseudonymize_data(anonymized_data, config)
        
        elif config.level == AnonymizationLevel.ANONYMIZATION:
            anonymized_data = self._anonymize_data(anonymized_data, config)
        
        elif config.level == AnonymizationLevel.K_ANONYMITY:
            # K-anonymity requires dataset-level processing
            raise ValueError("K-anonymity requires dataset-level processing")
        
        elif config.level == AnonymizationLevel.DIFFERENTIAL_PRIVACY:
            anonymized_data = self._add_differential_privacy_noise(anonymized_data, config)
        
        return anonymized_data
    
    def anonymize_dataset(
        self,
        dataset: pd.DataFrame,
        config: AnonymizationConfig
    ) -> pd.DataFrame:
        """
        Anonymize an entire dataset.
        
        Args:
            dataset: Pandas DataFrame with user data
            config: Anonymization configuration
            
        Returns:
            pd.DataFrame: Anonymized dataset
        """
        anonymized_df = dataset.copy()
        
        if config.level == AnonymizationLevel.NONE:
            return anonymized_df
        
        # Remove direct identifiers
        anonymized_df = self._remove_dataset_identifiers(anonymized_df)
        
        if config.level == AnonymizationLevel.PSEUDONYMIZATION:
            anonymized_df = self._pseudonymize_dataset(anonymized_df, config)
        
        elif config.level == AnonymizationLevel.ANONYMIZATION:
            anonymized_df = self._anonymize_dataset(anonymized_df, config)
        
        elif config.level == AnonymizationLevel.K_ANONYMITY:
            anonymized_df = self._apply_k_anonymity(anonymized_df, config)
        
        elif config.level == AnonymizationLevel.DIFFERENTIAL_PRIVACY:
            anonymized_df = self._apply_differential_privacy(anonymized_df, config)
        
        return anonymized_df
    
    def pseudonymize_research_data(
        self,
        research_data: Dict[str, Any],
        study_id: str
    ) -> Dict[str, Any]:
        """
        Pseudonymize research data for a specific study.
        
        Args:
            research_data: Research data to pseudonymize
            study_id: Study identifier for consistent pseudonyms
            
        Returns:
            Dict: Pseudonymized research data
        """
        pseudonymized_data = research_data.copy()
        
        # Generate study-specific pseudonym
        if 'user_id' in pseudonymized_data:
            user_id = pseudonymized_data['user_id']
            pseudonym = self._generate_study_pseudonym(user_id, study_id)
            pseudonymized_data['participant_id'] = pseudonym
            del pseudonymized_data['user_id']
        
        # Remove other identifying information
        identifying_fields = [
            'email', 'name', 'username', 'phone', 'address',
            'ip_address', 'device_id', 'session_id'
        ]
        
        for field in identifying_fields:
            if field in pseudonymized_data:
                del pseudonymized_data[field]
        
        # Generalize dates
        if 'created_at' in pseudonymized_data:
            date_obj = pseudonymized_data['created_at']
            if isinstance(date_obj, datetime):
                # Round to day precision
                pseudonymized_data['created_at'] = date_obj.replace(
                    hour=0, minute=0, second=0, microsecond=0
                )
        
        return pseudonymized_data
    
    def aggregate_data(
        self,
        dataset: pd.DataFrame,
        aggregation_level: str,
        min_group_size: int = 5
    ) -> pd.DataFrame:
        """
        Create aggregated datasets for analysis.
        
        Args:
            dataset: Dataset to aggregate
            aggregation_level: Level of aggregation (daily, weekly, monthly)
            min_group_size: Minimum group size for privacy
            
        Returns:
            pd.DataFrame: Aggregated dataset
        """
        if aggregation_level == "daily":
            time_col = dataset['created_at'].dt.date
        elif aggregation_level == "weekly":
            time_col = dataset['created_at'].dt.to_period('W')
        elif aggregation_level == "monthly":
            time_col = dataset['created_at'].dt.to_period('M')
        else:
            raise ValueError(f"Unsupported aggregation level: {aggregation_level}")
        
        # Group by time period and aggregate
        aggregated = dataset.groupby(time_col).agg({
            'user_id': 'nunique',
            'supplement_name': lambda x: x.value_counts().to_dict(),
            'dosage': ['mean', 'std', 'count'],
            'effectiveness_rating': ['mean', 'std', 'count']
        }).reset_index()
        
        # Filter out groups smaller than minimum size
        aggregated = aggregated[aggregated[('user_id', 'nunique')] >= min_group_size]
        
        return aggregated
    
    def _remove_direct_identifiers(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Remove direct identifying information."""
        direct_identifiers = [
            'email', 'name', 'first_name', 'last_name', 'username',
            'phone', 'address', 'ssn', 'passport', 'license',
            'ip_address', 'device_id', 'session_id', 'user_agent'
        ]
        
        cleaned_data = data.copy()
        for identifier in direct_identifiers:
            if identifier in cleaned_data:
                del cleaned_data[identifier]
        
        return cleaned_data
    
    def _pseudonymize_data(
        self,
        data: Dict[str, Any],
        config: AnonymizationConfig
    ) -> Dict[str, Any]:
        """Apply pseudonymization to data."""
        pseudonymized_data = data.copy()
        
        # Replace user ID with pseudonym
        if 'user_id' in pseudonymized_data:
            user_id = pseudonymized_data['user_id']
            pseudonym = self._generate_pseudonym(user_id)
            pseudonymized_data['user_pseudonym'] = pseudonym
            del pseudonymized_data['user_id']
        
        # Generalize demographic data
        if 'age' in pseudonymized_data:
            age = pseudonymized_data['age']
            age_bin = (age // config.age_binning) * config.age_binning
            pseudonymized_data['age_group'] = f"{age_bin}-{age_bin + config.age_binning - 1}"
            del pseudonymized_data['age']
        
        # Generalize location data
        if 'location' in pseudonymized_data:
            location = pseudonymized_data['location']
            if config.location_precision == "state":
                # Extract state from full location
                pseudonymized_data['location'] = self._extract_state(location)
            elif config.location_precision == "country":
                pseudonymized_data['location'] = self._extract_country(location)
        
        # Generalize dates
        if 'birth_date' in pseudonymized_data:
            birth_date = pseudonymized_data['birth_date']
            if config.date_precision == "year":
                pseudonymized_data['birth_year'] = birth_date.year
            elif config.date_precision == "month":
                pseudonymized_data['birth_month'] = f"{birth_date.year}-{birth_date.month:02d}"
            del pseudonymized_data['birth_date']
        
        return pseudonymized_data
    
    def _anonymize_data(
        self,
        data: Dict[str, Any],
        config: AnonymizationConfig
    ) -> Dict[str, Any]:
        """Apply full anonymization to data."""
        anonymized_data = self._pseudonymize_data(data, config)
        
        # Remove pseudonyms if not preserving relationships
        if not config.preserve_relationships:
            if 'user_pseudonym' in anonymized_data:
                del anonymized_data['user_pseudonym']
        
        # Further generalize sensitive data
        if 'income' in anonymized_data:
            income = anonymized_data['income']
            income_bracket = self._get_income_bracket(income)
            anonymized_data['income_bracket'] = income_bracket
            del anonymized_data['income']
        
        return anonymized_data
    
    def _apply_k_anonymity(
        self,
        dataset: pd.DataFrame,
        config: AnonymizationConfig
    ) -> pd.DataFrame:
        """Apply k-anonymity to dataset."""
        # Identify quasi-identifiers
        quasi_identifiers = ['age_group', 'location', 'gender', 'income_bracket']
        
        # Group by quasi-identifiers
        grouped = dataset.groupby(quasi_identifiers)
        
        # Filter groups with less than k members
        k_anonymous_groups = []
        for name, group in grouped:
            if len(group) >= config.k_value:
                k_anonymous_groups.append(group)
            else:
                # Generalize further or suppress
                generalized_group = self._generalize_group(group, quasi_identifiers)
                k_anonymous_groups.append(generalized_group)
        
        if k_anonymous_groups:
            return pd.concat(k_anonymous_groups, ignore_index=True)
        else:
            return pd.DataFrame()
    
    def _apply_differential_privacy(
        self,
        dataset: pd.DataFrame,
        config: AnonymizationConfig
    ) -> pd.DataFrame:
        """Apply differential privacy to dataset."""
        noisy_dataset = dataset.copy()
        
        # Add noise to numerical columns
        numerical_columns = dataset.select_dtypes(include=['int64', 'float64']).columns
        
        for col in numerical_columns:
            if col in ['effectiveness_rating', 'dosage', 'age']:
                # Add Laplace noise
                sensitivity = self._calculate_sensitivity(dataset[col])
                scale = sensitivity / config.epsilon
                noise = np.random.laplace(0, scale, len(dataset))
                noisy_dataset[col] = dataset[col] + noise
                
                # Ensure values stay within reasonable bounds
                if col == 'effectiveness_rating':
                    noisy_dataset[col] = np.clip(noisy_dataset[col], 1, 10)
                elif col == 'age':
                    noisy_dataset[col] = np.clip(noisy_dataset[col], 18, 100)
        
        return noisy_dataset
    
    def _generate_pseudonym(self, user_id: int) -> str:
        """Generate consistent pseudonym for user ID."""
        if user_id in self._pseudonym_cache:
            return self._pseudonym_cache[user_id]
        
        # Create hash-based pseudonym
        hash_input = f"{user_id}{self._salt}".encode('utf-8')
        hash_digest = hashlib.sha256(hash_input).hexdigest()
        pseudonym = f"P{hash_digest[:8].upper()}"
        
        self._pseudonym_cache[user_id] = pseudonym
        return pseudonym
    
    def _generate_study_pseudonym(self, user_id: int, study_id: str) -> str:
        """Generate study-specific pseudonym."""
        hash_input = f"{user_id}{study_id}{self._salt}".encode('utf-8')
        hash_digest = hashlib.sha256(hash_input).hexdigest()
        return f"S{study_id[:4]}{hash_digest[:6].upper()}"
    
    def _get_or_create_salt(self) -> str:
        """Get or create salt for pseudonym generation."""
        # In production, this should be stored securely
        return "supplement_tracker_salt_2024"
    
    def _extract_state(self, location: str) -> str:
        """Extract state from location string."""
        # Simple implementation - in production, use proper geocoding
        if ',' in location:
            parts = location.split(',')
            if len(parts) >= 2:
                return parts[-2].strip()
        return "Unknown"
    
    def _extract_country(self, location: str) -> str:
        """Extract country from location string."""
        if ',' in location:
            parts = location.split(',')
            return parts[-1].strip()
        return location
    
    def _get_income_bracket(self, income: float) -> str:
        """Convert income to bracket."""
        if income < 25000:
            return "Under $25K"
        elif income < 50000:
            return "$25K-$50K"
        elif income < 75000:
            return "$50K-$75K"
        elif income < 100000:
            return "$75K-$100K"
        else:
            return "Over $100K"
    
    def _generalize_group(
        self,
        group: pd.DataFrame,
        quasi_identifiers: List[str]
    ) -> pd.DataFrame:
        """Generalize a group that doesn't meet k-anonymity."""
        generalized_group = group.copy()
        
        # Suppress or generalize quasi-identifiers
        for qi in quasi_identifiers:
            if qi in generalized_group.columns:
                generalized_group[qi] = "*"  # Suppression
        
        return generalized_group
    
    def _calculate_sensitivity(self, column: pd.Series) -> float:
        """Calculate sensitivity for differential privacy."""
        return column.max() - column.min()
    
    def _remove_dataset_identifiers(self, dataset: pd.DataFrame) -> pd.DataFrame:
        """Remove identifying columns from dataset."""
        identifier_columns = [
            'email', 'name', 'first_name', 'last_name', 'username',
            'phone', 'address', 'ip_address', 'device_id', 'session_id'
        ]
        
        columns_to_drop = [col for col in identifier_columns if col in dataset.columns]
        return dataset.drop(columns=columns_to_drop)
    
    def _pseudonymize_dataset(
        self,
        dataset: pd.DataFrame,
        config: AnonymizationConfig
    ) -> pd.DataFrame:
        """Apply pseudonymization to entire dataset."""
        pseudonymized_df = dataset.copy()
        
        # Replace user IDs with pseudonyms
        if 'user_id' in pseudonymized_df.columns:
            pseudonymized_df['user_pseudonym'] = pseudonymized_df['user_id'].apply(
                self._generate_pseudonym
            )
            pseudonymized_df = pseudonymized_df.drop('user_id', axis=1)
        
        return pseudonymized_df
    
    def _anonymize_dataset(
        self,
        dataset: pd.DataFrame,
        config: AnonymizationConfig
    ) -> pd.DataFrame:
        """Apply full anonymization to dataset."""
        return self._pseudonymize_dataset(dataset, config)


# Import numpy for differential privacy
try:
    import numpy as np
except ImportError:
    # Fallback if numpy not available
    class np:
        @staticmethod
        def random():
            return None
        
        @staticmethod
        def clip(arr, min_val, max_val):
            return arr
