"""
Database configuration and connection management.

This module provides SQLAlchemy async database setup with connection pooling,
following best practices for FastAPI applications and PEP 484 type hints.
"""

from typing import AsyncGenerator

from sqlalchemy import MetaData
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.pool import Null<PERSON>ool

from app.core.config import settings

# SQLAlchemy async engine with connection pooling
engine = create_async_engine(
    str(settings.SQLALCHEMY_DATABASE_URI),
    echo=settings.DEBUG,
    future=True,
    poolclass=NullPool if settings.TESTING else None,
    pool_pre_ping=True,
    pool_recycle=300,  # Recycle connections every 5 minutes
)

# Async session factory
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autoflush=False,
    autocommit=False,
)

# Base class for all database models
# Using consistent naming convention for foreign keys and indexes
metadata = MetaData(
    naming_convention={
        "ix": "ix_%(column_0_label)s",
        "uq": "uq_%(table_name)s_%(column_0_name)s",
        "ck": "ck_%(table_name)s_%(constraint_name)s",
        "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
        "pk": "pk_%(table_name)s",
    }
)

Base = declarative_base(metadata=metadata)


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency function to get database session.
    
    This function provides a database session for FastAPI dependency injection.
    The session is automatically closed after the request is completed.
    
    Yields:
        AsyncSession: Database session for the current request
        
    Example:
        >>> from fastapi import Depends
        >>> async def get_user(db: AsyncSession = Depends(get_db)):
        ...     # Use db session here
        ...     pass
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def init_db() -> None:
    """
    Initialize database tables.
    
    This function creates all database tables defined in the models.
    Should be called during application startup.
    
    Note:
        In production, use Alembic migrations instead of this function.
    """
    async with engine.begin() as conn:
        # Import all models here to ensure they are registered with SQLAlchemy
        from app.modules.user_management import models as user_models  # noqa: F401
        from app.modules.supplement_tracking import models as supplement_models  # noqa: F401
        from app.modules.community_features import models as community_models  # noqa: F401
        from app.modules.research_tools import models as research_models  # noqa: F401
        
        # Create all tables
        await conn.run_sync(Base.metadata.create_all)


async def close_db() -> None:
    """
    Close database connections.
    
    This function should be called during application shutdown to properly
    close all database connections and clean up resources.
    """
    await engine.dispose()
