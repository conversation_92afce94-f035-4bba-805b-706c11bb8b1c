"""
Community features service layer.

This module provides business logic for community features including
user following, groups, posts, comments, peer reviews, and notifications.
"""

from typing import List, Optional
from uuid import UUID

from sqlalchemy import and_, desc, func, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload

from app.modules.community_features.models import (
    CommunityGroup, CommunityPost, Notification, PeerReview, PostComment, UserFollow
)
from app.modules.community_features.schemas import (
    CommunityGroupCreate, CommunityGroupUpdate,
    CommunityPostCreate, CommunityPostUpdate,
    NotificationCreate, PeerReviewCreate, PeerReviewUpdate,
    PostCommentCreate, PostCommentUpdate, UserFollowCreate
)


class UserFollowService:
    """Service for managing user follow relationships."""

    @staticmethod
    async def follow_user(
        db: AsyncSession,
        follower_id: UUID,
        follow_data: UserFollowCreate
    ) -> UserFollow:
        """Create a new user follow relationship."""
        # Check if already following
        existing = await db.execute(
            select(UserFollow).where(
                and_(
                    UserFollow.follower_id == follower_id,
                    UserFollow.followed_id == follow_data.followed_id
                )
            )
        )
        if existing.scalar_one_or_none():
            raise ValueError("Already following this user")

        # Prevent self-following
        if follower_id == follow_data.followed_id:
            raise ValueError("Cannot follow yourself")

        follow = UserFollow(
            follower_id=follower_id,
            followed_id=follow_data.followed_id
        )
        db.add(follow)
        await db.commit()
        await db.refresh(follow)
        return follow

    @staticmethod
    async def unfollow_user(
        db: AsyncSession,
        follower_id: UUID,
        followed_id: UUID
    ) -> bool:
        """Remove a user follow relationship."""
        result = await db.execute(
            select(UserFollow).where(
                and_(
                    UserFollow.follower_id == follower_id,
                    UserFollow.followed_id == followed_id
                )
            )
        )
        follow = result.scalar_one_or_none()
        if follow:
            await db.delete(follow)
            await db.commit()
            return True
        return False

    @staticmethod
    async def get_followers(
        db: AsyncSession,
        user_id: UUID,
        skip: int = 0,
        limit: int = 20
    ) -> List[UserFollow]:
        """Get users following the specified user."""
        result = await db.execute(
            select(UserFollow)
            .where(UserFollow.followed_id == user_id)
            .order_by(desc(UserFollow.created_at))
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    @staticmethod
    async def get_following(
        db: AsyncSession,
        user_id: UUID,
        skip: int = 0,
        limit: int = 20
    ) -> List[UserFollow]:
        """Get users that the specified user is following."""
        result = await db.execute(
            select(UserFollow)
            .where(UserFollow.follower_id == user_id)
            .order_by(desc(UserFollow.created_at))
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()


class CommunityGroupService:
    """Service for managing community groups."""

    @staticmethod
    async def create_group(
        db: AsyncSession,
        group_data: CommunityGroupCreate,
        creator_id: UUID
    ) -> CommunityGroup:
        """Create a new community group."""
        group = CommunityGroup(
            **group_data.model_dump(),
            created_by_user_id=creator_id
        )
        db.add(group)
        await db.commit()
        await db.refresh(group)
        return group

    @staticmethod
    async def get_group(db: AsyncSession, group_id: UUID) -> Optional[CommunityGroup]:
        """Get a community group by ID."""
        result = await db.execute(
            select(CommunityGroup).where(CommunityGroup.id == group_id)
        )
        return result.scalar_one_or_none()

    @staticmethod
    async def get_groups(
        db: AsyncSession,
        skip: int = 0,
        limit: int = 20,
        category: Optional[str] = None,
        public_only: bool = True
    ) -> List[CommunityGroup]:
        """Get community groups with optional filtering."""
        query = select(CommunityGroup)
        
        if public_only:
            query = query.where(CommunityGroup.is_public == True)
        
        if category:
            query = query.where(CommunityGroup.category == category)
        
        query = query.order_by(desc(CommunityGroup.member_count), desc(CommunityGroup.created_at))
        query = query.offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def update_group(
        db: AsyncSession,
        group_id: UUID,
        group_data: CommunityGroupUpdate,
        user_id: UUID
    ) -> Optional[CommunityGroup]:
        """Update a community group."""
        result = await db.execute(
            select(CommunityGroup).where(
                and_(
                    CommunityGroup.id == group_id,
                    CommunityGroup.created_by_user_id == user_id
                )
            )
        )
        group = result.scalar_one_or_none()
        if not group:
            return None

        update_data = group_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(group, field, value)

        await db.commit()
        await db.refresh(group)
        return group

    @staticmethod
    async def delete_group(
        db: AsyncSession,
        group_id: UUID,
        user_id: UUID
    ) -> bool:
        """Delete a community group."""
        result = await db.execute(
            select(CommunityGroup).where(
                and_(
                    CommunityGroup.id == group_id,
                    CommunityGroup.created_by_user_id == user_id
                )
            )
        )
        group = result.scalar_one_or_none()
        if group:
            await db.delete(group)
            await db.commit()
            return True
        return False


class CommunityPostService:
    """Service for managing community posts."""

    @staticmethod
    async def create_post(
        db: AsyncSession,
        post_data: CommunityPostCreate,
        author_id: UUID
    ) -> CommunityPost:
        """Create a new community post."""
        post = CommunityPost(
            **post_data.model_dump(),
            author_id=author_id
        )
        db.add(post)
        await db.commit()
        await db.refresh(post)
        return post

    @staticmethod
    async def get_post(db: AsyncSession, post_id: UUID) -> Optional[CommunityPost]:
        """Get a community post by ID."""
        result = await db.execute(
            select(CommunityPost)
            .options(selectinload(CommunityPost.group))
            .where(CommunityPost.id == post_id)
        )
        return result.scalar_one_or_none()

    @staticmethod
    async def get_posts(
        db: AsyncSession,
        skip: int = 0,
        limit: int = 20,
        group_id: Optional[UUID] = None,
        post_type: Optional[str] = None,
        author_id: Optional[UUID] = None
    ) -> List[CommunityPost]:
        """Get community posts with optional filtering."""
        query = select(CommunityPost).options(selectinload(CommunityPost.group))
        
        if group_id:
            query = query.where(CommunityPost.group_id == group_id)
        
        if post_type:
            query = query.where(CommunityPost.post_type == post_type)
        
        if author_id:
            query = query.where(CommunityPost.author_id == author_id)
        
        # Order by pinned posts first, then by creation date
        query = query.order_by(desc(CommunityPost.is_pinned), desc(CommunityPost.created_at))
        query = query.offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def update_post(
        db: AsyncSession,
        post_id: UUID,
        post_data: CommunityPostUpdate,
        user_id: UUID
    ) -> Optional[CommunityPost]:
        """Update a community post."""
        result = await db.execute(
            select(CommunityPost).where(
                and_(
                    CommunityPost.id == post_id,
                    CommunityPost.author_id == user_id
                )
            )
        )
        post = result.scalar_one_or_none()
        if not post:
            return None

        update_data = post_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(post, field, value)

        await db.commit()
        await db.refresh(post)
        return post

    @staticmethod
    async def delete_post(
        db: AsyncSession,
        post_id: UUID,
        user_id: UUID
    ) -> bool:
        """Delete a community post."""
        result = await db.execute(
            select(CommunityPost).where(
                and_(
                    CommunityPost.id == post_id,
                    CommunityPost.author_id == user_id
                )
            )
        )
        post = result.scalar_one_or_none()
        if post:
            await db.delete(post)
            await db.commit()
            return True
        return False

    @staticmethod
    async def increment_view_count(db: AsyncSession, post_id: UUID) -> None:
        """Increment the view count for a post."""
        await db.execute(
            CommunityPost.__table__.update()
            .where(CommunityPost.id == post_id)
            .values(view_count=CommunityPost.view_count + 1)
        )
        await db.commit()


class NotificationService:
    """Service for managing user notifications."""

    @staticmethod
    async def create_notification(
        db: AsyncSession,
        notification_data: NotificationCreate
    ) -> Notification:
        """Create a new notification."""
        notification = Notification(**notification_data.model_dump())
        db.add(notification)
        await db.commit()
        await db.refresh(notification)
        return notification

    @staticmethod
    async def get_user_notifications(
        db: AsyncSession,
        user_id: UUID,
        skip: int = 0,
        limit: int = 20,
        unread_only: bool = False
    ) -> List[Notification]:
        """Get notifications for a user."""
        query = select(Notification).where(Notification.user_id == user_id)
        
        if unread_only:
            query = query.where(Notification.is_read == False)
        
        query = query.order_by(desc(Notification.created_at))
        query = query.offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def mark_as_read(
        db: AsyncSession,
        notification_id: UUID,
        user_id: UUID
    ) -> bool:
        """Mark a notification as read."""
        result = await db.execute(
            select(Notification).where(
                and_(
                    Notification.id == notification_id,
                    Notification.user_id == user_id
                )
            )
        )
        notification = result.scalar_one_or_none()
        if notification:
            notification.is_read = True
            await db.commit()
            return True
        return False

    @staticmethod
    async def get_unread_count(db: AsyncSession, user_id: UUID) -> int:
        """Get count of unread notifications for a user."""
        result = await db.execute(
            select(func.count(Notification.id))
            .where(
                and_(
                    Notification.user_id == user_id,
                    Notification.is_read == False
                )
            )
        )
        return result.scalar() or 0


class PostCommentService:
    """Service for managing post comments."""

    @staticmethod
    async def create_comment(
        db: AsyncSession,
        comment_data: PostCommentCreate,
        author_id: UUID
    ) -> PostComment:
        """Create a new post comment."""
        comment = PostComment(
            **comment_data.model_dump(),
            author_id=author_id
        )
        db.add(comment)

        # Update comment count on the post
        await db.execute(
            CommunityPost.__table__.update()
            .where(CommunityPost.id == comment_data.post_id)
            .values(comment_count=CommunityPost.comment_count + 1)
        )

        await db.commit()
        await db.refresh(comment)
        return comment

    @staticmethod
    async def get_comment(db: AsyncSession, comment_id: UUID) -> Optional[PostComment]:
        """Get a post comment by ID."""
        result = await db.execute(
            select(PostComment).where(PostComment.id == comment_id)
        )
        return result.scalar_one_or_none()

    @staticmethod
    async def get_post_comments(
        db: AsyncSession,
        post_id: UUID,
        skip: int = 0,
        limit: int = 20,
        parent_only: bool = False
    ) -> List[PostComment]:
        """Get comments for a post."""
        query = select(PostComment).where(
            and_(
                PostComment.post_id == post_id,
                PostComment.is_deleted == False
            )
        )

        if parent_only:
            query = query.where(PostComment.parent_comment_id.is_(None))

        query = query.order_by(PostComment.created_at)
        query = query.offset(skip).limit(limit)

        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def update_comment(
        db: AsyncSession,
        comment_id: UUID,
        comment_data: PostCommentUpdate,
        user_id: UUID
    ) -> Optional[PostComment]:
        """Update a post comment."""
        result = await db.execute(
            select(PostComment).where(
                and_(
                    PostComment.id == comment_id,
                    PostComment.author_id == user_id,
                    PostComment.is_deleted == False
                )
            )
        )
        comment = result.scalar_one_or_none()
        if not comment:
            return None

        update_data = comment_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(comment, field, value)

        await db.commit()
        await db.refresh(comment)
        return comment

    @staticmethod
    async def delete_comment(
        db: AsyncSession,
        comment_id: UUID,
        user_id: UUID
    ) -> bool:
        """Soft delete a post comment."""
        result = await db.execute(
            select(PostComment).where(
                and_(
                    PostComment.id == comment_id,
                    PostComment.author_id == user_id,
                    PostComment.is_deleted == False
                )
            )
        )
        comment = result.scalar_one_or_none()
        if comment:
            comment.is_deleted = True

            # Update comment count on the post
            await db.execute(
                CommunityPost.__table__.update()
                .where(CommunityPost.id == comment.post_id)
                .values(comment_count=CommunityPost.comment_count - 1)
            )

            await db.commit()
            return True
        return False


class PeerReviewService:
    """Service for managing peer reviews."""

    @staticmethod
    async def create_review(
        db: AsyncSession,
        review_data: PeerReviewCreate,
        reviewer_id: UUID
    ) -> PeerReview:
        """Create a new peer review."""
        # Check if user already reviewed this content
        existing = await db.execute(
            select(PeerReview).where(
                and_(
                    PeerReview.content_type == review_data.content_type,
                    PeerReview.content_id == review_data.content_id,
                    PeerReview.reviewer_id == reviewer_id
                )
            )
        )
        if existing.scalar_one_or_none():
            raise ValueError("You have already reviewed this content")

        review = PeerReview(
            **review_data.model_dump(),
            reviewer_id=reviewer_id
        )
        db.add(review)
        await db.commit()
        await db.refresh(review)
        return review

    @staticmethod
    async def get_review(db: AsyncSession, review_id: UUID) -> Optional[PeerReview]:
        """Get a peer review by ID."""
        result = await db.execute(
            select(PeerReview).where(PeerReview.id == review_id)
        )
        return result.scalar_one_or_none()

    @staticmethod
    async def get_content_reviews(
        db: AsyncSession,
        content_type: str,
        content_id: UUID,
        skip: int = 0,
        limit: int = 20
    ) -> List[PeerReview]:
        """Get reviews for specific content."""
        result = await db.execute(
            select(PeerReview)
            .where(
                and_(
                    PeerReview.content_type == content_type,
                    PeerReview.content_id == content_id
                )
            )
            .order_by(desc(PeerReview.created_at))
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    @staticmethod
    async def get_user_reviews(
        db: AsyncSession,
        reviewer_id: UUID,
        skip: int = 0,
        limit: int = 20
    ) -> List[PeerReview]:
        """Get reviews by a specific user."""
        result = await db.execute(
            select(PeerReview)
            .where(PeerReview.reviewer_id == reviewer_id)
            .order_by(desc(PeerReview.created_at))
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    @staticmethod
    async def update_review(
        db: AsyncSession,
        review_id: UUID,
        review_data: PeerReviewUpdate,
        user_id: UUID
    ) -> Optional[PeerReview]:
        """Update a peer review."""
        result = await db.execute(
            select(PeerReview).where(
                and_(
                    PeerReview.id == review_id,
                    PeerReview.reviewer_id == user_id
                )
            )
        )
        review = result.scalar_one_or_none()
        if not review:
            return None

        update_data = review_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(review, field, value)

        await db.commit()
        await db.refresh(review)
        return review
