"""
Community features Pydantic schemas.

This module defines Pydantic models for request/response validation
and serialization for community features.
"""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, Field


# User Follow Schemas
class UserFollowBase(BaseModel):
    """Base schema for user follow relationships."""
    pass


class UserFollowCreate(UserFollowBase):
    """Schema for creating a user follow relationship."""
    followed_id: UUID = Field(..., description="ID of the user to follow")


class UserFollowResponse(UserFollowBase):
    """Schema for user follow relationship responses."""
    id: UUID
    follower_id: UUID
    followed_id: UUID
    created_at: datetime

    model_config = {"from_attributes": True}


# Community Group Schemas
class CommunityGroupBase(BaseModel):
    """Base schema for community groups."""
    name: str = Field(..., min_length=1, max_length=255, description="Group name")
    description: Optional[str] = Field(None, description="Group description")
    category: str = Field(..., min_length=1, max_length=50, description="Group category")
    is_public: bool = Field(True, description="Whether the group is publicly visible")
    is_moderated: bool = Field(False, description="Whether posts require moderation")


class CommunityGroupCreate(CommunityGroupBase):
    """Schema for creating a community group."""
    pass


class CommunityGroupUpdate(BaseModel):
    """Schema for updating a community group."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    category: Optional[str] = Field(None, min_length=1, max_length=50)
    is_public: Optional[bool] = None
    is_moderated: Optional[bool] = None


class CommunityGroupResponse(CommunityGroupBase):
    """Schema for community group responses."""
    id: UUID
    member_count: int
    created_by_user_id: UUID
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


class CommunityGroupListResponse(BaseModel):
    """Schema for paginated community group list responses."""
    groups: List[CommunityGroupResponse]
    total: int
    page: int
    size: int
    pages: int


# Community Post Schemas
class CommunityPostBase(BaseModel):
    """Base schema for community posts."""
    title: str = Field(..., min_length=1, max_length=500, description="Post title")
    content: str = Field(..., min_length=1, description="Post content")
    post_type: str = Field("discussion", max_length=50, description="Type of post")
    group_id: Optional[UUID] = Field(None, description="Optional group ID")


class CommunityPostCreate(CommunityPostBase):
    """Schema for creating a community post."""
    pass


class CommunityPostUpdate(BaseModel):
    """Schema for updating a community post."""
    title: Optional[str] = Field(None, min_length=1, max_length=500)
    content: Optional[str] = Field(None, min_length=1)
    post_type: Optional[str] = Field(None, max_length=50)


class CommunityPostResponse(CommunityPostBase):
    """Schema for community post responses."""
    id: UUID
    author_id: UUID
    is_pinned: bool
    is_locked: bool
    view_count: int
    like_count: int
    comment_count: int
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


class CommunityPostListResponse(BaseModel):
    """Schema for paginated community post list responses."""
    posts: List[CommunityPostResponse]
    total: int
    page: int
    size: int
    pages: int


# Post Comment Schemas
class PostCommentBase(BaseModel):
    """Base schema for post comments."""
    content: str = Field(..., min_length=1, description="Comment content")
    parent_comment_id: Optional[UUID] = Field(None, description="Parent comment for threading")


class PostCommentCreate(PostCommentBase):
    """Schema for creating a post comment."""
    post_id: UUID = Field(..., description="ID of the post being commented on")


class PostCommentUpdate(BaseModel):
    """Schema for updating a post comment."""
    content: Optional[str] = Field(None, min_length=1)


class PostCommentResponse(PostCommentBase):
    """Schema for post comment responses."""
    id: UUID
    post_id: UUID
    author_id: UUID
    like_count: int
    is_deleted: bool
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


class PostCommentListResponse(BaseModel):
    """Schema for paginated post comment list responses."""
    comments: List[PostCommentResponse]
    total: int
    page: int
    size: int
    pages: int


# Peer Review Schemas
class PeerReviewBase(BaseModel):
    """Base schema for peer reviews."""
    content_type: str = Field(..., max_length=50, description="Type of content being reviewed")
    content_id: UUID = Field(..., description="ID of content being reviewed")
    score: Optional[int] = Field(None, ge=1, le=10, description="Review score (1-10)")
    feedback: Optional[str] = Field(None, description="Review feedback")
    expertise_level: str = Field("general", max_length=20, description="Reviewer expertise level")


class PeerReviewCreate(PeerReviewBase):
    """Schema for creating a peer review."""
    pass


class PeerReviewUpdate(BaseModel):
    """Schema for updating a peer review."""
    status: Optional[str] = Field(None, max_length=20)
    score: Optional[int] = Field(None, ge=1, le=10)
    feedback: Optional[str] = None
    expertise_level: Optional[str] = Field(None, max_length=20)


class PeerReviewResponse(PeerReviewBase):
    """Schema for peer review responses."""
    id: UUID
    reviewer_id: UUID
    status: str
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


class PeerReviewListResponse(BaseModel):
    """Schema for paginated peer review list responses."""
    reviews: List[PeerReviewResponse]
    total: int
    page: int
    size: int
    pages: int


# Notification Schemas
class NotificationBase(BaseModel):
    """Base schema for notifications."""
    type: str = Field(..., max_length=50, description="Notification type")
    title: str = Field(..., max_length=255, description="Notification title")
    message: str = Field(..., description="Notification message")
    related_id: Optional[UUID] = Field(None, description="Related content ID")
    related_type: Optional[str] = Field(None, max_length=50, description="Related content type")


class NotificationCreate(NotificationBase):
    """Schema for creating a notification."""
    user_id: UUID = Field(..., description="User receiving the notification")


class NotificationUpdate(BaseModel):
    """Schema for updating a notification."""
    is_read: Optional[bool] = None
    is_sent: Optional[bool] = None


class NotificationResponse(NotificationBase):
    """Schema for notification responses."""
    id: UUID
    user_id: UUID
    is_read: bool
    is_sent: bool
    created_at: datetime

    model_config = {"from_attributes": True}


class NotificationListResponse(BaseModel):
    """Schema for paginated notification list responses."""
    notifications: List[NotificationResponse]
    total: int
    page: int
    size: int
    pages: int
    unread_count: int
