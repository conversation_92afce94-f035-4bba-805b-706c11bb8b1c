"""
Research tools database models.

This module defines SQLAlchemy models for research protocol design,
literature management, statistical analysis, and participant management.
"""

from datetime import datetime
from typing import Optional
from uuid import uuid4

from sqlalchemy import (
    Boolean, Column, DateTime, Foreign<PERSON>ey, Integer, String, Text, 
    Numeric, JSON, UniqueConstraint
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base


class ResearchProtocol(Base):
    """
    Research protocol model for experimental design and management.
    
    This model represents structured research protocols for supplement studies,
    including methodology, variables, and analysis plans.
    
    Attributes:
        id: Unique protocol identifier
        title: Protocol title
        description: Detailed protocol description
        hypothesis: Research hypothesis
        methodology: Experimental methodology
        variables: JSON structure of variables and measurements
        duration_days: Study duration in days
        sample_size_target: Target number of participants
        sample_size_actual: Actual enrolled participants
        status: Protocol status (draft, active, completed, cancelled)
        ethics_approval: Ethics approval status
        created_by_user_id: User who created the protocol
        created_at: Protocol creation timestamp
        updated_at: Last update timestamp
        started_at: Study start date
        completed_at: Study completion date
    """
    
    __tablename__ = "research_protocols"
    
    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        index=True,
        doc="Unique protocol identifier"
    )
    
    title = Column(
        String(500),
        nullable=False,
        index=True,
        doc="Protocol title"
    )
    
    description = Column(
        Text,
        nullable=False,
        doc="Detailed protocol description"
    )
    
    hypothesis = Column(
        Text,
        nullable=True,
        doc="Research hypothesis"
    )
    
    methodology = Column(
        Text,
        nullable=False,
        doc="Experimental methodology"
    )
    
    variables = Column(
        JSON,
        nullable=True,
        doc="JSON structure of variables and measurements"
    )
    
    duration_days = Column(
        Integer,
        nullable=False,
        doc="Study duration in days"
    )
    
    sample_size_target = Column(
        Integer,
        nullable=False,
        doc="Target number of participants"
    )
    
    sample_size_actual = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Actual enrolled participants"
    )
    
    status = Column(
        String(20),
        nullable=False,
        default="draft",
        index=True,
        doc="Protocol status"
    )
    
    ethics_approval = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Ethics approval status"
    )
    
    created_by_user_id = Column(
        UUID(as_uuid=True),
        nullable=False,
        index=True,
        doc="User who created the protocol"
    )
    
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        doc="Protocol creation timestamp"
    )
    
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        doc="Last update timestamp"
    )
    
    started_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Study start date"
    )
    
    completed_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Study completion date"
    )
    
    def __repr__(self) -> str:
        """String representation of the ResearchProtocol model."""
        return f"<ResearchProtocol(id={self.id}, title={self.title[:50]}, status={self.status})>"


class LiteratureReference(Base):
    """
    Literature reference model for research paper management.
    
    This model stores and manages scientific literature references,
    including metadata, abstracts, and user annotations.
    
    Attributes:
        id: Unique reference identifier
        title: Paper title
        authors: List of authors
        journal: Journal name
        publication_date: Publication date
        doi: Digital Object Identifier
        pmid: PubMed ID
        url: Paper URL
        abstract: Paper abstract
        keywords: Research keywords
        study_type: Type of study (RCT, observational, meta-analysis, etc.)
        sample_size: Study sample size
        notes: User notes and annotations
        quality_score: Quality assessment score
        relevance_score: Relevance to platform topics
        added_by_user_id: User who added the reference
        created_at: Reference creation timestamp
        updated_at: Last update timestamp
    """
    
    __tablename__ = "literature_references"
    
    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        index=True,
        doc="Unique reference identifier"
    )
    
    title = Column(
        Text,
        nullable=False,
        index=True,
        doc="Paper title"
    )
    
    authors = Column(
        Text,
        nullable=True,
        doc="List of authors"
    )
    
    journal = Column(
        String(255),
        nullable=True,
        index=True,
        doc="Journal name"
    )
    
    publication_date = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Publication date"
    )
    
    doi = Column(
        String(255),
        nullable=True,
        unique=True,
        index=True,
        doc="Digital Object Identifier"
    )
    
    pmid = Column(
        String(20),
        nullable=True,
        unique=True,
        index=True,
        doc="PubMed ID"
    )
    
    url = Column(
        Text,
        nullable=True,
        doc="Paper URL"
    )
    
    abstract = Column(
        Text,
        nullable=True,
        doc="Paper abstract"
    )
    
    keywords = Column(
        Text,
        nullable=True,
        doc="Research keywords"
    )
    
    study_type = Column(
        String(50),
        nullable=True,
        index=True,
        doc="Type of study"
    )
    
    sample_size = Column(
        Integer,
        nullable=True,
        doc="Study sample size"
    )
    
    notes = Column(
        Text,
        nullable=True,
        doc="User notes and annotations"
    )
    
    quality_score = Column(
        Numeric(precision=3, scale=1),
        nullable=True,
        doc="Quality assessment score"
    )
    
    relevance_score = Column(
        Numeric(precision=3, scale=1),
        nullable=True,
        doc="Relevance to platform topics"
    )
    
    added_by_user_id = Column(
        UUID(as_uuid=True),
        nullable=False,
        index=True,
        doc="User who added the reference"
    )
    
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        doc="Reference creation timestamp"
    )
    
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        doc="Last update timestamp"
    )
    
    def __repr__(self) -> str:
        """String representation of the LiteratureReference model."""
        return f"<LiteratureReference(id={self.id}, title={self.title[:50]}, doi={self.doi})>"


class StudyParticipant(Base):
    """
    Study participant model for research participant management.
    
    This model manages participants in research studies, including
    enrollment status, demographics, and participation tracking.
    
    Attributes:
        id: Unique participant identifier
        protocol_id: Associated research protocol
        user_id: Platform user (if registered user)
        participant_code: Anonymous participant identifier
        status: Participation status (invited, enrolled, active, completed, withdrawn)
        demographics: JSON structure of demographic data
        baseline_data: Baseline measurements and data
        consent_given: Informed consent status
        consent_date: Date consent was given
        enrolled_at: Enrollment timestamp
        completed_at: Study completion timestamp
        withdrawal_reason: Reason for withdrawal (if applicable)
        notes: Researcher notes about participant
        created_at: Record creation timestamp
        updated_at: Last update timestamp
    """
    
    __tablename__ = "study_participants"
    
    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        index=True,
        doc="Unique participant identifier"
    )
    
    protocol_id = Column(
        UUID(as_uuid=True),
        ForeignKey("research_protocols.id"),
        nullable=False,
        index=True,
        doc="Associated research protocol"
    )
    
    user_id = Column(
        UUID(as_uuid=True),
        nullable=True,
        index=True,
        doc="Platform user (if registered user)"
    )
    
    participant_code = Column(
        String(50),
        nullable=False,
        index=True,
        doc="Anonymous participant identifier"
    )
    
    status = Column(
        String(20),
        nullable=False,
        default="invited",
        index=True,
        doc="Participation status"
    )
    
    demographics = Column(
        JSON,
        nullable=True,
        doc="JSON structure of demographic data"
    )
    
    baseline_data = Column(
        JSON,
        nullable=True,
        doc="Baseline measurements and data"
    )
    
    consent_given = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Informed consent status"
    )
    
    consent_date = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Date consent was given"
    )
    
    enrolled_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Enrollment timestamp"
    )
    
    completed_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Study completion timestamp"
    )
    
    withdrawal_reason = Column(
        Text,
        nullable=True,
        doc="Reason for withdrawal (if applicable)"
    )
    
    notes = Column(
        Text,
        nullable=True,
        doc="Researcher notes about participant"
    )
    
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        doc="Record creation timestamp"
    )
    
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        doc="Last update timestamp"
    )
    
    # Relationships
    protocol = relationship("ResearchProtocol", backref="participants")
    
    # Ensure unique participant codes within each protocol
    __table_args__ = (
        UniqueConstraint('protocol_id', 'participant_code', name='uq_protocol_participant_code'),
    )
    
    def __repr__(self) -> str:
        """String representation of the StudyParticipant model."""
        return f"<StudyParticipant(id={self.id}, code={self.participant_code}, status={self.status})>"


class StatisticalAnalysis(Base):
    """
    Statistical analysis model for research data analysis.

    This model stores statistical analyses performed on research data,
    including correlation studies, hypothesis tests, and data visualizations.

    Attributes:
        id: Unique analysis identifier
        protocol_id: Associated research protocol (optional)
        title: Analysis title
        description: Analysis description
        analysis_type: Type of analysis (correlation, t-test, anova, regression, etc.)
        data_source: Source of data for analysis
        variables: Variables included in analysis
        parameters: Analysis parameters and settings
        results: Analysis results and statistics
        visualizations: Generated charts and graphs metadata
        interpretation: Analysis interpretation and conclusions
        confidence_level: Statistical confidence level
        p_value: Statistical significance p-value
        effect_size: Effect size measure
        created_by_user_id: User who performed the analysis
        created_at: Analysis creation timestamp
        updated_at: Last update timestamp
    """

    __tablename__ = "statistical_analyses"

    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        index=True,
        doc="Unique analysis identifier"
    )

    protocol_id = Column(
        UUID(as_uuid=True),
        ForeignKey("research_protocols.id"),
        nullable=True,
        index=True,
        doc="Associated research protocol (optional)"
    )

    title = Column(
        String(500),
        nullable=False,
        index=True,
        doc="Analysis title"
    )

    description = Column(
        Text,
        nullable=True,
        doc="Analysis description"
    )

    analysis_type = Column(
        String(50),
        nullable=False,
        index=True,
        doc="Type of analysis"
    )

    data_source = Column(
        String(100),
        nullable=False,
        doc="Source of data for analysis"
    )

    variables = Column(
        JSON,
        nullable=False,
        doc="Variables included in analysis"
    )

    parameters = Column(
        JSON,
        nullable=True,
        doc="Analysis parameters and settings"
    )

    results = Column(
        JSON,
        nullable=True,
        doc="Analysis results and statistics"
    )

    visualizations = Column(
        JSON,
        nullable=True,
        doc="Generated charts and graphs metadata"
    )

    interpretation = Column(
        Text,
        nullable=True,
        doc="Analysis interpretation and conclusions"
    )

    confidence_level = Column(
        Numeric(precision=4, scale=3),
        nullable=True,
        doc="Statistical confidence level"
    )

    p_value = Column(
        Numeric(precision=10, scale=9),
        nullable=True,
        doc="Statistical significance p-value"
    )

    effect_size = Column(
        Numeric(precision=6, scale=4),
        nullable=True,
        doc="Effect size measure"
    )

    created_by_user_id = Column(
        UUID(as_uuid=True),
        nullable=False,
        index=True,
        doc="User who performed the analysis"
    )

    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        doc="Analysis creation timestamp"
    )

    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        doc="Last update timestamp"
    )

    # Relationships
    protocol = relationship("ResearchProtocol", backref="analyses")

    def __repr__(self) -> str:
        """String representation of the StatisticalAnalysis model."""
        return f"<StatisticalAnalysis(id={self.id}, title={self.title[:50]}, type={self.analysis_type})>"


class ResearchCollaboration(Base):
    """
    Research collaboration model for team-based research projects.

    This model manages collaborations between researchers on protocols,
    including roles, permissions, and contribution tracking.

    Attributes:
        id: Unique collaboration identifier
        protocol_id: Associated research protocol
        user_id: Collaborating user
        role: Collaboration role (principal_investigator, co_investigator, analyst, etc.)
        permissions: JSON structure of permissions
        contribution_description: Description of user's contribution
        status: Collaboration status (invited, active, completed, removed)
        invited_by_user_id: User who sent the invitation
        invited_at: Invitation timestamp
        accepted_at: Acceptance timestamp
        created_at: Record creation timestamp
        updated_at: Last update timestamp
    """

    __tablename__ = "research_collaborations"

    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        index=True,
        doc="Unique collaboration identifier"
    )

    protocol_id = Column(
        UUID(as_uuid=True),
        ForeignKey("research_protocols.id"),
        nullable=False,
        index=True,
        doc="Associated research protocol"
    )

    user_id = Column(
        UUID(as_uuid=True),
        nullable=False,
        index=True,
        doc="Collaborating user"
    )

    role = Column(
        String(50),
        nullable=False,
        index=True,
        doc="Collaboration role"
    )

    permissions = Column(
        JSON,
        nullable=True,
        doc="JSON structure of permissions"
    )

    contribution_description = Column(
        Text,
        nullable=True,
        doc="Description of user's contribution"
    )

    status = Column(
        String(20),
        nullable=False,
        default="invited",
        index=True,
        doc="Collaboration status"
    )

    invited_by_user_id = Column(
        UUID(as_uuid=True),
        nullable=False,
        index=True,
        doc="User who sent the invitation"
    )

    invited_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        doc="Invitation timestamp"
    )

    accepted_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Acceptance timestamp"
    )

    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        doc="Record creation timestamp"
    )

    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        doc="Last update timestamp"
    )

    # Relationships
    protocol = relationship("ResearchProtocol", backref="collaborations")

    # Ensure unique user-protocol combinations
    __table_args__ = (
        UniqueConstraint('protocol_id', 'user_id', name='uq_protocol_user_collaboration'),
    )

    def __repr__(self) -> str:
        """String representation of the ResearchCollaboration model."""
        return f"<ResearchCollaboration(id={self.id}, protocol_id={self.protocol_id}, role={self.role})>"
