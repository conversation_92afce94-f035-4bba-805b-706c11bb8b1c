"""
Research tools service layer.

This module provides business logic for research protocol management,
literature references, statistical analysis, and participant management.
"""

from typing import List, Optional, Dict, Any
from uuid import UUID
from datetime import datetime

from sqlalchemy import and_, desc, func, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload

from app.modules.research_tools.models import (
    ResearchProtocol, LiteratureReference, StudyParticipant, 
    StatisticalAnalysis, ResearchCollaboration
)
from app.modules.research_tools.schemas import (
    ResearchProtocolCreate, ResearchProtocolUpdate,
    LiteratureReferenceCreate, LiteratureReferenceUpdate,
    StudyParticipantCreate, StudyParticipantUpdate,
    StatisticalAnalysisCreate, StatisticalAnalysisUpdate,
    ResearchCollaborationCreate, ResearchCollaborationUpdate
)


class ResearchProtocolService:
    """Service for managing research protocols."""

    @staticmethod
    async def create_protocol(
        db: AsyncSession,
        protocol_data: ResearchProtocolCreate,
        creator_id: UUID
    ) -> ResearchProtocol:
        """Create a new research protocol."""
        protocol = ResearchProtocol(
            **protocol_data.model_dump(),
            created_by_user_id=creator_id
        )
        db.add(protocol)
        await db.commit()
        await db.refresh(protocol)
        return protocol

    @staticmethod
    async def get_protocol(db: AsyncSession, protocol_id: UUID) -> Optional[ResearchProtocol]:
        """Get a research protocol by ID."""
        result = await db.execute(
            select(ResearchProtocol)
            .options(
                selectinload(ResearchProtocol.participants),
                selectinload(ResearchProtocol.analyses),
                selectinload(ResearchProtocol.collaborations)
            )
            .where(ResearchProtocol.id == protocol_id)
        )
        return result.scalar_one_or_none()

    @staticmethod
    async def get_protocols(
        db: AsyncSession,
        skip: int = 0,
        limit: int = 20,
        status: Optional[str] = None,
        creator_id: Optional[UUID] = None,
        search: Optional[str] = None
    ) -> List[ResearchProtocol]:
        """Get research protocols with optional filtering."""
        query = select(ResearchProtocol)
        
        if status:
            query = query.where(ResearchProtocol.status == status)
        
        if creator_id:
            query = query.where(ResearchProtocol.created_by_user_id == creator_id)
        
        if search:
            search_term = f"%{search}%"
            query = query.where(
                or_(
                    ResearchProtocol.title.ilike(search_term),
                    ResearchProtocol.description.ilike(search_term),
                    ResearchProtocol.hypothesis.ilike(search_term)
                )
            )
        
        query = query.order_by(desc(ResearchProtocol.created_at))
        query = query.offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def update_protocol(
        db: AsyncSession,
        protocol_id: UUID,
        protocol_data: ResearchProtocolUpdate,
        user_id: UUID
    ) -> Optional[ResearchProtocol]:
        """Update a research protocol."""
        result = await db.execute(
            select(ResearchProtocol).where(
                and_(
                    ResearchProtocol.id == protocol_id,
                    ResearchProtocol.created_by_user_id == user_id
                )
            )
        )
        protocol = result.scalar_one_or_none()
        if not protocol:
            return None

        update_data = protocol_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(protocol, field, value)

        # Update timestamps for status changes
        if "status" in update_data:
            if update_data["status"] == "active" and not protocol.started_at:
                protocol.started_at = datetime.utcnow()
            elif update_data["status"] == "completed" and not protocol.completed_at:
                protocol.completed_at = datetime.utcnow()

        await db.commit()
        await db.refresh(protocol)
        return protocol

    @staticmethod
    async def delete_protocol(
        db: AsyncSession,
        protocol_id: UUID,
        user_id: UUID
    ) -> bool:
        """Delete a research protocol."""
        result = await db.execute(
            select(ResearchProtocol).where(
                and_(
                    ResearchProtocol.id == protocol_id,
                    ResearchProtocol.created_by_user_id == user_id
                )
            )
        )
        protocol = result.scalar_one_or_none()
        if protocol:
            await db.delete(protocol)
            await db.commit()
            return True
        return False

    @staticmethod
    async def get_protocol_statistics(
        db: AsyncSession,
        protocol_id: UUID
    ) -> Dict[str, Any]:
        """Get statistics for a research protocol."""
        # Get participant counts by status
        participant_stats = await db.execute(
            select(
                StudyParticipant.status,
                func.count(StudyParticipant.id).label('count')
            )
            .where(StudyParticipant.protocol_id == protocol_id)
            .group_by(StudyParticipant.status)
        )
        
        participant_counts = {row.status: row.count for row in participant_stats}
        
        # Get analysis count
        analysis_count = await db.execute(
            select(func.count(StatisticalAnalysis.id))
            .where(StatisticalAnalysis.protocol_id == protocol_id)
        )
        
        # Get collaboration count
        collaboration_count = await db.execute(
            select(func.count(ResearchCollaboration.id))
            .where(ResearchCollaboration.protocol_id == protocol_id)
        )
        
        return {
            "participant_counts": participant_counts,
            "total_participants": sum(participant_counts.values()),
            "analysis_count": analysis_count.scalar() or 0,
            "collaboration_count": collaboration_count.scalar() or 0
        }


class LiteratureReferenceService:
    """Service for managing literature references."""

    @staticmethod
    async def create_reference(
        db: AsyncSession,
        reference_data: LiteratureReferenceCreate,
        user_id: UUID
    ) -> LiteratureReference:
        """Create a new literature reference."""
        reference = LiteratureReference(
            **reference_data.model_dump(),
            added_by_user_id=user_id
        )
        db.add(reference)
        await db.commit()
        await db.refresh(reference)
        return reference

    @staticmethod
    async def get_reference(db: AsyncSession, reference_id: UUID) -> Optional[LiteratureReference]:
        """Get a literature reference by ID."""
        result = await db.execute(
            select(LiteratureReference).where(LiteratureReference.id == reference_id)
        )
        return result.scalar_one_or_none()

    @staticmethod
    async def get_references(
        db: AsyncSession,
        skip: int = 0,
        limit: int = 20,
        search: Optional[str] = None,
        journal: Optional[str] = None,
        study_type: Optional[str] = None,
        min_quality_score: Optional[float] = None
    ) -> List[LiteratureReference]:
        """Get literature references with optional filtering."""
        query = select(LiteratureReference)
        
        if search:
            search_term = f"%{search}%"
            query = query.where(
                or_(
                    LiteratureReference.title.ilike(search_term),
                    LiteratureReference.authors.ilike(search_term),
                    LiteratureReference.keywords.ilike(search_term),
                    LiteratureReference.abstract.ilike(search_term)
                )
            )
        
        if journal:
            query = query.where(LiteratureReference.journal.ilike(f"%{journal}%"))
        
        if study_type:
            query = query.where(LiteratureReference.study_type == study_type)
        
        if min_quality_score is not None:
            query = query.where(LiteratureReference.quality_score >= min_quality_score)
        
        query = query.order_by(desc(LiteratureReference.publication_date))
        query = query.offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def update_reference(
        db: AsyncSession,
        reference_id: UUID,
        reference_data: LiteratureReferenceUpdate,
        user_id: UUID
    ) -> Optional[LiteratureReference]:
        """Update a literature reference."""
        result = await db.execute(
            select(LiteratureReference).where(
                and_(
                    LiteratureReference.id == reference_id,
                    LiteratureReference.added_by_user_id == user_id
                )
            )
        )
        reference = result.scalar_one_or_none()
        if not reference:
            return None

        update_data = reference_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(reference, field, value)

        await db.commit()
        await db.refresh(reference)
        return reference

    @staticmethod
    async def delete_reference(
        db: AsyncSession,
        reference_id: UUID,
        user_id: UUID
    ) -> bool:
        """Delete a literature reference."""
        result = await db.execute(
            select(LiteratureReference).where(
                and_(
                    LiteratureReference.id == reference_id,
                    LiteratureReference.added_by_user_id == user_id
                )
            )
        )
        reference = result.scalar_one_or_none()
        if reference:
            await db.delete(reference)
            await db.commit()
            return True
        return False

    @staticmethod
    async def search_by_doi(db: AsyncSession, doi: str) -> Optional[LiteratureReference]:
        """Search for a reference by DOI."""
        result = await db.execute(
            select(LiteratureReference).where(LiteratureReference.doi == doi)
        )
        return result.scalar_one_or_none()

    @staticmethod
    async def search_by_pmid(db: AsyncSession, pmid: str) -> Optional[LiteratureReference]:
        """Search for a reference by PubMed ID."""
        result = await db.execute(
            select(LiteratureReference).where(LiteratureReference.pmid == pmid)
        )
        return result.scalar_one_or_none()


class StudyParticipantService:
    """Service for managing study participants."""

    @staticmethod
    async def create_participant(
        db: AsyncSession,
        participant_data: StudyParticipantCreate,
        researcher_id: UUID
    ) -> StudyParticipant:
        """Create a new study participant."""
        participant = StudyParticipant(**participant_data.model_dump())
        db.add(participant)
        await db.commit()
        await db.refresh(participant)
        return participant

    @staticmethod
    async def get_participant(db: AsyncSession, participant_id: UUID) -> Optional[StudyParticipant]:
        """Get a study participant by ID."""
        result = await db.execute(
            select(StudyParticipant)
            .options(selectinload(StudyParticipant.protocol))
            .where(StudyParticipant.id == participant_id)
        )
        return result.scalar_one_or_none()

    @staticmethod
    async def get_protocol_participants(
        db: AsyncSession,
        protocol_id: UUID,
        skip: int = 0,
        limit: int = 20,
        status: Optional[str] = None
    ) -> List[StudyParticipant]:
        """Get participants for a specific protocol."""
        query = select(StudyParticipant).where(StudyParticipant.protocol_id == protocol_id)
        
        if status:
            query = query.where(StudyParticipant.status == status)
        
        query = query.order_by(StudyParticipant.created_at)
        query = query.offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def update_participant(
        db: AsyncSession,
        participant_id: UUID,
        participant_data: StudyParticipantUpdate
    ) -> Optional[StudyParticipant]:
        """Update a study participant."""
        result = await db.execute(
            select(StudyParticipant).where(StudyParticipant.id == participant_id)
        )
        participant = result.scalar_one_or_none()
        if not participant:
            return None

        update_data = participant_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(participant, field, value)

        # Update timestamps for status changes
        if "status" in update_data:
            if update_data["status"] == "enrolled" and not participant.enrolled_at:
                participant.enrolled_at = datetime.utcnow()
            elif update_data["status"] == "completed" and not participant.completed_at:
                participant.completed_at = datetime.utcnow()

        # Update consent timestamp
        if "consent_given" in update_data and update_data["consent_given"] and not participant.consent_date:
            participant.consent_date = datetime.utcnow()

        await db.commit()
        await db.refresh(participant)
        return participant

    @staticmethod
    async def generate_participant_code(db: AsyncSession, protocol_id: UUID) -> str:
        """Generate a unique participant code for a protocol."""
        # Get the current participant count for the protocol
        count_result = await db.execute(
            select(func.count(StudyParticipant.id))
            .where(StudyParticipant.protocol_id == protocol_id)
        )
        count = count_result.scalar() or 0
        
        # Generate code in format: P001, P002, etc.
        return f"P{count + 1:03d}"


class StatisticalAnalysisService:
    """Service for managing statistical analyses."""

    @staticmethod
    async def create_analysis(
        db: AsyncSession,
        analysis_data: StatisticalAnalysisCreate,
        analyst_id: UUID
    ) -> StatisticalAnalysis:
        """Create a new statistical analysis."""
        analysis = StatisticalAnalysis(
            **analysis_data.model_dump(),
            created_by_user_id=analyst_id
        )
        db.add(analysis)
        await db.commit()
        await db.refresh(analysis)
        return analysis

    @staticmethod
    async def get_analysis(db: AsyncSession, analysis_id: UUID) -> Optional[StatisticalAnalysis]:
        """Get a statistical analysis by ID."""
        result = await db.execute(
            select(StatisticalAnalysis)
            .options(selectinload(StatisticalAnalysis.protocol))
            .where(StatisticalAnalysis.id == analysis_id)
        )
        return result.scalar_one_or_none()

    @staticmethod
    async def get_analyses(
        db: AsyncSession,
        skip: int = 0,
        limit: int = 20,
        protocol_id: Optional[UUID] = None,
        analysis_type: Optional[str] = None,
        analyst_id: Optional[UUID] = None
    ) -> List[StatisticalAnalysis]:
        """Get statistical analyses with optional filtering."""
        query = select(StatisticalAnalysis)

        if protocol_id:
            query = query.where(StatisticalAnalysis.protocol_id == protocol_id)

        if analysis_type:
            query = query.where(StatisticalAnalysis.analysis_type == analysis_type)

        if analyst_id:
            query = query.where(StatisticalAnalysis.created_by_user_id == analyst_id)

        query = query.order_by(desc(StatisticalAnalysis.created_at))
        query = query.offset(skip).limit(limit)

        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def update_analysis(
        db: AsyncSession,
        analysis_id: UUID,
        analysis_data: StatisticalAnalysisUpdate,
        user_id: UUID
    ) -> Optional[StatisticalAnalysis]:
        """Update a statistical analysis."""
        result = await db.execute(
            select(StatisticalAnalysis).where(
                and_(
                    StatisticalAnalysis.id == analysis_id,
                    StatisticalAnalysis.created_by_user_id == user_id
                )
            )
        )
        analysis = result.scalar_one_or_none()
        if not analysis:
            return None

        update_data = analysis_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(analysis, field, value)

        await db.commit()
        await db.refresh(analysis)
        return analysis

    @staticmethod
    async def delete_analysis(
        db: AsyncSession,
        analysis_id: UUID,
        user_id: UUID
    ) -> bool:
        """Delete a statistical analysis."""
        result = await db.execute(
            select(StatisticalAnalysis).where(
                and_(
                    StatisticalAnalysis.id == analysis_id,
                    StatisticalAnalysis.created_by_user_id == user_id
                )
            )
        )
        analysis = result.scalar_one_or_none()
        if analysis:
            await db.delete(analysis)
            await db.commit()
            return True
        return False


class ResearchCollaborationService:
    """Service for managing research collaborations."""

    @staticmethod
    async def create_collaboration(
        db: AsyncSession,
        collaboration_data: ResearchCollaborationCreate,
        inviter_id: UUID
    ) -> ResearchCollaboration:
        """Create a new research collaboration."""
        collaboration = ResearchCollaboration(
            **collaboration_data.model_dump(),
            invited_by_user_id=inviter_id
        )
        db.add(collaboration)
        await db.commit()
        await db.refresh(collaboration)
        return collaboration

    @staticmethod
    async def get_collaboration(db: AsyncSession, collaboration_id: UUID) -> Optional[ResearchCollaboration]:
        """Get a research collaboration by ID."""
        result = await db.execute(
            select(ResearchCollaboration)
            .options(selectinload(ResearchCollaboration.protocol))
            .where(ResearchCollaboration.id == collaboration_id)
        )
        return result.scalar_one_or_none()

    @staticmethod
    async def get_protocol_collaborations(
        db: AsyncSession,
        protocol_id: UUID,
        skip: int = 0,
        limit: int = 20,
        status: Optional[str] = None
    ) -> List[ResearchCollaboration]:
        """Get collaborations for a specific protocol."""
        query = select(ResearchCollaboration).where(ResearchCollaboration.protocol_id == protocol_id)

        if status:
            query = query.where(ResearchCollaboration.status == status)

        query = query.order_by(ResearchCollaboration.invited_at)
        query = query.offset(skip).limit(limit)

        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def get_user_collaborations(
        db: AsyncSession,
        user_id: UUID,
        skip: int = 0,
        limit: int = 20,
        status: Optional[str] = None
    ) -> List[ResearchCollaboration]:
        """Get collaborations for a specific user."""
        query = select(ResearchCollaboration).where(ResearchCollaboration.user_id == user_id)

        if status:
            query = query.where(ResearchCollaboration.status == status)

        query = query.order_by(desc(ResearchCollaboration.invited_at))
        query = query.offset(skip).limit(limit)

        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def update_collaboration(
        db: AsyncSession,
        collaboration_id: UUID,
        collaboration_data: ResearchCollaborationUpdate,
        user_id: UUID
    ) -> Optional[ResearchCollaboration]:
        """Update a research collaboration."""
        result = await db.execute(
            select(ResearchCollaboration).where(
                and_(
                    ResearchCollaboration.id == collaboration_id,
                    or_(
                        ResearchCollaboration.user_id == user_id,
                        ResearchCollaboration.invited_by_user_id == user_id
                    )
                )
            )
        )
        collaboration = result.scalar_one_or_none()
        if not collaboration:
            return None

        update_data = collaboration_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(collaboration, field, value)

        # Update acceptance timestamp
        if "status" in update_data and update_data["status"] == "active" and not collaboration.accepted_at:
            collaboration.accepted_at = datetime.utcnow()

        await db.commit()
        await db.refresh(collaboration)
        return collaboration

    @staticmethod
    async def accept_collaboration(
        db: AsyncSession,
        collaboration_id: UUID,
        user_id: UUID
    ) -> Optional[ResearchCollaboration]:
        """Accept a collaboration invitation."""
        result = await db.execute(
            select(ResearchCollaboration).where(
                and_(
                    ResearchCollaboration.id == collaboration_id,
                    ResearchCollaboration.user_id == user_id,
                    ResearchCollaboration.status == "invited"
                )
            )
        )
        collaboration = result.scalar_one_or_none()
        if collaboration:
            collaboration.status = "active"
            collaboration.accepted_at = datetime.utcnow()
            await db.commit()
            await db.refresh(collaboration)
        return collaboration

    @staticmethod
    async def decline_collaboration(
        db: AsyncSession,
        collaboration_id: UUID,
        user_id: UUID
    ) -> bool:
        """Decline a collaboration invitation."""
        result = await db.execute(
            select(ResearchCollaboration).where(
                and_(
                    ResearchCollaboration.id == collaboration_id,
                    ResearchCollaboration.user_id == user_id,
                    ResearchCollaboration.status == "invited"
                )
            )
        )
        collaboration = result.scalar_one_or_none()
        if collaboration:
            collaboration.status = "removed"
            await db.commit()
            return True
        return False
