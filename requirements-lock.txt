absl-py==2.3.0
alabaster==1.0.0
alembic==1.14.0
amqp==5.3.1
annotated-types==0.7.0
anyio==3.7.1
astunparse==1.6.3
async-timeout==5.0.1
asyncpg==0.30.0
attrs==25.3.0
Authlib==1.6.0
babel==2.17.0
backports-datetime-fromisoformat==2.0.3
bcrypt==4.3.0
beautifulsoup4==4.13.4
billiard==4.2.1
black==25.1.0
blinker==1.4
blis==1.2.1
boto3==1.35.95
botocore==1.35.99
bracex==2.5.post1
cachetools==5.5.2
catalogue==2.0.10
celery==5.4.0
certifi==2025.6.15
cfgv==3.4.0
charset-normalizer==3.4.2
click==8.1.8
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
cloudpathlib==0.16.0
colorama==0.4.6
confection==0.1.5
coverage==7.9.1
cryptography==3.4.8
cymem==2.0.11
dbus-python==1.2.18
distlib==0.3.9
distro==1.7.0
distro-info==1.1+ubuntu0.2
dnspython==2.7.0
doc8==2.0.0
docutils==0.21.2
dparse==0.6.4
ecdsa==0.19.1
elastic-transport==8.17.1
elasticsearch==8.17.0
email_validator==2.2.0
exceptiongroup==1.3.0
fastapi==0.115.6
filelock==3.16.1
flake8==7.1.1
flatbuffers==25.2.10
fsspec==2025.5.1
gast==0.6.0
ghp-import==2.1.0
google-auth==2.40.3
google-auth-oauthlib==1.2.2
google-pasta==0.2.0
greenlet==3.2.3
grpcio==1.73.0
h11==0.16.0
h5py==3.14.0
hf-xet==1.1.4
html5lib==1.1
httpcore==1.0.9
httplib2==0.20.2
httptools==0.6.4
httpx==0.28.1
huggingface-hub==0.33.0
identify==2.6.12
idna==3.10
imagesize==1.4.1
importlib-metadata==4.6.4
iniconfig==1.1.1
jeepney==0.7.1
Jinja2==3.1.6
jmespath==1.0.1
joblib==1.5.1
jsonpointer==3.0.0
jsonschema==4.24.0
jsonschema-path==0.3.4
jsonschema-specifications==2025.4.1
keras==3.10.0
keyring==23.5.0
kombu==5.5.4
langcodes==3.5.0
language_data==1.3.0
launchpadlib==1.10.16
lazr.restfulclient==0.14.4
lazr.uri==1.0.6
lazy-object-proxy==1.11.0
libclang==18.1.1
lxml==5.4.0
Mako==1.3.10
marisa-trie==1.2.1
Markdown==3.8
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==4.0.0
mccabe==0.7.0
mdit-py-plugins==0.4.2
mdurl==0.1.2
mergedeep==1.3.4
mkdocs==1.6.1
mkdocs-get-deps==0.2.0
mkdocs-material==9.5.50
mkdocs-material-extensions==1.3.1
ml-dtypes==0.4.1
more-itertools==8.10.0
murmurhash==1.0.13
mypy==1.14.1
mypy_extensions==1.1.0
myst-parser==4.0.1
namex==0.1.0
nltk==3.9.1
nodeenv==1.9.1
numpy==1.26.4
oauthlib==3.2.0
openapi-schema-validator==0.6.3
openapi-spec-validator==0.7.2
opt_einsum==3.4.0
optree==0.16.0
packaging==25.0
paginate==0.5.7
pandas==2.2.3
passlib==1.7.4
pathable==0.4.4
pathspec==0.12.1
pbr==6.1.1
platformdirs==4.3.8
pluggy==1.6.0
pre_commit==4.0.1
preshed==3.0.10
prompt_toolkit==3.0.51
protobuf==4.25.8
psutil==6.1.1
psycopg2-binary==2.9.10
py==1.10.0
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycodestyle==2.12.1
pydantic==2.10.4
pydantic-settings==2.7.0
pydantic_core==2.27.2
pyflakes==3.2.0
Pygments==2.19.1
PyGObject==3.42.1
PyJWT==2.3.0
pymdown-extensions==10.15
pyparsing==2.4.7
pyspelling==2.10
pytest==8.3.4
pytest-asyncio==0.25.0
pytest-cov==6.0.0
python-apt==2.4.0+ubuntu4
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
python-jose==3.3.0
python-multipart==0.0.20
pytz==2025.2
PyYAML==6.0.2
pyyaml_env_tag==1.1
redis==5.2.1
referencing==0.36.2
regex==2024.11.6
requests==2.32.4
requests-oauthlib==2.0.0
restructuredtext-lint==1.4.0
rfc3339-validator==0.1.4
rich==14.0.0
rpds-py==0.25.1
rsa==4.9.1
rstcheck==6.2.5
rstcheck-core==1.2.2
ruamel.yaml==0.18.14
ruamel.yaml.clib==0.2.12
s3transfer==0.10.4
safetensors==0.5.3
safety==3.5.2
safety-schemas==0.0.14
scikit-learn==1.7.0
scipy==1.15.3
SecretStorage==3.3.1
sentry-sdk==2.21.0
shellingham==1.5.4
six==1.16.0
smart-open==6.4.0
sniffio==1.3.1
snowballstemmer==3.0.1
soupsieve==2.7
spacy==3.8.3
spacy-legacy==3.0.12
spacy-loggers==1.0.5
Sphinx==8.1.3
sphinx-autobuild==2024.10.3
sphinx-autodoc-typehints==3.0.1
sphinx-click==6.0.0
sphinx-copybutton==0.5.2
sphinx-jsonschema==1.19.1
sphinx-rtd-theme==3.0.2
sphinx-tabs==3.4.7
sphinx_design==0.6.1
sphinxcontrib-applehelp==2.0.0
sphinxcontrib-devhelp==2.0.0
sphinxcontrib-htmlhelp==2.1.0
sphinxcontrib-jquery==4.1
sphinxcontrib-jsmath==1.0.1
sphinxcontrib-mermaid==1.0.0
sphinxcontrib-qthelp==2.0.0
sphinxcontrib-serializinghtml==2.0.0
sphinxext-opengraph==0.10.0
SQLAlchemy==2.0.36
srsly==2.5.1
ssh-import-id==5.11
starlette==0.41.3
stevedore==5.4.1
structlog==25.4.0
tenacity==9.1.2
tensorboard==2.18.0
tensorboard-data-server==0.7.2
tensorflow==2.18.0
tensorflow-estimator==2.15.0
tensorflow-io-gcs-filesystem==0.37.1
termcolor==3.1.0
thinc==8.3.4
threadpoolctl==3.6.0
tokenizers==0.21.1
toml==0.10.2
tomli==2.2.1
tomlkit==0.13.3
tqdm==4.67.1
transformers==4.52.4
typer==0.9.4
typing_extensions==4.14.0
tzdata==2025.2
unattended-upgrades==0.1
urllib3==2.4.0
uvicorn==0.32.1
uvloop==0.21.0
vine==5.1.0
virtualenv==20.31.2
wadllib==1.3.6
wasabi==1.1.3
watchdog==6.0.0
watchfiles==1.1.0
wcmatch==10.0
wcwidth==0.2.13
weasel==0.3.4
webencodings==0.5.1
websockets==15.0.1
Werkzeug==3.1.3
wrapt==1.14.1
zipp==1.0.0
