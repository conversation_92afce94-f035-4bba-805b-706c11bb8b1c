#!/usr/bin/env node
(function() {

  var fs = require('fs');
  var path = require('path');
  var parse = require('../parser').parse;
  var jsesc = require('jsesc');
  var regexes = process.argv.splice(2);
  var first = regexes[0];
  var data;
  var log = console.log;
  var flags = '';
  var main = function() {
    if (/^(?:-h|--help|undefined)$/.test(first)) {
      log([
        '\nUsage:\n',
        '\tregjsparser [regex ...]',
        '\tregjsparser [-f | --flags] u [regex ...]',
        '\tregjsparser [-v | --version]',
        '\tregjsparser [-h | --help]',
        '\nExamples:\n',
        '\tregjsparser \'^foo.bar$\'',
        '\tregjsparser \'[a-zA-Z0-9]\''
      ].join('\n'));
      return process.exit(1);
    }

    if (/^(?:-v|--version)$/.test(first)) {
      log('v%s', require(path.resolve(__dirname, '../package.json')).version);
      return process.exit(1);
    }

    if (/^(?:-f|--flags)$/.test(first)) {
      flags = regexes[1];
      regexes = regexes.slice(2);
    }

    regexes.forEach(function(snippet) {
      try {
        result = parse(snippet, flags);
        log(jsesc(result, {
          'json': true,
          'compact': false,
          'indent': '\t'
        }));
      } catch(error) {
        log(error.message + '\n');
        log('Error: failed to parse. Make sure the regular expression is valid.');
        log('If you think this is a bug in regjsparser, please report it:');
        log('\thttps://github.com/jviereck/regjsparser/issues/new');
        log('\nStack trace:\n');
        log(error.stack);
        return process.exit(1);
      }
    });
    // Return with exit status 0 outside of the `forEach` loop, in case
    // multiple regular expressions were passed in.
    return process.exit(0);
  };

  main();

}());
