{"name": "nth-check", "version": "1.0.2", "description": "performant nth-check parser & compiler", "main": "index.js", "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "https://github.com/fb55/nth-check"}, "files": ["compile.js", "index.js", "parse.js"], "keywords": ["nth-child", "nth", "css"], "author": "<PERSON> <<EMAIL>>", "license": "BSD-2-<PERSON><PERSON>", "bugs": {"url": "https://github.com/fb55/nth-check/issues"}, "homepage": "https://github.com/fb55/nth-check", "dependencies": {"boolbase": "~1.0.0"}}