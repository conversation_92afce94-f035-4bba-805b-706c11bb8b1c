"""Add research tools models

Revision ID: 992ccffc5740
Revises: 4633dcfc2947
Create Date: 2025-06-17 22:40:41.041072

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '992ccffc5740'
down_revision: Union[str, None] = '4633dcfc2947'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade database schema."""
    # Create research_protocols table
    op.create_table(
        'research_protocols',
        sa.Column('id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('title', sa.String(length=500), nullable=False),
        sa.Column('description', sa.Text(), nullable=False),
        sa.Column('hypothesis', sa.Text(), nullable=True),
        sa.Column('methodology', sa.Text(), nullable=False),
        sa.Column('variables', sa.JSON(), nullable=True),
        sa.Column('duration_days', sa.Integer(), nullable=False),
        sa.Column('sample_size_target', sa.Integer(), nullable=False),
        sa.Column('sample_size_actual', sa.Integer(), nullable=False, server_default=sa.text('0')),
        sa.Column('status', sa.String(length=20), nullable=False, server_default=sa.text("'draft'")),
        sa.Column('ethics_approval', sa.Boolean(), nullable=False, server_default=sa.text('false')),
        sa.Column('created_by_user_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('started_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id', name=op.f('pk_research_protocols'))
    )
    op.create_index(op.f('ix_research_protocols_created_by_user_id'), 'research_protocols', ['created_by_user_id'], unique=False)
    op.create_index(op.f('ix_research_protocols_id'), 'research_protocols', ['id'], unique=False)
    op.create_index(op.f('ix_research_protocols_status'), 'research_protocols', ['status'], unique=False)
    op.create_index(op.f('ix_research_protocols_title'), 'research_protocols', ['title'], unique=False)

    # Create literature_references table
    op.create_table(
        'literature_references',
        sa.Column('id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('title', sa.Text(), nullable=False),
        sa.Column('authors', sa.Text(), nullable=True),
        sa.Column('journal', sa.String(length=255), nullable=True),
        sa.Column('publication_date', sa.DateTime(timezone=True), nullable=True),
        sa.Column('doi', sa.String(length=255), nullable=True),
        sa.Column('pmid', sa.String(length=20), nullable=True),
        sa.Column('url', sa.Text(), nullable=True),
        sa.Column('abstract', sa.Text(), nullable=True),
        sa.Column('keywords', sa.Text(), nullable=True),
        sa.Column('study_type', sa.String(length=50), nullable=True),
        sa.Column('sample_size', sa.Integer(), nullable=True),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.Column('quality_score', sa.Numeric(precision=3, scale=1), nullable=True),
        sa.Column('relevance_score', sa.Numeric(precision=3, scale=1), nullable=True),
        sa.Column('added_by_user_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.PrimaryKeyConstraint('id', name=op.f('pk_literature_references')),
        sa.UniqueConstraint('doi', name=op.f('uq_literature_references_doi')),
        sa.UniqueConstraint('pmid', name=op.f('uq_literature_references_pmid'))
    )
    op.create_index(op.f('ix_literature_references_added_by_user_id'), 'literature_references', ['added_by_user_id'], unique=False)
    op.create_index(op.f('ix_literature_references_doi'), 'literature_references', ['doi'], unique=False)
    op.create_index(op.f('ix_literature_references_id'), 'literature_references', ['id'], unique=False)
    op.create_index(op.f('ix_literature_references_journal'), 'literature_references', ['journal'], unique=False)
    op.create_index(op.f('ix_literature_references_pmid'), 'literature_references', ['pmid'], unique=False)
    op.create_index(op.f('ix_literature_references_publication_date'), 'literature_references', ['publication_date'], unique=False)
    op.create_index(op.f('ix_literature_references_study_type'), 'literature_references', ['study_type'], unique=False)
    op.create_index(op.f('ix_literature_references_title'), 'literature_references', ['title'], unique=False)

    # Create study_participants table
    op.create_table(
        'study_participants',
        sa.Column('id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('protocol_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('participant_code', sa.String(length=50), nullable=False),
        sa.Column('status', sa.String(length=20), nullable=False, server_default=sa.text("'invited'")),
        sa.Column('demographics', sa.JSON(), nullable=True),
        sa.Column('baseline_data', sa.JSON(), nullable=True),
        sa.Column('consent_given', sa.Boolean(), nullable=False, server_default=sa.text('false')),
        sa.Column('consent_date', sa.DateTime(timezone=True), nullable=True),
        sa.Column('enrolled_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('withdrawal_reason', sa.Text(), nullable=True),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['protocol_id'], ['research_protocols.id'], name=op.f('fk_study_participants_protocol_id_research_protocols')),
        sa.PrimaryKeyConstraint('id', name=op.f('pk_study_participants')),
        sa.UniqueConstraint('protocol_id', 'participant_code', name='uq_protocol_participant_code')
    )
    op.create_index(op.f('ix_study_participants_id'), 'study_participants', ['id'], unique=False)
    op.create_index(op.f('ix_study_participants_participant_code'), 'study_participants', ['participant_code'], unique=False)
    op.create_index(op.f('ix_study_participants_protocol_id'), 'study_participants', ['protocol_id'], unique=False)
    op.create_index(op.f('ix_study_participants_status'), 'study_participants', ['status'], unique=False)
    op.create_index(op.f('ix_study_participants_user_id'), 'study_participants', ['user_id'], unique=False)

    # Create statistical_analyses table
    op.create_table(
        'statistical_analyses',
        sa.Column('id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('protocol_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('title', sa.String(length=500), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('analysis_type', sa.String(length=50), nullable=False),
        sa.Column('data_source', sa.String(length=100), nullable=False),
        sa.Column('variables', sa.JSON(), nullable=False),
        sa.Column('parameters', sa.JSON(), nullable=True),
        sa.Column('results', sa.JSON(), nullable=True),
        sa.Column('visualizations', sa.JSON(), nullable=True),
        sa.Column('interpretation', sa.Text(), nullable=True),
        sa.Column('confidence_level', sa.Numeric(precision=4, scale=3), nullable=True),
        sa.Column('p_value', sa.Numeric(precision=10, scale=9), nullable=True),
        sa.Column('effect_size', sa.Numeric(precision=6, scale=4), nullable=True),
        sa.Column('created_by_user_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['protocol_id'], ['research_protocols.id'], name=op.f('fk_statistical_analyses_protocol_id_research_protocols')),
        sa.PrimaryKeyConstraint('id', name=op.f('pk_statistical_analyses'))
    )
    op.create_index(op.f('ix_statistical_analyses_analysis_type'), 'statistical_analyses', ['analysis_type'], unique=False)
    op.create_index(op.f('ix_statistical_analyses_created_by_user_id'), 'statistical_analyses', ['created_by_user_id'], unique=False)
    op.create_index(op.f('ix_statistical_analyses_id'), 'statistical_analyses', ['id'], unique=False)
    op.create_index(op.f('ix_statistical_analyses_protocol_id'), 'statistical_analyses', ['protocol_id'], unique=False)
    op.create_index(op.f('ix_statistical_analyses_title'), 'statistical_analyses', ['title'], unique=False)

    # Create research_collaborations table
    op.create_table(
        'research_collaborations',
        sa.Column('id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('protocol_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('role', sa.String(length=50), nullable=False),
        sa.Column('permissions', sa.JSON(), nullable=True),
        sa.Column('contribution_description', sa.Text(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=False, server_default=sa.text("'invited'")),
        sa.Column('invited_by_user_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('invited_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('accepted_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['protocol_id'], ['research_protocols.id'], name=op.f('fk_research_collaborations_protocol_id_research_protocols')),
        sa.PrimaryKeyConstraint('id', name=op.f('pk_research_collaborations')),
        sa.UniqueConstraint('protocol_id', 'user_id', name='uq_protocol_user_collaboration')
    )
    op.create_index(op.f('ix_research_collaborations_id'), 'research_collaborations', ['id'], unique=False)
    op.create_index(op.f('ix_research_collaborations_invited_by_user_id'), 'research_collaborations', ['invited_by_user_id'], unique=False)
    op.create_index(op.f('ix_research_collaborations_protocol_id'), 'research_collaborations', ['protocol_id'], unique=False)
    op.create_index(op.f('ix_research_collaborations_role'), 'research_collaborations', ['role'], unique=False)
    op.create_index(op.f('ix_research_collaborations_status'), 'research_collaborations', ['status'], unique=False)
    op.create_index(op.f('ix_research_collaborations_user_id'), 'research_collaborations', ['user_id'], unique=False)


def downgrade() -> None:
    """Downgrade database schema."""
    # Drop tables in reverse order to handle foreign key constraints
    op.drop_table('research_collaborations')
    op.drop_table('statistical_analyses')
    op.drop_table('study_participants')
    op.drop_table('literature_references')
    op.drop_table('research_protocols')
