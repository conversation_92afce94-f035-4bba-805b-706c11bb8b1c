#!/bin/bash

# Supplement Tracker Management Script
# Convenient wrapper for managing the Supplement Tracker application

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SERVICE_MANAGER="${SCRIPT_DIR}/scripts/service-url-manager.sh"
COMPOSE_FILE="${SCRIPT_DIR}/docker-compose.host-traefik.yml"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

show_help() {
    echo "Supplement Tracker Management Script"
    echo
    echo "Usage: $0 [command]"
    echo
    echo "Application Commands:"
    echo "  start, up              Start the application with portless URLs"
    echo "  stop, down             Stop the application"
    echo "  restart                Restart the application"
    echo "  status, ps             Show application status"
    echo "  logs                   Show application logs"
    echo
    echo "Service Management:"
    echo "  register               Register services with host Traefik"
    echo "  unregister             Unregister services from host Traefik"
    echo "  test                   Test connectivity to services"
    echo
    echo "Development:"
    echo "  build                  Rebuild application containers"
    echo "  shell-backend          Open shell in backend container"
    echo "  shell-frontend         Open shell in frontend container"
    echo
    echo "URLs (after starting):"
    echo "  • Frontend:     http://app.pills.localhost"
    echo "  • Backend API:  http://api.pills.localhost"
    echo "  • API Docs:     http://api.pills.localhost/docs"
    echo "  • Traefik:      http://traefik.pills.localhost"
    echo
}

start_application() {
    log_info "Starting Supplement Tracker with portless URLs..."
    
    # Start containers
    docker compose -f "${COMPOSE_FILE}" up -d
    
    # Wait for containers to be ready
    sleep 5
    
    # Register with host Traefik
    "${SERVICE_MANAGER}" register
    
    log_success "Application started successfully!"
    echo
    log_info "Available at:"
    log_info "  • Frontend:     http://app.pills.localhost"
    log_info "  • Backend API:  http://api.pills.localhost"
    log_info "  • API Docs:     http://api.pills.localhost/docs"
    log_info "  • Traefik:      http://traefik.pills.localhost"
}

stop_application() {
    log_info "Stopping Supplement Tracker..."
    
    # Unregister from host Traefik
    "${SERVICE_MANAGER}" unregister 2>/dev/null || true
    
    # Stop containers
    docker compose -f "${COMPOSE_FILE}" down
    
    log_success "Application stopped successfully!"
}

restart_application() {
    log_info "Restarting Supplement Tracker..."
    stop_application
    sleep 2
    start_application
}

show_status() {
    echo "=== Docker Containers ==="
    docker compose -f "${COMPOSE_FILE}" ps
    echo
    echo "=== Service Registration Status ==="
    "${SERVICE_MANAGER}" status
}

show_logs() {
    docker compose -f "${COMPOSE_FILE}" logs -f
}

build_application() {
    log_info "Building Supplement Tracker containers..."
    docker compose -f "${COMPOSE_FILE}" build
    log_success "Build complete!"
}

# Main command handling
case "${1:-}" in
    "start"|"up")
        start_application
        ;;
    "stop"|"down")
        stop_application
        ;;
    "restart")
        restart_application
        ;;
    "status"|"ps")
        show_status
        ;;
    "logs")
        show_logs
        ;;
    "register")
        "${SERVICE_MANAGER}" register
        ;;
    "unregister")
        "${SERVICE_MANAGER}" unregister
        ;;
    "test")
        "${SERVICE_MANAGER}" test
        ;;
    "build")
        build_application
        ;;
    "shell-backend")
        docker exec -it supplement-backend /bin/bash
        ;;
    "shell-frontend")
        docker exec -it supplement-frontend /bin/sh
        ;;
    "help"|"--help"|"-h"|"")
        show_help
        ;;
    *)
        echo "Unknown command: $1"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac
