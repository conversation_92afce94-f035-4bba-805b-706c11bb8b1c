"""
Research study management API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel

from ..database import get_db, ResearchStudy, ResearchParticipation

router = APIRouter(prefix="/research", tags=["research"])

# Pydantic models
class ResearchStudyBase(BaseModel):
    title: str
    description: str
    category: Optional[str] = None
    duration_weeks: Optional[int] = None
    max_participants: Optional[int] = None
    difficulty: Optional[str] = "beginner"
    compensation: Optional[str] = None
    requirements: Optional[str] = None
    researcher_name: Optional[str] = None
    institution: Optional[str] = None

class ResearchStudyCreate(ResearchStudyBase):
    pass

class ResearchStudyResponse(ResearchStudyBase):
    id: int
    current_participants: int
    status: str
    rating: float
    rating_count: int
    start_date: Optional[datetime] = None
    estimated_completion: Optional[datetime] = None
    created_at: datetime
    
    class Config:
        from_attributes = True

class ResearchParticipationBase(BaseModel):
    study_id: int

class ResearchParticipationCreate(ResearchParticipationBase):
    pass

class ResearchParticipationResponse(ResearchParticipationBase):
    id: int
    user_id: int
    joined_at: datetime
    status: str
    progress_percentage: float
    next_task: Optional[str] = None
    next_task_due: Optional[datetime] = None
    study: ResearchStudyResponse
    
    class Config:
        from_attributes = True

class StudyRating(BaseModel):
    rating: int
    feedback: Optional[str] = None

# Mock user for development
def get_current_user():
    return {"id": 1, "username": "demo_user", "email": "<EMAIL>"}

@router.get("/studies", response_model=List[ResearchStudyResponse])
async def get_research_studies(
    category: Optional[str] = None,
    status: Optional[str] = None,
    difficulty: Optional[str] = None,
    search: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Get available research studies with filtering"""
    query = db.query(ResearchStudy)
    
    if category:
        query = query.filter(ResearchStudy.category == category)
    
    if status:
        query = query.filter(ResearchStudy.status == status)
    
    if difficulty:
        query = query.filter(ResearchStudy.difficulty == difficulty)
    
    if search:
        query = query.filter(ResearchStudy.title.contains(search))
    
    studies = query.all()
    return studies

@router.get("/studies/{study_id}", response_model=ResearchStudyResponse)
async def get_research_study(
    study_id: int,
    db: Session = Depends(get_db)
):
    """Get detailed information about a specific study"""
    study = db.query(ResearchStudy).filter(ResearchStudy.id == study_id).first()
    if not study:
        raise HTTPException(status_code=404, detail="Study not found")
    return study

@router.post("/studies", response_model=ResearchStudyResponse)
async def create_research_study(
    study: ResearchStudyCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Create a new research study"""
    db_study = ResearchStudy(**study.dict())
    db.add(db_study)
    db.commit()
    db.refresh(db_study)
    return db_study

@router.post("/studies/{study_id}/join", response_model=ResearchParticipationResponse)
async def join_study(
    study_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Join a research study"""
    # Check if study exists
    study = db.query(ResearchStudy).filter(ResearchStudy.id == study_id).first()
    if not study:
        raise HTTPException(status_code=404, detail="Study not found")
    
    # Check if user is already participating
    existing_participation = db.query(ResearchParticipation).filter(
        ResearchParticipation.user_id == current_user["id"],
        ResearchParticipation.study_id == study_id,
        ResearchParticipation.status == "active"
    ).first()
    
    if existing_participation:
        raise HTTPException(status_code=400, detail="Already participating in this study")
    
    # Check if study has space
    if study.current_participants >= study.max_participants:
        raise HTTPException(status_code=400, detail="Study is full")
    
    # Create participation record
    participation = ResearchParticipation(
        user_id=current_user["id"],
        study_id=study_id,
        next_task="Complete initial survey",
        next_task_due=datetime.now().replace(hour=23, minute=59, second=59)
    )
    
    db.add(participation)
    
    # Update participant count
    study.current_participants += 1
    
    db.commit()
    db.refresh(participation)
    return participation

@router.post("/studies/{study_id}/leave")
async def leave_study(
    study_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Leave a research study"""
    participation = db.query(ResearchParticipation).filter(
        ResearchParticipation.user_id == current_user["id"],
        ResearchParticipation.study_id == study_id,
        ResearchParticipation.status == "active"
    ).first()
    
    if not participation:
        raise HTTPException(status_code=404, detail="Not participating in this study")
    
    participation.status = "withdrawn"
    
    # Update participant count
    study = db.query(ResearchStudy).filter(ResearchStudy.id == study_id).first()
    if study:
        study.current_participants = max(0, study.current_participants - 1)
    
    db.commit()
    return {"message": "Successfully left the study"}

@router.get("/my-participations", response_model=List[ResearchParticipationResponse])
async def get_my_participations(
    status: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Get user's research participations"""
    query = db.query(ResearchParticipation).filter(
        ResearchParticipation.user_id == current_user["id"]
    )
    
    if status:
        query = query.filter(ResearchParticipation.status == status)
    
    participations = query.all()
    return participations

@router.put("/participations/{participation_id}/progress")
async def update_participation_progress(
    participation_id: int,
    progress_percentage: float,
    next_task: Optional[str] = None,
    next_task_due: Optional[datetime] = None,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Update participation progress"""
    participation = db.query(ResearchParticipation).filter(
        ResearchParticipation.id == participation_id,
        ResearchParticipation.user_id == current_user["id"]
    ).first()
    
    if not participation:
        raise HTTPException(status_code=404, detail="Participation not found")
    
    participation.progress_percentage = min(100.0, max(0.0, progress_percentage))
    
    if next_task:
        participation.next_task = next_task
    
    if next_task_due:
        participation.next_task_due = next_task_due
    
    # Mark as completed if 100%
    if participation.progress_percentage >= 100.0:
        participation.status = "completed"
        participation.completion_date = datetime.now()
    
    db.commit()
    db.refresh(participation)
    return participation

@router.post("/studies/{study_id}/rate")
async def rate_study(
    study_id: int,
    rating_data: StudyRating,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Rate a completed study"""
    # Check if user participated in the study
    participation = db.query(ResearchParticipation).filter(
        ResearchParticipation.user_id == current_user["id"],
        ResearchParticipation.study_id == study_id,
        ResearchParticipation.status == "completed"
    ).first()
    
    if not participation:
        raise HTTPException(status_code=400, detail="Can only rate completed studies")
    
    # Update participation with rating
    participation.rating = rating_data.rating
    participation.feedback = rating_data.feedback
    
    # Update study's overall rating
    study = db.query(ResearchStudy).filter(ResearchStudy.id == study_id).first()
    if study:
        # Calculate new average rating
        total_rating = study.rating * study.rating_count + rating_data.rating
        study.rating_count += 1
        study.rating = total_rating / study.rating_count
    
    db.commit()
    return {"message": "Rating submitted successfully"}

@router.get("/dashboard/stats")
async def get_research_dashboard_stats(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Get research dashboard statistics"""
    # Active participations
    active_participations = db.query(ResearchParticipation).filter(
        ResearchParticipation.user_id == current_user["id"],
        ResearchParticipation.status == "active"
    ).count()
    
    # Completed studies
    completed_studies = db.query(ResearchParticipation).filter(
        ResearchParticipation.user_id == current_user["id"],
        ResearchParticipation.status == "completed"
    ).count()
    
    # Available studies
    available_studies = db.query(ResearchStudy).filter(
        ResearchStudy.status == "recruiting"
    ).count()
    
    return {
        "active_participations": active_participations,
        "completed_studies": completed_studies,
        "available_studies": available_studies,
        "research_points": completed_studies * 100  # Mock points system
    }
