"""
Supplement management API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, date
from pydantic import BaseModel

from database import get_db, User, Supplement, UserSupplement, IntakeLog

router = APIRouter(prefix="/supplements", tags=["supplements"])

# Pydantic models for request/response
class SupplementBase(BaseModel):
    name: str
    brand: Optional[str] = None
    category: Optional[str] = None
    description: Optional[str] = None
    default_dosage: Optional[str] = None
    dosage_unit: Optional[str] = None

class SupplementCreate(SupplementBase):
    pass

class SupplementResponse(SupplementBase):
    id: int
    evidence_level: Optional[str] = None
    created_at: datetime
    
    class Config:
        from_attributes = True

class UserSupplementBase(BaseModel):
    supplement_id: int
    custom_name: Optional[str] = None
    dosage: str
    frequency: str
    time_of_day: Optional[str] = None
    notes: Optional[str] = None

class UserSupplementCreate(UserSupplementBase):
    pass

class UserSupplementResponse(UserSupplementBase):
    id: int
    user_id: int
    is_active: bool
    start_date: datetime
    supplement: SupplementResponse
    
    class Config:
        from_attributes = True

class IntakeLogBase(BaseModel):
    user_supplement_id: int
    taken_at: datetime
    dosage_taken: Optional[str] = None
    notes: Optional[str] = None
    mood_before: Optional[int] = None
    mood_after: Optional[int] = None

class IntakeLogCreate(IntakeLogBase):
    pass

class IntakeLogResponse(IntakeLogBase):
    id: int
    user_id: int
    created_at: datetime
    
    class Config:
        from_attributes = True

class DashboardStats(BaseModel):
    today_intakes: int
    active_supplements: int
    current_streak: int
    weekly_compliance: float

# Mock user for development (replace with real authentication)
def get_current_user():
    return {"id": 1, "username": "demo_user", "email": "<EMAIL>"}

@router.get("/catalog", response_model=List[SupplementResponse])
async def get_supplement_catalog(
    category: Optional[str] = None,
    search: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Get supplement catalog with optional filtering"""
    query = db.query(Supplement)
    
    if category:
        query = query.filter(Supplement.category == category)
    
    if search:
        query = query.filter(Supplement.name.contains(search))
    
    supplements = query.all()
    return supplements

@router.post("/catalog", response_model=SupplementResponse)
async def create_supplement(
    supplement: SupplementCreate,
    db: Session = Depends(get_db)
):
    """Create a new supplement in the catalog"""
    db_supplement = Supplement(**supplement.dict())
    db.add(db_supplement)
    db.commit()
    db.refresh(db_supplement)
    return db_supplement

@router.get("/my-supplements", response_model=List[UserSupplementResponse])
async def get_user_supplements(
    active_only: bool = True,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Get user's personal supplement schedule"""
    query = db.query(UserSupplement).filter(UserSupplement.user_id == current_user["id"])
    
    if active_only:
        query = query.filter(UserSupplement.is_active == True)
    
    user_supplements = query.all()
    return user_supplements

@router.post("/my-supplements", response_model=UserSupplementResponse)
async def add_user_supplement(
    user_supplement: UserSupplementCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Add a supplement to user's schedule"""
    db_user_supplement = UserSupplement(
        user_id=current_user["id"],
        **user_supplement.dict()
    )
    db.add(db_user_supplement)
    db.commit()
    db.refresh(db_user_supplement)
    return db_user_supplement

@router.put("/my-supplements/{supplement_id}")
async def update_user_supplement(
    supplement_id: int,
    user_supplement: UserSupplementBase,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Update user's supplement schedule"""
    db_supplement = db.query(UserSupplement).filter(
        UserSupplement.id == supplement_id,
        UserSupplement.user_id == current_user["id"]
    ).first()
    
    if not db_supplement:
        raise HTTPException(status_code=404, detail="Supplement not found")
    
    for key, value in user_supplement.dict(exclude_unset=True).items():
        setattr(db_supplement, key, value)
    
    db.commit()
    db.refresh(db_supplement)
    return db_supplement

@router.delete("/my-supplements/{supplement_id}")
async def remove_user_supplement(
    supplement_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Remove supplement from user's schedule"""
    db_supplement = db.query(UserSupplement).filter(
        UserSupplement.id == supplement_id,
        UserSupplement.user_id == current_user["id"]
    ).first()
    
    if not db_supplement:
        raise HTTPException(status_code=404, detail="Supplement not found")
    
    db_supplement.is_active = False
    db.commit()
    return {"message": "Supplement removed successfully"}

@router.post("/intake", response_model=IntakeLogResponse)
async def log_intake(
    intake: IntakeLogCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Log supplement intake"""
    # Verify the user supplement belongs to the current user
    user_supplement = db.query(UserSupplement).filter(
        UserSupplement.id == intake.user_supplement_id,
        UserSupplement.user_id == current_user["id"]
    ).first()
    
    if not user_supplement:
        raise HTTPException(status_code=404, detail="User supplement not found")
    
    db_intake = IntakeLog(
        user_id=current_user["id"],
        **intake.dict()
    )
    db.add(db_intake)
    db.commit()
    db.refresh(db_intake)
    return db_intake

@router.get("/intake/today", response_model=List[IntakeLogResponse])
async def get_today_intake(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Get today's intake logs"""
    today = date.today()
    intake_logs = db.query(IntakeLog).filter(
        IntakeLog.user_id == current_user["id"],
        IntakeLog.taken_at >= today
    ).all()
    return intake_logs

@router.get("/dashboard/stats", response_model=DashboardStats)
async def get_dashboard_stats(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Get dashboard statistics"""
    today = date.today()
    
    # Today's intakes
    today_intakes = db.query(IntakeLog).filter(
        IntakeLog.user_id == current_user["id"],
        IntakeLog.taken_at >= today
    ).count()
    
    # Active supplements
    active_supplements = db.query(UserSupplement).filter(
        UserSupplement.user_id == current_user["id"],
        UserSupplement.is_active == True
    ).count()
    
    # Mock data for now (implement real calculations later)
    current_streak = 15
    weekly_compliance = 85.0
    
    return DashboardStats(
        today_intakes=today_intakes,
        active_supplements=active_supplements,
        current_streak=current_streak,
        weekly_compliance=weekly_compliance
    )

@router.get("/due-today")
async def get_supplements_due_today(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Get supplements due today that haven't been taken"""
    today = date.today()
    
    # Get all active user supplements
    user_supplements = db.query(UserSupplement).filter(
        UserSupplement.user_id == current_user["id"],
        UserSupplement.is_active == True
    ).all()
    
    # Check which ones haven't been taken today
    due_supplements = []
    for user_supplement in user_supplements:
        today_intake = db.query(IntakeLog).filter(
            IntakeLog.user_supplement_id == user_supplement.id,
            IntakeLog.taken_at >= today
        ).first()
        
        if not today_intake:
            due_supplements.append({
                "id": user_supplement.id,
                "name": user_supplement.custom_name or user_supplement.supplement.name,
                "brand": user_supplement.supplement.brand,
                "dosage": user_supplement.dosage,
                "time_of_day": user_supplement.time_of_day,
                "frequency": user_supplement.frequency
            })
    
    return due_supplements
