"""
Supplement management API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, date
from pydantic import BaseModel

from database import get_db, User, Supplement, UserSupplement, IntakeLog
from auth import get_current_active_user

router = APIRouter(prefix="/supplements", tags=["supplements"])

# Pydantic models for request/response
class SupplementBase(BaseModel):
    name: str
    brand: Optional[str] = None
    category: Optional[str] = None
    description: Optional[str] = None
    default_dosage: Optional[str] = None
    dosage_unit: Optional[str] = None

class SupplementCreate(SupplementBase):
    pass

class SupplementResponse(SupplementBase):
    id: int
    evidence_level: Optional[str] = None
    created_at: datetime
    
    class Config:
        from_attributes = True

class UserSupplementBase(BaseModel):
    supplement_id: int
    custom_name: Optional[str] = None
    dosage: str
    frequency: str
    time_of_day: Optional[str] = None
    notes: Optional[str] = None

class UserSupplementCreate(UserSupplementBase):
    pass

class UserSupplementResponse(UserSupplementBase):
    id: int
    user_id: int
    is_active: bool
    start_date: datetime
    supplement: SupplementResponse
    
    class Config:
        from_attributes = True

class IntakeLogBase(BaseModel):
    user_supplement_id: int
    taken_at: datetime
    dosage_taken: Optional[str] = None
    notes: Optional[str] = None
    mood_before: Optional[int] = None
    mood_after: Optional[int] = None

class IntakeLogCreate(IntakeLogBase):
    pass

class IntakeLogResponse(IntakeLogBase):
    id: int
    user_id: int
    created_at: datetime
    
    class Config:
        from_attributes = True

class DashboardStats(BaseModel):
    today_intakes: int
    active_supplements: int
    current_streak: int
    weekly_compliance: float

# Supplement catalog endpoints
@router.get("/catalog", response_model=List[SupplementResponse])
async def get_supplement_catalog(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None,
    category: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Get supplement catalog with search and filtering"""
    query = db.query(Supplement)
    
    if search:
        query = query.filter(
            Supplement.name.ilike(f"%{search}%") |
            Supplement.brand.ilike(f"%{search}%") |
            Supplement.description.ilike(f"%{search}%")
        )
    
    if category:
        query = query.filter(Supplement.category == category)
    
    supplements = query.offset(skip).limit(limit).all()
    return supplements

@router.get("/catalog/{supplement_id}", response_model=SupplementResponse)
async def get_supplement_details(
    supplement_id: int,
    db: Session = Depends(get_db)
):
    """Get specific supplement details"""
    supplement = db.query(Supplement).filter(Supplement.id == supplement_id).first()
    
    if not supplement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Supplement not found"
        )
    
    return supplement

@router.post("/catalog", response_model=SupplementResponse, status_code=status.HTTP_201_CREATED)
async def create_supplement(
    supplement_data: SupplementCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Create a new supplement in the catalog"""
    # Check if supplement already exists
    existing = db.query(Supplement).filter(
        (Supplement.name == supplement_data.name) &
        (Supplement.brand == supplement_data.brand)
    ).first()
    
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Supplement with this name and brand already exists"
        )
    
    supplement = Supplement(**supplement_data.dict())
    db.add(supplement)
    db.commit()
    db.refresh(supplement)
    
    return supplement

# User supplement management
@router.get("/my-supplements", response_model=List[UserSupplementResponse])
async def get_my_supplements(
    active_only: bool = True,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get current user's supplement schedule"""
    query = db.query(UserSupplement).filter(UserSupplement.user_id == current_user.id)
    
    if active_only:
        query = query.filter(UserSupplement.is_active == True)
    
    user_supplements = query.all()
    return user_supplements

@router.post("/my-supplements", response_model=UserSupplementResponse, status_code=status.HTTP_201_CREATED)
async def add_supplement_to_schedule(
    supplement_data: UserSupplementCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Add a supplement to user's schedule"""
    # Verify supplement exists
    supplement = db.query(Supplement).filter(Supplement.id == supplement_data.supplement_id).first()
    if not supplement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Supplement not found"
        )
    
    # Check if user already has this supplement in their schedule
    existing = db.query(UserSupplement).filter(
        (UserSupplement.user_id == current_user.id) &
        (UserSupplement.supplement_id == supplement_data.supplement_id) &
        (UserSupplement.is_active == True)
    ).first()
    
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Supplement already in your active schedule"
        )
    
    user_supplement = UserSupplement(
        user_id=current_user.id,
        **supplement_data.dict()
    )
    
    db.add(user_supplement)
    db.commit()
    db.refresh(user_supplement)
    
    return user_supplement

@router.put("/my-supplements/{user_supplement_id}", response_model=UserSupplementResponse)
async def update_supplement_schedule(
    user_supplement_id: int,
    supplement_data: UserSupplementCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Update user's supplement schedule"""
    user_supplement = db.query(UserSupplement).filter(
        (UserSupplement.id == user_supplement_id) &
        (UserSupplement.user_id == current_user.id)
    ).first()
    
    if not user_supplement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Supplement schedule not found"
        )
    
    for field, value in supplement_data.dict(exclude_unset=True).items():
        setattr(user_supplement, field, value)
    
    db.commit()
    db.refresh(user_supplement)
    
    return user_supplement

@router.delete("/my-supplements/{user_supplement_id}")
async def remove_supplement_from_schedule(
    user_supplement_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Remove supplement from user's schedule (soft delete)"""
    user_supplement = db.query(UserSupplement).filter(
        (UserSupplement.id == user_supplement_id) &
        (UserSupplement.user_id == current_user.id)
    ).first()
    
    if not user_supplement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Supplement schedule not found"
        )
    
    user_supplement.is_active = False
    db.commit()
    
    return {"message": "Supplement removed from schedule"}
