"""
Supplement Tracker Backend API
FastAPI application for supplement tracking and health metrics
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import os

# Import database and routers (temporarily commented out)
# from database import create_tables
# from routers import supplements, research

# Create FastAPI app
app = FastAPI(
    title="Supplement Tracker API",
    description="API for tracking supplements, health metrics, and research protocols",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Create database tables on startup (temporarily commented out)
# create_tables()

# Include routers (temporarily commented out)
# app.include_router(supplements.router, prefix="/api/v1")
# app.include_router(research.router, prefix="/api/v1")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://app.pills.localhost",
        "https://app.pills.localhost",
        "http://localhost:3000",
        "http://127.0.0.1:3000"
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Health check endpoint
@app.get("/health/")
async def health_check():
    """Health check endpoint for monitoring"""
    return {
        "status": "healthy",
        "service": "supplement-tracker-api",
        "version": "1.0.0"
    }

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Supplement Tracker API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health/"
    }

# API endpoints
@app.get("/api/v1/supplements/")
async def get_supplements():
    """Get all supplements"""
    return {
        "supplements": [
            {
                "id": 1,
                "name": "Vitamin D3",
                "brand": "Nature Made",
                "dosage": "2000 IU",
                "frequency": "daily",
                "category": "vitamins"
            },
            {
                "id": 2,
                "name": "Omega-3",
                "brand": "Nordic Naturals",
                "dosage": "1000mg",
                "frequency": "daily",
                "category": "omega3"
            }
        ]
    }

@app.get("/api/v1/tracking/")
async def get_tracking_data():
    """Get tracking data"""
    return {
        "tracking": {
            "today_taken": 3,
            "today_scheduled": 5,
            "adherence_rate": 85.5,
            "current_streak": 12
        }
    }

@app.get("/api/v1/health/")
async def get_health_metrics():
    """Get health metrics"""
    return {
        "health_metrics": {
            "heart_rate": 72,
            "blood_pressure": "120/80",
            "weight": 70.5,
            "sleep_hours": 7.5,
            "steps": 8500
        }
    }

@app.get("/api/v1/analytics/")
async def get_analytics():
    """Get analytics data"""
    return {
        "analytics": {
            "total_supplements": 15,
            "total_intakes": 342,
            "avg_adherence": 87.3,
            "most_consistent": "Vitamin D3"
        }
    }

# Error handlers
@app.exception_handler(404)
async def not_found_handler(request, exc):
    return JSONResponse(
        status_code=404,
        content={"message": "Endpoint not found"}
    )

@app.exception_handler(500)
async def internal_error_handler(request, exc):
    return JSONResponse(
        status_code=500,
        content={"message": "Internal server error"}
    )

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
