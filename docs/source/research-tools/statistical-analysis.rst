Statistical Analysis
===================

The Statistical Analysis module provides comprehensive tools for data analysis, correlation discovery, and statistical inference, enabling researchers to extract meaningful insights from supplement research data.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
--------

Statistical analysis transforms raw research data into actionable insights and evidence-based conclusions. The system provides:

* **Multiple Analysis Types**: Correlation, regression, hypothesis testing, and more
* **Automated Workflows**: Guided analysis selection and execution
* **Professional Results**: Publication-ready statistical outputs
* **Visualization Tools**: Interactive charts and graphs
* **Interpretation Support**: Statistical guidance and recommendations
* **Reproducible Research**: Complete analysis documentation and version control

Analysis Workflow
------------------

The statistical analysis workflow guides researchers through the complete process from data preparation to results interpretation.

.. mermaid::

   flowchart TD
       START([Begin Analysis]) --> DATA[Data Preparation]
       DATA --> EXPLORE[Exploratory Analysis]
       EXPLORE --> SELECT[Select Analysis Type]
       SELECT --> CONFIG[Configure Parameters]
       CONFIG --> VALIDATE[Validate Assumptions]
       VALIDATE --> EXECUTE[Execute Analysis]
       EXECUTE --> RESULTS[Generate Results]
       RESULTS --> INTERPRET[Interpret Findings]
       INTERPRET --> VISUALIZE[Create Visualizations]
       VISUALIZE --> REPORT[Generate Report]
       REPORT --> REVIEW[Peer Review]
       REVIEW --> PUBLISH[Publish Results]
       PUBLISH --> END([Analysis Complete])
       
       VALIDATE -->|Assumptions Violated| TRANSFORM[Data Transformation]
       TRANSFORM --> VALIDATE
       
       REVIEW -->|Revisions Needed| INTERPRET
       
       style START fill:#4caf50
       style END fill:#2196f3
       style SELECT fill:#ff9800
       style VALIDATE fill:#9c27b0
       style RESULTS fill:#e91e63

**Workflow Stages**:

1. **Data Preparation**: Cleaning, validation, and preprocessing
2. **Exploratory Analysis**: Descriptive statistics and data visualization
3. **Analysis Selection**: Choosing appropriate statistical methods
4. **Parameter Configuration**: Setting analysis parameters and options
5. **Assumption Validation**: Checking statistical assumptions
6. **Execution**: Running the statistical analysis
7. **Results Generation**: Producing statistical outputs and summaries
8. **Interpretation**: Understanding and explaining findings
9. **Visualization**: Creating charts and graphs
10. **Reporting**: Generating comprehensive analysis reports

Analysis Types
--------------

The system supports multiple statistical analysis types for different research questions and data types.

.. mermaid::

   graph TD
       subgraph "Descriptive Statistics"
           DS1[Summary Statistics]
           DS2[Distribution Analysis]
           DS3[Frequency Tables]
           DS4[Cross-tabulations]
       end
       
       subgraph "Correlation Analysis"
           CA1[Pearson Correlation]
           CA2[Spearman Correlation]
           CA3[Kendall's Tau]
           CA4[Partial Correlation]
       end
       
       subgraph "Hypothesis Testing"
           HT1[T-tests]
           HT2[Chi-square Tests]
           HT3[ANOVA]
           HT4[Non-parametric Tests]
       end
       
       subgraph "Regression Analysis"
           RA1[Linear Regression]
           RA2[Logistic Regression]
           RA3[Multiple Regression]
           RA4[Polynomial Regression]
       end
       
       subgraph "Advanced Methods"
           AM1[Time Series Analysis]
           AM2[Survival Analysis]
           AM3[Mixed Effects Models]
           AM4[Machine Learning]
       end
       
       style DS1 fill:#e3f2fd
       style CA1 fill:#f3e5f5
       style HT1 fill:#e8f5e8
       style RA1 fill:#fff3e0
       style AM1 fill:#fce4ec

Correlation Analysis
~~~~~~~~~~~~~~~~~~~~

**Purpose**: Examine relationships between variables to identify patterns and associations.

**Supported Methods**:
* **Pearson Correlation**: Linear relationships between continuous variables
* **Spearman Correlation**: Monotonic relationships and ordinal data
* **Kendall's Tau**: Rank-based correlation for small samples
* **Partial Correlation**: Controlling for confounding variables

**Example Analysis**:

.. code-block:: python

   # Correlation analysis configuration
   correlation_request = {
       "analysis_type": "correlation",
       "method": "pearson",
       "variables": {
           "x": "magnesium_dosage",
           "y": "sleep_quality_score"
       },
       "confidence_level": 0.95,
       "filter_criteria": {
           "age_range": [25, 65],
           "study_duration": ">=8_weeks"
       }
   }
   
   # Execute analysis
   response = requests.post(
       "https://api.supplement-tracker.com/v1/research/analyses",
       json=correlation_request,
       headers={"Authorization": "Bearer YOUR_TOKEN"}
   )

**Results Interpretation**:
* **Correlation Coefficient (r)**: Strength and direction of relationship
* **P-value**: Statistical significance of the correlation
* **Confidence Interval**: Range of plausible correlation values
* **Effect Size**: Practical significance of the relationship

Regression Analysis
~~~~~~~~~~~~~~~~~~~

**Purpose**: Model relationships between dependent and independent variables for prediction and explanation.

**Regression Types**:

.. list-table:: Regression Analysis Options
   :header-rows: 1
   :widths: 25 35 40

   * - Type
     - Use Case
     - Example
   * - **Linear Regression**
     - Continuous outcome, linear relationship
     - Sleep quality vs. magnesium dose
   * - **Logistic Regression**
     - Binary outcome (yes/no)
     - Supplement effectiveness (effective/not)
   * - **Multiple Regression**
     - Multiple predictors
     - Sleep quality vs. dose, age, BMI
   * - **Polynomial Regression**
     - Non-linear relationships
     - Dose-response curves

**Regression Workflow**:

.. code-block:: python

   # Multiple regression analysis
   regression_request = {
       "analysis_type": "multiple_regression",
       "dependent_variable": "sleep_quality_improvement",
       "independent_variables": [
           "magnesium_dosage",
           "baseline_sleep_score",
           "age",
           "bmi"
       ],
       "model_type": "linear",
       "confidence_level": 0.95,
       "include_interactions": False
   }

Hypothesis Testing
~~~~~~~~~~~~~~~~~~

**Purpose**: Test specific research hypotheses using appropriate statistical tests.

**Test Selection Decision Tree**:

.. mermaid::

   graph TD
       START([Research Question]) --> DATA_TYPE{Data Type?}
       
       DATA_TYPE -->|Continuous| GROUPS{Number of Groups?}
       DATA_TYPE -->|Categorical| CHI_SQUARE[Chi-square Test]
       DATA_TYPE -->|Ordinal| NONPARAM[Non-parametric Tests]
       
       GROUPS -->|1 Group| ONE_SAMPLE[One-sample t-test]
       GROUPS -->|2 Groups| TWO_GROUPS{Independent or Paired?}
       GROUPS -->|3+ Groups| ANOVA_TYPE{ANOVA Type?}
       
       TWO_GROUPS -->|Independent| INDEPENDENT_T[Independent t-test]
       TWO_GROUPS -->|Paired| PAIRED_T[Paired t-test]
       
       ANOVA_TYPE -->|One Factor| ONE_WAY[One-way ANOVA]
       ANOVA_TYPE -->|Multiple Factors| FACTORIAL[Factorial ANOVA]
       ANOVA_TYPE -->|Repeated Measures| REPEATED[Repeated Measures ANOVA]
       
       style START fill:#4caf50
       style DATA_TYPE fill:#ff9800
       style GROUPS fill:#9c27b0
       style TWO_GROUPS fill:#e91e63

**Common Hypothesis Tests**:

1. **T-tests**
   * One-sample: Compare sample mean to population value
   * Independent: Compare means between two groups
   * Paired: Compare before/after measurements

2. **ANOVA**
   * One-way: Compare means across multiple groups
   * Factorial: Multiple factors and interactions
   * Repeated measures: Within-subject comparisons

3. **Chi-square Tests**
   * Goodness of fit: Test distribution assumptions
   * Independence: Test association between variables

4. **Non-parametric Tests**
   * Mann-Whitney U: Non-parametric alternative to t-test
   * Kruskal-Wallis: Non-parametric alternative to ANOVA
   * Wilcoxon: Non-parametric paired comparison

Statistical Assumptions
-----------------------

Proper statistical analysis requires validation of underlying assumptions for each test type.

**Common Assumptions**:

.. list-table:: Statistical Assumptions by Test Type
   :header-rows: 1
   :widths: 25 75

   * - Test Type
     - Key Assumptions
   * - **T-tests**
     - Normality, independence, equal variances (for independent t-test)
   * - **ANOVA**
     - Normality, independence, homogeneity of variance
   * - **Correlation**
     - Linearity (Pearson), independence, normality for significance tests
   * - **Regression**
     - Linearity, independence, normality of residuals, homoscedasticity
   * - **Chi-square**
     - Independence, expected frequencies ≥5 in each cell

**Assumption Testing**:

.. code-block:: python

   # Automatic assumption testing
   assumption_tests = {
       "normality": {
           "test": "shapiro_wilk",
           "variables": ["sleep_quality_score", "magnesium_level"]
       },
       "homogeneity": {
           "test": "levene",
           "groups": "treatment_group"
       },
       "independence": {
           "test": "durbin_watson",
           "time_variable": "measurement_date"
       }
   }

Results Visualization
---------------------

Comprehensive visualization tools help communicate statistical findings effectively.

.. mermaid::

   graph LR
       subgraph "Basic Plots"
           BP1[Histograms]
           BP2[Box Plots]
           BP3[Scatter Plots]
           BP4[Bar Charts]
       end
       
       subgraph "Statistical Plots"
           SP1[Q-Q Plots]
           SP2[Residual Plots]
           SP3[Correlation Matrices]
           SP4[Forest Plots]
       end
       
       subgraph "Advanced Visualizations"
           AV1[Heatmaps]
           AV2[Violin Plots]
           AV3[Regression Lines]
           AV4[Confidence Bands]
       end
       
       subgraph "Interactive Features"
           IF1[Zoom and Pan]
           IF2[Data Tooltips]
           IF3[Filter Controls]
           IF4[Export Options]
       end
       
       BP1 --> SP1
       BP2 --> SP2
       BP3 --> SP3
       BP4 --> SP4
       
       SP1 --> AV1
       SP2 --> AV2
       SP3 --> AV3
       SP4 --> AV4
       
       AV1 --> IF1
       AV2 --> IF2
       AV3 --> IF3
       AV4 --> IF4

**Visualization Types**:

1. **Exploratory Plots**
   * Histograms for distribution assessment
   * Box plots for outlier detection
   * Scatter plots for relationship exploration
   * Correlation heatmaps for variable relationships

2. **Diagnostic Plots**
   * Q-Q plots for normality assessment
   * Residual plots for regression diagnostics
   * Leverage plots for influential observations
   * Cook's distance for outlier identification

3. **Results Presentation**
   * Forest plots for effect sizes and confidence intervals
   * Regression plots with confidence bands
   * ANOVA interaction plots
   * Before/after comparison plots

Report Generation
-----------------

Automated report generation produces comprehensive, publication-ready statistical reports.

**Report Components**:

.. mermaid::

   sequenceDiagram
       participant U as User
       participant S as System
       participant A as Analysis Engine
       participant R as Report Generator
       participant V as Visualization Engine
       
       U->>S: Request Analysis Report
       S->>A: Execute Statistical Analysis
       A->>A: Perform Calculations
       A->>S: Return Results
       S->>V: Generate Visualizations
       V->>S: Return Charts and Plots
       S->>R: Compile Report Components
       R->>R: Format Report
       R->>S: Return Formatted Report
       S->>U: Deliver Complete Report

**Report Sections**:

1. **Executive Summary**
   * Key findings and conclusions
   * Statistical significance summary
   * Clinical/practical significance
   * Recommendations for action

2. **Methods**
   * Data description and preprocessing
   * Statistical methods used
   * Assumption testing results
   * Analysis parameters and settings

3. **Results**
   * Descriptive statistics
   * Statistical test results
   * Effect sizes and confidence intervals
   * Tables and figures

4. **Discussion**
   * Interpretation of findings
   * Limitations and considerations
   * Comparison with existing literature
   * Implications for practice

5. **Technical Appendix**
   * Detailed statistical output
   * Diagnostic plots and tests
   * Data quality assessments
   * Reproducibility information

Power Analysis
--------------

Statistical power analysis helps determine appropriate sample sizes and interpret non-significant results.

**Power Analysis Components**:
* **Effect Size**: Magnitude of the difference or relationship
* **Sample Size**: Number of observations in the analysis
* **Significance Level (α)**: Type I error rate (typically 0.05)
* **Statistical Power (1-β)**: Probability of detecting a true effect

**Power Calculation Example**:

.. code-block:: python

   # Power analysis for correlation study
   from scipy import stats
   import numpy as np
   
   def power_analysis_correlation(effect_size, n, alpha=0.05):
       """Calculate statistical power for correlation analysis."""
       # Transform correlation to Fisher's z
       z_r = 0.5 * np.log((1 + effect_size) / (1 - effect_size))
       
       # Standard error
       se = 1 / np.sqrt(n - 3)
       
       # Critical value
       z_crit = stats.norm.ppf(1 - alpha/2)
       
       # Power calculation
       z_beta = (abs(z_r) - z_crit * se) / se
       power = stats.norm.cdf(z_beta)
       
       return power
   
   # Example: Power for detecting r=0.3 with n=100
   power = power_analysis_correlation(effect_size=0.3, n=100)
   print(f"Statistical power: {power:.3f}")

API Integration
---------------

Complete API access for statistical analysis functionality.

**Key Endpoints**:

.. code-block:: http

   POST   /api/v1/research/analyses                       # Create analysis
   GET    /api/v1/research/analyses                       # List analyses
   GET    /api/v1/research/analyses/{id}                  # Get analysis
   PUT    /api/v1/research/analyses/{id}                  # Update analysis
   DELETE /api/v1/research/analyses/{id}                  # Delete analysis
   POST   /api/v1/research/analyses/{id}/execute          # Execute analysis
   GET    /api/v1/research/analyses/{id}/results          # Get results
   GET    /api/v1/research/analyses/{id}/visualizations   # Get plots

**Analysis Execution Example**:

.. code-block:: python

   # Create and execute statistical analysis
   analysis_data = {
       "title": "Magnesium Dosage and Sleep Quality Correlation",
       "description": "Examining the relationship between magnesium supplementation dosage and sleep quality improvements",
       "analysis_type": "correlation",
       "data_source": "participant_data",
       "variables": {
           "x": "daily_magnesium_mg",
           "y": "sleep_quality_change"
       },
       "parameters": {
           "method": "pearson",
           "confidence_level": 0.95,
           "two_tailed": True
       }
   }
   
   # Create analysis
   response = requests.post(
       "https://api.supplement-tracker.com/v1/research/analyses",
       json=analysis_data,
       headers={"Authorization": "Bearer YOUR_TOKEN"}
   )
   
   analysis_id = response.json()["id"]
   
   # Execute analysis
   execution_response = requests.post(
       f"https://api.supplement-tracker.com/v1/research/analyses/{analysis_id}/execute",
       headers={"Authorization": "Bearer YOUR_TOKEN"}
   )

Best Practices
--------------

**Analysis Planning**:
* Define research questions and hypotheses before analysis
* Choose appropriate statistical methods for data type and research design
* Plan for multiple comparison corrections when appropriate
* Consider effect size and practical significance, not just statistical significance

**Data Quality**:
* Perform thorough exploratory data analysis
* Check for missing data patterns and handle appropriately
* Identify and address outliers and influential observations
* Validate data entry and coding accuracy

**Statistical Rigor**:
* Test and report assumption violations
* Use appropriate corrections for assumption violations
* Report confidence intervals along with p-values
* Consider Bayesian approaches for small samples or complex models

**Reproducibility**:
* Document all analysis decisions and parameters
* Use version control for analysis scripts and data
* Provide sufficient detail for replication
* Share analysis code and data when possible

**Interpretation**:
* Distinguish between statistical and practical significance
* Consider alternative explanations for findings
* Acknowledge limitations and potential biases
* Place results in context of existing literature

Next Steps
----------

* :doc:`collaboration` - Set up research team collaboration
* :doc:`api-reference` - Technical API documentation
* :doc:`overview` - Return to Research Tools overview
* :doc:`protocol-management` - Learn about research protocol design

The Statistical Analysis module provides professional-grade analytical capabilities that enable researchers to extract meaningful insights from supplement research data while maintaining statistical rigor and reproducibility.
