==============
API Endpoints
==============

This section provides comprehensive documentation for all Community Features API endpoints. The API follows RESTful principles and uses JSON for data exchange, with comprehensive error handling and authentication requirements.

Base URL and Authentication
===========================

**Base URL:** ``https://api.supplement-tracker.com/api/v1/community``

**Authentication:** All endpoints require Bearer token authentication:

.. code-block:: http

   Authorization: Bearer <your-access-token>

**Content Type:** All POST/PUT requests require:

.. code-block:: http

   Content-Type: application/json

Social Connections API
=====================

Follow Management
----------------

**Follow a User**

.. code-block:: http

   POST /follow
   Content-Type: application/json
   Authorization: Bearer <token>

   {
     "followed_id": "123e4567-e89b-12d3-a456-************"
   }

**Response (201 Created):**

.. code-block:: json

   {
     "id": "987fcdeb-51a2-43d1-9f4e-123456789abc",
     "follower_id": "456e7890-e89b-12d3-a456-************",
     "followed_id": "123e4567-e89b-12d3-a456-************",
     "created_at": "2025-06-17T22:30:00Z"
   }

**Error Responses:**

.. code-block:: json

   // 400 Bad Request - Already following
   {
     "detail": "Already following this user"
   }

   // 400 Bad Request - Self-follow attempt
   {
     "detail": "Cannot follow yourself"
   }

**Unfollow a User**

.. code-block:: http

   DELETE /follow/{user_id}
   Authorization: Bearer <token>

**Response (200 OK):**

.. code-block:: json

   {
     "message": "Successfully unfollowed user"
   }

**Error Response:**

.. code-block:: json

   // 404 Not Found
   {
     "detail": "Follow relationship not found"
   }

**Get User Followers**

.. code-block:: http

   GET /followers/{user_id}?skip=0&limit=20
   Authorization: Bearer <token>

**Response (200 OK):**

.. code-block:: json

   [
     {
       "id": "987fcdeb-51a2-43d1-9f4e-123456789abc",
       "follower_id": "456e7890-e89b-12d3-a456-************",
       "followed_id": "123e4567-e89b-12d3-a456-************",
       "created_at": "2025-06-17T22:30:00Z"
     }
   ]

**Get Users Following**

.. code-block:: http

   GET /following/{user_id}?skip=0&limit=20
   Authorization: Bearer <token>

**Response (200 OK):**

.. code-block:: json

   [
     {
       "id": "123fcdeb-51a2-43d1-9f4e-987456789abc",
       "follower_id": "123e4567-e89b-12d3-a456-************",
       "followed_id": "789e1234-e89b-12d3-a456-************",
       "created_at": "2025-06-17T21:15:00Z"
     }
   ]

Community Groups API
===================

Group Management
---------------

**Create Community Group**

.. code-block:: http

   POST /groups
   Content-Type: application/json
   Authorization: Bearer <token>

   {
     "name": "Magnesium Research",
     "description": "Dedicated to discussing magnesium supplements, research, and experiences",
     "category": "Minerals",
     "is_public": true,
     "is_moderated": false
   }

**Response (201 Created):**

.. code-block:: json

   {
     "id": "123e4567-e89b-12d3-a456-************",
     "name": "Magnesium Research",
     "description": "Dedicated to discussing magnesium supplements, research, and experiences",
     "category": "Minerals",
     "is_public": true,
     "is_moderated": false,
     "member_count": 1,
     "created_by_user_id": "456e7890-e89b-12d3-a456-************",
     "created_at": "2025-06-17T22:30:00Z",
     "updated_at": "2025-06-17T22:30:00Z"
   }

**List Community Groups**

.. code-block:: http

   GET /groups?skip=0&limit=20&category=Minerals
   Authorization: Bearer <token>

**Response (200 OK):**

.. code-block:: json

   [
     {
       "id": "123e4567-e89b-12d3-a456-************",
       "name": "Magnesium Research",
       "description": "Dedicated to discussing magnesium supplements",
       "category": "Minerals",
       "is_public": true,
       "is_moderated": false,
       "member_count": 25,
       "created_by_user_id": "456e7890-e89b-12d3-a456-************",
       "created_at": "2025-06-17T22:30:00Z",
       "updated_at": "2025-06-17T22:30:00Z"
     }
   ]

**Get Specific Group**

.. code-block:: http

   GET /groups/{group_id}
   Authorization: Bearer <token>

**Update Group**

.. code-block:: http

   PUT /groups/{group_id}
   Content-Type: application/json
   Authorization: Bearer <token>

   {
     "description": "Updated description with more details about our research focus",
     "is_moderated": true
   }

**Delete Group**

.. code-block:: http

   DELETE /groups/{group_id}
   Authorization: Bearer <token>

**Response (200 OK):**

.. code-block:: json

   {
     "message": "Group deleted successfully"
   }

Discussion Posts API
===================

Post Management
--------------

**Create Discussion Post**

.. code-block:: http

   POST /posts
   Content-Type: application/json
   Authorization: Bearer <token>

   {
     "title": "Magnesium Glycinate vs Magnesium Oxide - Personal Experience",
     "content": "I've been experimenting with different forms of magnesium for sleep quality. Here are my detailed findings over 3 months...",
     "post_type": "experience",
     "group_id": "123e4567-e89b-12d3-a456-************"
   }

**Response (201 Created):**

.. code-block:: json

   {
     "id": "789e1234-e89b-12d3-a456-************",
     "title": "Magnesium Glycinate vs Magnesium Oxide - Personal Experience",
     "content": "I've been experimenting with different forms of magnesium...",
     "post_type": "experience",
     "group_id": "123e4567-e89b-12d3-a456-************",
     "author_id": "456e7890-e89b-12d3-a456-************",
     "is_pinned": false,
     "is_locked": false,
     "view_count": 0,
     "like_count": 0,
     "comment_count": 0,
     "created_at": "2025-06-17T22:35:00Z",
     "updated_at": "2025-06-17T22:35:00Z"
   }

**List Discussion Posts**

.. code-block:: http

   GET /posts?skip=0&limit=20&group_id=123e4567&post_type=experience&author_id=456e7890
   Authorization: Bearer <token>

**Query Parameters:**

* ``skip`` (int): Number of posts to skip for pagination (default: 0)
* ``limit`` (int): Maximum number of posts to return (default: 20, max: 100)
* ``group_id`` (UUID): Filter posts by specific group
* ``post_type`` (string): Filter by post type (discussion, question, experience, research)
* ``author_id`` (UUID): Filter posts by specific author

**Get Specific Post**

.. code-block:: http

   GET /posts/{post_id}
   Authorization: Bearer <token>

**Note:** This endpoint automatically increments the view count for the post.

**Update Post**

.. code-block:: http

   PUT /posts/{post_id}
   Content-Type: application/json
   Authorization: Bearer <token>

   {
     "title": "Updated title with more specific information",
     "content": "Updated content with additional findings..."
   }

**Delete Post**

.. code-block:: http

   DELETE /posts/{post_id}
   Authorization: Bearer <token>

Comments API
===========

Comment Management
-----------------

**Create Comment**

.. code-block:: http

   POST /posts/{post_id}/comments
   Content-Type: application/json
   Authorization: Bearer <token>

   {
     "content": "Great post! I've had similar experiences with magnesium glycinate. What dosage did you find most effective?",
     "parent_comment_id": null
   }

**Create Reply**

.. code-block:: http

   POST /posts/{post_id}/comments
   Content-Type: application/json
   Authorization: Bearer <token>

   {
     "content": "I found 400mg before bed worked best for me.",
     "parent_comment_id": "abc123de-f456-7890-1234-567890abcdef"
   }

**Response (201 Created):**

.. code-block:: json

   {
     "id": "def456gh-i789-0123-4567-890123456789",
     "post_id": "789e1234-e89b-12d3-a456-************",
     "author_id": "456e7890-e89b-12d3-a456-************",
     "parent_comment_id": "abc123de-f456-7890-1234-567890abcdef",
     "content": "I found 400mg before bed worked best for me.",
     "like_count": 0,
     "is_deleted": false,
     "created_at": "2025-06-17T22:40:00Z",
     "updated_at": "2025-06-17T22:40:00Z"
   }

**Get Post Comments**

.. code-block:: http

   GET /posts/{post_id}/comments?skip=0&limit=20&parent_only=false
   Authorization: Bearer <token>

**Query Parameters:**

* ``parent_only`` (bool): If true, returns only top-level comments (default: false)

**Update Comment**

.. code-block:: http

   PUT /comments/{comment_id}
   Content-Type: application/json
   Authorization: Bearer <token>

   {
     "content": "Updated comment with additional information..."
   }

**Delete Comment**

.. code-block:: http

   DELETE /comments/{comment_id}
   Authorization: Bearer <token>

**Note:** Comments are soft-deleted to preserve thread structure.

Peer Review API
==============

Review Management
----------------

**Create Peer Review**

.. code-block:: http

   POST /reviews
   Content-Type: application/json
   Authorization: Bearer <token>

   {
     "content_type": "post",
     "content_id": "789e1234-e89b-12d3-a456-************",
     "score": 8,
     "feedback": "Well-researched post with good methodology. Minor suggestion: include more details about timing of measurements.",
     "expertise_level": "intermediate"
   }

**Response (201 Created):**

.. code-block:: json

   {
     "id": "abc123de-f456-7890-1234-567890abcdef",
     "content_type": "post",
     "content_id": "789e1234-e89b-12d3-a456-************",
     "reviewer_id": "456e7890-e89b-12d3-a456-************",
     "status": "pending",
     "score": 8,
     "feedback": "Well-researched post with good methodology...",
     "expertise_level": "intermediate",
     "created_at": "2025-06-17T22:40:00Z",
     "updated_at": "2025-06-17T22:40:00Z"
   }

**Get Content Reviews**

.. code-block:: http

   GET /reviews/content/{content_type}/{content_id}?skip=0&limit=20
   Authorization: Bearer <token>

**Get User Reviews**

.. code-block:: http

   GET /reviews/user/{user_id}?skip=0&limit=20
   Authorization: Bearer <token>

**Update Review**

.. code-block:: http

   PUT /reviews/{review_id}
   Content-Type: application/json
   Authorization: Bearer <token>

   {
     "status": "completed",
     "score": 9,
     "feedback": "Excellent analysis with comprehensive data and clear methodology."
   }

Notifications API
================

Notification Management
----------------------

**Get User Notifications**

.. code-block:: http

   GET /notifications?skip=0&limit=20&unread_only=false
   Authorization: Bearer <token>

**Query Parameters:**

* ``unread_only`` (bool): If true, returns only unread notifications (default: false)

**Response (200 OK):**

.. code-block:: json

   [
     {
       "id": "notif-123e4567-e89b-12d3-a456-************",
       "user_id": "456e7890-e89b-12d3-a456-************",
       "type": "follow",
       "title": "New Follower",
       "message": "John Doe started following you",
       "related_id": "789e1234-e89b-12d3-a456-************",
       "related_type": "user",
       "is_read": false,
       "is_sent": false,
       "created_at": "2025-06-17T22:45:00Z"
     }
   ]

**Mark Notification as Read**

.. code-block:: http

   PUT /notifications/{notification_id}/read
   Authorization: Bearer <token>

**Response (200 OK):**

.. code-block:: json

   {
     "message": "Notification marked as read"
   }

**Get Unread Count**

.. code-block:: http

   GET /notifications/unread-count
   Authorization: Bearer <token>

**Response (200 OK):**

.. code-block:: json

   {
     "unread_count": 5
   }

Error Handling
==============

Standard HTTP Status Codes
--------------------------

* **200 OK**: Successful GET, PUT requests
* **201 Created**: Successful POST requests
* **204 No Content**: Successful DELETE requests
* **400 Bad Request**: Invalid request data or business logic violation
* **401 Unauthorized**: Missing or invalid authentication token
* **403 Forbidden**: Insufficient permissions for the requested action
* **404 Not Found**: Requested resource does not exist
* **422 Unprocessable Entity**: Valid JSON but invalid data values
* **429 Too Many Requests**: Rate limit exceeded
* **500 Internal Server Error**: Server-side error

Error Response Format
--------------------

All error responses follow this format:

.. code-block:: json

   {
     "detail": "Human-readable error message",
     "error_code": "MACHINE_READABLE_CODE",
     "timestamp": "2025-06-17T22:45:00Z",
     "path": "/api/v1/community/posts"
   }

Common Error Codes
-----------------

* ``ALREADY_FOLLOWING``: User is already following the target user
* ``CANNOT_FOLLOW_SELF``: Attempt to follow oneself
* ``ALREADY_REVIEWED``: User has already reviewed this content
* ``INSUFFICIENT_PERMISSIONS``: User lacks permission for the action
* ``RESOURCE_NOT_FOUND``: Requested resource does not exist
* ``VALIDATION_ERROR``: Request data validation failed

Rate Limiting
=============

API rate limits are applied per user:

* **General Endpoints**: 1000 requests per hour
* **Write Operations**: 100 requests per hour
* **Search Endpoints**: 500 requests per hour

Rate limit headers are included in all responses:

.. code-block:: http

   X-RateLimit-Limit: 1000
   X-RateLimit-Remaining: 999
   X-RateLimit-Reset: 1640995200

Pagination
==========

List endpoints support pagination with these parameters:

* ``skip`` (int): Number of items to skip (default: 0)
* ``limit`` (int): Maximum items to return (default: 20, max: 100)

Pagination metadata is included in response headers:

.. code-block:: http

   X-Total-Count: 150
   X-Page-Count: 8
   X-Current-Page: 1
   X-Per-Page: 20

API Versioning
==============

The API uses URL-based versioning:

* **Current Version**: ``/api/v1/community/``
* **Version Header**: ``API-Version: 1.0``

Backward compatibility is maintained within major versions. Breaking changes will result in a new major version.

SDK and Client Libraries
========================

Official client libraries are available for:

* **Python**: ``pip install supplement-tracker-client``
* **JavaScript/Node.js**: ``npm install supplement-tracker-client``
* **cURL Examples**: Available in this documentation

Example usage with Python client:

.. code-block:: python

   from supplement_tracker import CommunityClient

   client = CommunityClient(token="your-access-token")
   
   # Follow a user
   follow = client.follow_user("123e4567-e89b-12d3-a456-************")
   
   # Create a post
   post = client.create_post(
       title="My Magnesium Experience",
       content="Detailed experience report...",
       group_id="group-uuid"
   )
   
   # Get notifications
   notifications = client.get_notifications(unread_only=True)

Legal Compliance API
===================

The compliance API provides comprehensive legal and privacy compliance features including consent management, user rights, and data protection.

**Base URL:** ``https://api.supplement-tracker.com/api/compliance``

Consent Management
-----------------

**Record Consent**

.. code-block:: http

   POST /consent
   Content-Type: application/json
   Authorization: Bearer <token>

   {
     "consent_type": "research_participation",
     "consent_version": "1.0",
     "consent_text": "I consent to participate in research studies...",
     "metadata": {
       "study_id": "supplement_effectiveness_2024"
     }
   }

**Response (200 OK):**

.. code-block:: json

   {
     "consent_id": 12345,
     "consent_type": "research_participation",
     "status": "given",
     "version": "1.0",
     "given_at": "2024-12-17T10:30:00Z",
     "expires_at": null
   }

**Withdraw Consent**

.. code-block:: http

   POST /consent/withdraw
   Content-Type: application/json
   Authorization: Bearer <token>

   {
     "consent_type": "marketing_communications",
     "withdrawal_reason": "No longer interested"
   }

**Get Consent Status**

.. code-block:: http

   GET /consent/status
   Authorization: Bearer <token>

User Rights Management
---------------------

**Submit Rights Request**

.. code-block:: http

   POST /rights/request
   Content-Type: application/json
   Authorization: Bearer <token>

   {
     "request_type": "access",
     "description": "I would like to download all my personal data",
     "request_data": {
       "format": "json",
       "include_research_data": true
     }
   }

**Export User Data**

.. code-block:: http

   POST /data/export
   Content-Type: application/json
   Authorization: Bearer <token>

   {
     "include_research_data": true,
     "include_health_data": true,
     "format": "json"
   }

**Download Data Package**

.. code-block:: http

   GET /data/package
   Authorization: Bearer <token>

Compliance Validation
--------------------

**Validate Research Consent**

.. code-block:: http

   GET /validation/research/{study_id}
   Authorization: Bearer <token>

**Response (200 OK):**

.. code-block:: json

   {
     "valid": true,
     "missing_consents": [],
     "study_id": "supplement_effectiveness_2024"
   }

For detailed compliance API documentation, see :doc:`../legal-compliance/compliance-api`.

This comprehensive API enables full integration with the Community Features and Legal Compliance systems, supporting all social connections, group management, discussions, peer reviews, notifications, and privacy protection functionality.
