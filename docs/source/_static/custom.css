/* Custom CSS for Supplement Tracker Documentation */

/* Color scheme based on supplement/health theme */
:root {
    --primary-color: #2980b9;
    --secondary-color: #27ae60;
    --accent-color: #e74c3c;
    --warning-color: #f39c12;
    --success-color: #27ae60;
    --info-color: #3498db;
    --light-bg: #ecf0f1;
    --dark-text: #2c3e50;
    --medium-text: #34495e;
    --light-text: #7f8c8d;
}

/* Enhanced code blocks */
.highlight {
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.highlight pre {
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 0 8px 8px 0;
}

/* API endpoint styling */
.highlight-http .highlight {
    border-left-color: var(--success-color);
}

.highlight-json .highlight {
    border-left-color: var(--info-color);
}

.highlight-python .highlight {
    border-left-color: var(--warning-color);
}

/* Mermaid diagram styling */
.mermaid {
    text-align: center;
    margin: 2rem 0;
    padding: 1rem;
    background: #fafbfc;
    border-radius: 8px;
    border: 1px solid #e1e4e8;
}

/* Enhanced admonitions */
.admonition {
    border-radius: 8px;
    border-left: 4px solid;
    margin: 1.5rem 0;
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.admonition.note {
    border-left-color: var(--info-color);
    background-color: #e8f4fd;
}

.admonition.warning {
    border-left-color: var(--warning-color);
    background-color: #fff3cd;
}

.admonition.tip {
    border-left-color: var(--success-color);
    background-color: #d4edda;
}

.admonition.important {
    border-left-color: var(--accent-color);
    background-color: #f8d7da;
}

/* Badge styling for features */
.badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.375rem;
    margin: 0.125rem;
}

.badge-success {
    color: #fff;
    background-color: var(--success-color);
}

.badge-info {
    color: #fff;
    background-color: var(--info-color);
}

.badge-warning {
    color: #212529;
    background-color: var(--warning-color);
}

.badge-primary {
    color: #fff;
    background-color: var(--primary-color);
}

/* Enhanced tables */
table.docutils {
    border-collapse: collapse;
    border-spacing: 0;
    width: 100%;
    margin: 1rem 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 8px;
    overflow: hidden;
}

table.docutils th {
    background-color: var(--primary-color);
    color: white;
    font-weight: 600;
    padding: 0.75rem;
    text-align: left;
}

table.docutils td {
    padding: 0.75rem;
    border-bottom: 1px solid #dee2e6;
}

table.docutils tbody tr:nth-child(even) {
    background-color: #f8f9fa;
}

table.docutils tbody tr:hover {
    background-color: #e9ecef;
}

/* API method styling */
.http-method {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-weight: bold;
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-size: 0.85rem;
}

.http-get {
    background-color: #d4edda;
    color: #155724;
}

.http-post {
    background-color: #cce5ff;
    color: #004085;
}

.http-put {
    background-color: #fff3cd;
    color: #856404;
}

.http-delete {
    background-color: #f8d7da;
    color: #721c24;
}

/* Status code styling */
.status-code {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-weight: bold;
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-size: 0.85rem;
}

.status-2xx {
    background-color: #d4edda;
    color: #155724;
}

.status-4xx {
    background-color: #fff3cd;
    color: #856404;
}

.status-5xx {
    background-color: #f8d7da;
    color: #721c24;
}

/* Feature highlight boxes */
.feature-box {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 12px;
    margin: 2rem 0;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.feature-box h3 {
    margin-top: 0;
    color: white;
}

.feature-box p {
    margin-bottom: 0;
    opacity: 0.9;
}

/* Phase completion indicators */
.phase-complete {
    background-color: var(--success-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    display: inline-block;
    margin: 0.5rem 0;
}

.phase-complete::before {
    content: "✅ ";
}

.phase-in-progress {
    background-color: var(--warning-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    display: inline-block;
    margin: 0.5rem 0;
}

.phase-in-progress::before {
    content: "🔄 ";
}

.phase-planned {
    background-color: var(--light-text);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    display: inline-block;
    margin: 0.5rem 0;
}

.phase-planned::before {
    content: "⏳ ";
}

/* Emoji enhancement */
.emoji {
    font-size: 1.2em;
    vertical-align: middle;
}

/* Navigation enhancements */
.wy-nav-content {
    max-width: 1200px;
}

/* Code copy button styling */
.highlight-copy-btn {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.2s;
}

.highlight:hover .highlight-copy-btn {
    opacity: 1;
}

/* Responsive design improvements */
@media (max-width: 768px) {
    .mermaid {
        overflow-x: auto;
    }
    
    table.docutils {
        font-size: 0.875rem;
    }
    
    .feature-box {
        padding: 1rem;
    }
}

/* Print styles */
@media print {
    .mermaid {
        break-inside: avoid;
    }
    
    .highlight {
        break-inside: avoid;
    }
    
    .admonition {
        break-inside: avoid;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .highlight pre {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .mermaid {
        background: #2d3748;
        border-color: #4a5568;
    }
    
    table.docutils tbody tr:nth-child(even) {
        background-color: #2d3748;
    }
    
    table.docutils tbody tr:hover {
        background-color: #4a5568;
    }
}

/* Accessibility improvements */
.highlight:focus-within {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.badge:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Animation for interactive elements */
.badge, .phase-complete, .phase-in-progress, .phase-planned {
    transition: transform 0.2s ease;
}

.badge:hover, .phase-complete:hover, .phase-in-progress:hover, .phase-planned:hover {
    transform: translateY(-1px);
}

/* Custom scrollbar for code blocks */
.highlight pre::-webkit-scrollbar {
    height: 8px;
}

.highlight pre::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.highlight pre::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

.highlight pre::-webkit-scrollbar-thumb:hover {
    background: #1f5f8b;
}
