# Supplement Tracker Documentation

This directory contains the complete documentation for the Supplement Tracker project, built with [Sphinx](https://www.sphinx-doc.org/).

## Quick Start

### Building the Documentation

**Using the build script (recommended):**
```bash
./build.sh html
```

**Using Nix:**
```bash
nix-shell -p python311Packages.sphinx --run "sphinx-build -b html . _build/html"
```

**Using Make:**
```bash
make html
```

**Using system Sphinx:**
```bash
pip install -r requirements.txt
sphinx-build -b html . _build/html
```

### Opening the Documentation

After building, open `_build/html/index.html` in your browser, or use:
```bash
make open
```

## Documentation Structure

```
docs/
├── index.rst                    # Main documentation index
├── user-guide/                  # User-facing documentation
│   ├── installation.rst         # Installation and setup
│   ├── configuration.rst        # Configuration guide
│   ├── authentication.rst       # Authentication guide
│   ├── api-usage.rst            # API usage examples
│   ├── supplement-tracking.rst  # Supplement tracking guide
│   ├── community-features.rst   # Community features (planned)
│   └── troubleshooting.rst      # Troubleshooting guide
├── api/                         # API reference documentation
│   ├── index.rst               # API overview
│   ├── authentication.rst      # Authentication endpoints
│   ├── users.rst               # User management endpoints
│   ├── supplements.rst         # Supplement endpoints
│   ├── community.rst           # Community endpoints (planned)
│   ├── research.rst            # Research endpoints (planned)
│   ├── schemas.rst             # Data schemas and validation
│   └── errors.rst              # Error handling and codes
├── architecture/               # Technical architecture
│   ├── overview.rst            # System overview
│   ├── database-design.rst     # Database schema and design
│   ├── api-design.rst          # API design principles
│   ├── security.rst            # Security architecture
│   ├── deployment.rst          # Deployment strategies
│   ├── monitoring.rst          # Monitoring and observability
│   └── system-design.rst       # Detailed system design
└── developer/                  # Developer guides
    ├── setup.rst               # Development environment setup
    └── contributing.rst        # Contributing guidelines
```

## Build Options

### Standard Builds

- `html` - HTML documentation (default)
- `singlehtml` - Single-page HTML
- `latex` - LaTeX output
- `pdf` - PDF output (requires LaTeX)
- `epub` - EPUB e-book format

### Development Builds

- `make dev` - Build and open in browser
- `make strict` - Build with warnings as errors
- `make linkcheck` - Check for broken links
- `make clean` - Clean build directory

### Using the Build Script

```bash
# Standard build
./build.sh html

# Strict build (warnings as errors)
./build.sh html true

# Other formats
./build.sh latex
./build.sh epub
```

## Development Workflow

1. **Edit documentation files** (`.rst` files)
2. **Build locally** to test changes:
   ```bash
   ./build.sh html
   ```
3. **Check for warnings** and fix them:
   ```bash
   ./build.sh html true
   ```
4. **Check links** (optional):
   ```bash
   make linkcheck
   ```
5. **Commit changes** when satisfied

## Writing Guidelines

### reStructuredText (RST) Syntax

- Use `.rst` files for all documentation
- Follow [reStructuredText syntax](https://www.sphinx-doc.org/en/master/usage/restructuredtext/basics.html)
- Use consistent heading levels:
  ```rst
  ===============================================
  Document Title (H1)
  ===============================================
  
  Section Title (H2)
  ==================
  
  Subsection Title (H3)
  ---------------------
  
  Sub-subsection Title (H4)
  ~~~~~~~~~~~~~~~~~~~~~~~~~
  ```

### Code Examples

Use appropriate syntax highlighting:

```rst
.. code-block:: python

    def example_function():
        return "Hello, World!"

.. code-block:: bash

    curl -X GET "https://api.example.com/endpoint"

.. code-block:: json

    {
        "key": "value"
    }
```

### Cross-References

Link to other documents:
```rst
:doc:`installation`
:doc:`../api/authentication`
:doc:`/user-guide/configuration`
```

### Tables

Use list-table for complex tables:
```rst
.. list-table::
   :header-rows: 1
   :widths: 20 80

   * - Parameter
     - Description
   * - name
     - The name of the item
```

### Admonitions

Use admonitions for important information:
```rst
.. note::
   This is a note.

.. warning::
   This is a warning.

.. important::
   This is important information.
```

## Configuration

The documentation is configured in `conf.py`. Key settings:

- **Project information**: Name, version, author
- **Extensions**: Sphinx extensions used
- **Theme**: HTML theme and styling
- **Intersphinx**: Links to external documentation

## Troubleshooting

### Common Issues

**Build fails with "command not found":**
- Install Sphinx: `pip install sphinx`
- Or use Nix: `nix-shell -p python311Packages.sphinx`

**Warnings about missing documents:**
- Check that all referenced documents exist
- Verify file paths in `:doc:` references

**CSS not loading:**
- Ensure `_static/custom.css` exists
- Check `html_css_files` in `conf.py`

**Links not working:**
- Run `make linkcheck` to find broken links
- Update or remove broken references

### Getting Help

- [Sphinx Documentation](https://www.sphinx-doc.org/)
- [reStructuredText Guide](https://docutils.sourceforge.io/rst.html)
- [Project Issues](https://github.com/forkrul/day2-supplement-tracker/issues)

## Contributing

See :doc:`developer/contributing` for guidelines on contributing to the documentation.

Key points:
- Test builds locally before submitting
- Follow the writing guidelines
- Update relevant sections when adding features
- Include code examples where helpful
- Check for broken links and references
# Supplement Tracker Community Platform Documentation

This directory contains the comprehensive Sphinx documentation for the **Supplement Tracker Community Platform**, focusing on the newly implemented **Phase 2 Community Features**.

## 📚 Documentation Overview

The documentation covers:

- **Community Features Overview**: Architecture and feature introduction
- **Social Connections**: User following and networking system
- **Discussion Forums**: Community groups and threaded discussions
- **Peer Review System**: Content validation and quality assurance
- **Real-time Notifications**: Engagement and activity updates
- **Complete API Reference**: All endpoints with examples

## 🚀 Quick Start

### Prerequisites

```bash
# Install documentation dependencies
pip install -r requirements.txt
```

### Building Documentation

```bash
# Build HTML documentation
make html

# Build and serve locally
make serve

# Live reload during development
make livehtml

# Build all formats (HTML, PDF, EPUB)
make all
```

### Viewing Documentation

After building, open `build/html/index.html` in your browser, or use:

```bash
# Serve on http://localhost:8080
make serve
```

## 📖 Documentation Structure

```
docs/
├── source/
│   ├── community/           # Community features documentation
│   │   ├── overview.rst     # Architecture and feature overview
│   │   ├── social-connections.rst
│   │   ├── discussion-forums.rst
│   │   ├── peer-review.rst
│   │   ├── notifications.rst
│   │   └── api-endpoints.rst
│   ├── user-guide/         # User guides and tutorials
│   │   └── getting-started.rst
│   ├── _static/            # Custom CSS and assets
│   │   └── custom.css      # Enhanced styling
│   ├── conf.py             # Sphinx configuration
│   └── index.rst           # Main documentation index
├── build/                  # Generated documentation
├── Makefile               # Build commands
├── requirements.txt       # Documentation dependencies
└── README.md             # This file
```

## ✨ Features

### Rich Visual Documentation
- **15+ Mermaid diagrams** showing system architecture and user flows
- **Interactive API examples** with request/response samples
- **Custom CSS styling** for enhanced readability
- **Responsive design** with mobile support
- **Dark mode compatibility**

### Comprehensive Content
- **Complete API reference** with all endpoints documented
- **Step-by-step user guides** for all community features
- **Architecture diagrams** showing data flow and relationships
- **Best practices** and troubleshooting guides
- **Code examples** in multiple languages

### Developer Experience
- **Live reload** development server
- **Multiple output formats** (HTML, PDF, EPUB)
- **Link checking** and spell checking
- **Search functionality** with full-text search
- **Cross-references** between sections

## 🎯 Key Documentation Sections

### Community Features Overview
Comprehensive introduction to Phase 2 features with architecture diagrams and feature explanations.

### Social Connections
Complete guide to the user following system, including:
- Following/unfollowing users
- Building networks and discovering content
- Privacy controls and analytics
- API usage examples

### Discussion Forums
Detailed documentation of community groups and discussions:
- Creating and managing groups
- Post types and content guidelines
- Threaded comment system
- Moderation and quality control

### Peer Review System
In-depth coverage of content validation:
- Review process and criteria
- Reviewer qualifications and recognition
- Scoring system and aggregation
- Quality assurance measures

### Real-time Notifications
Complete notification system documentation:
- Notification types and delivery channels
- Preference management and customization
- Smart filtering and analytics
- API integration examples

### API Reference
Comprehensive API documentation with:
- All endpoints with examples
- Request/response schemas
- Error handling and status codes
- Rate limiting and pagination
- Authentication requirements

## 🛠 Development Commands

```bash
# Clean build directory
make clean

# Build with strict error checking
make strict

# Check for broken links
make linkcheck

# Check spelling
make spelling

# Build PDF documentation
make pdf

# Build EPUB documentation
make epub

# Development build (clean + html)
make dev
```

## 📊 Documentation Metrics

- **8 main documentation files**
- **15+ Mermaid diagrams**
- **50+ code examples**
- **Complete API coverage** for all Phase 2 endpoints
- **Mobile-responsive design**
- **Accessibility compliant**

## 🎨 Custom Styling

The documentation includes custom CSS with:
- **Enhanced code blocks** with syntax highlighting
- **API method styling** (GET, POST, PUT, DELETE)
- **Status code indicators** (2xx, 4xx, 5xx)
- **Feature highlight boxes**
- **Phase completion indicators**
- **Responsive design** for all screen sizes

## 📱 Responsive Design

The documentation is fully responsive and works on:
- **Desktop browsers** (Chrome, Firefox, Safari, Edge)
- **Tablet devices** (iPad, Android tablets)
- **Mobile phones** (iOS, Android)
- **Print media** (optimized for printing)

## 🔍 Search and Navigation

- **Full-text search** across all documentation
- **Hierarchical navigation** with collapsible sections
- **Cross-references** between related sections
- **Table of contents** for easy navigation
- **Breadcrumb navigation** showing current location

## 🚀 Deployment

The documentation can be deployed to:
- **GitHub Pages** (automatic deployment from repository)
- **Read the Docs** (automatic builds from Git)
- **Netlify** or **Vercel** (static site hosting)
- **Custom web servers** (static HTML files)

### GitHub Pages Deployment

```bash
# Build documentation
make html

# Deploy to GitHub Pages (if configured)
git add docs/build/html/
git commit -m "Update documentation"
git push origin master
```

## 📈 Analytics and Monitoring

The documentation includes:
- **Google Analytics** integration (configurable)
- **Performance monitoring** with Core Web Vitals
- **User behavior tracking** (optional)
- **Search analytics** for content optimization

## 🤝 Contributing

To contribute to the documentation:

1. **Edit source files** in `docs/source/`
2. **Build locally** to test changes: `make html`
3. **Check for errors** with: `make strict`
4. **Verify links** with: `make linkcheck`
5. **Submit pull request** with changes

## 📄 License

This documentation is part of the Supplement Tracker Community Platform and is licensed under the MIT License.

## 🆘 Support

For documentation issues:
- **GitHub Issues**: Report bugs or request improvements
- **Discussions**: Ask questions or suggest enhancements
- **Email**: Contact the development team

---

**Built with ❤️ using Sphinx, Mermaid, and modern web technologies.**
